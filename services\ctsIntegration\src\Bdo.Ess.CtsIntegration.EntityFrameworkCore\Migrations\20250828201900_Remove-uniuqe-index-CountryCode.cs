﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bdo.Ess.CtsIntegration.Migrations
{
    /// <inheritdoc />
    public partial class RemoveuniuqeindexCountryCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CountryCertificates_CountryCode",
                table: "CountryCertificates");

            migrationBuilder.CreateIndex(
                name: "IX_CountryCertificates_CountryCode",
                table: "CountryCertificates",
                column: "CountryCode");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CountryCertificates_CountryCode",
                table: "CountryCertificates");

            migrationBuilder.CreateIndex(
                name: "IX_CountryCertificates_CountryCode",
                table: "CountryCertificates",
                column: "CountryCode",
                unique: true);
        }
    }
}
