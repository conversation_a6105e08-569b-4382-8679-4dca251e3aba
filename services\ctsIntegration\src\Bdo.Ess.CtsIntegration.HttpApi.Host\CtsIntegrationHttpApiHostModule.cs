using Azure.Core;
using Azure.Identity;
using Bdo.Ess.CtsIntegration.DbMigrations;
using Bdo.Ess.CtsIntegration.EntityFrameworkCore;
using Bdo.Ess.IdentityService;
using Bdo.Ess.IdentityService.EntityFrameworkCore;
using Bdo.Ess.Shared.Constants.Encryption;
using Bdo.Ess.Shared.Hosting.AspNetCore;
using Bdo.Ess.Shared.Hosting.Microservices;
using Bdo.Ess.Shared.HttpApi;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Prometheus;
using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Http.Client;
using Volo.Abp.Http.Client.IdentityModel;
using Volo.Abp.Modularity;
using Volo.Abp.Security.Claims;
using Volo.Abp.Timing;

namespace Bdo.Ess.CtsIntegration;

[DependsOn(
	typeof(AbpHttpClientIdentityModelModule),
	typeof(EssSharedHostingMicroservicesModule),
	typeof(ESSSharedHttpApiModule),
	typeof(CtsIntegrationApplicationModule),
	typeof(CtsIntegrationHttpApiModule),
    typeof(CtsIntegrationEntityFrameworkCoreModule),
    typeof(IdentityServiceHttpApiClientModule),
    typeof(IdentityServiceApplicationContractsModule)
	)]
public class CtsIntegrationHttpApiHostModule : AbpModule
{
	public override void PreConfigureServices(ServiceConfigurationContext context)
	{
		PreConfigure<AbpHttpClientBuilderOptions>(options =>
		{
			options.ProxyClientActions.Add((remoteServiceName, clientBuilder, client) =>
			{
				client.Timeout = TimeSpan.FromMinutes(55);
			});
		});
	}
	public override void ConfigureServices(ServiceConfigurationContext context)
	{
		var configuration = context.Services.GetConfiguration();
		var env = context.Services.GetHostingEnvironment();

		//You can disable this setting in production to avoid any potential security risks.
		Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;

		JwtBearerConfigurationHelper.Configure(context, "CtsIntegrationService");

		SwaggerConfigurationHelper.ConfigureWithOidc(
			context: context,
			authority: configuration["AuthServer:Authority"]!,
			scopes: new[] { "CtsIntegrationService" },
			flows: new[] { "authorization_code" },
			discoveryEndpoint: configuration["AuthServer:MetadataAddress"],
			apiTitle: "CtsIntegrationService API"
		);
		context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
		{
			options.IsDynamicClaimsEnabled = false;
		});
		context.Services.AddCors(options =>
		{
			options.AddDefaultPolicy(builder =>
			{
				builder
					.WithOrigins(
						configuration["App:CorsOrigins"]?
							.Split(",", StringSplitOptions.RemoveEmptyEntries)
							.Select(o => o.Trim().RemovePostFix("/"))
							.ToArray()
					)
					.WithAbpExposedHeaders()
					.SetIsOriginAllowedToAllowWildcardSubdomains()
					.AllowAnyHeader()
					.AllowAnyMethod()
					.AllowCredentials();
			});
		});
        if (!int.TryParse(configuration["Cts:Api:TimeoutSeconds"], out int httpTimeout))
        {
            httpTimeout = 300;
        }
		context.Services.AddHttpClient("CtsClient", client =>
        {
            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Add("User-Agent", "ESS-CtsIntegration/1.0");
            client.Timeout = TimeSpan.FromSeconds(httpTimeout);
        })
		.ConfigurePrimaryHttpMessageHandler(() => new SocketsHttpHandler()
		{
			//CTS API get access token take longer time, there is log create connection timeout at 1 minute
			ConnectTimeout = TimeSpan.FromSeconds(180),
			PooledConnectionIdleTimeout = TimeSpan.FromMinutes(5),
			PooledConnectionLifetime = TimeSpan.FromMinutes(20)
		});

        Configure<AbpClockOptions>(options =>
        {
            options.Kind = DateTimeKind.Utc;
        });
       
        EncryptionConfigurationHelper.ConfigureDataProtection(context, configuration);

    }

	public override void OnApplicationInitialization(ApplicationInitializationContext context)
	{
		var app = context.GetApplicationBuilder();
		var env = context.GetEnvironment();

		if (env.IsDevelopment())
		{
			app.UseDeveloperExceptionPage();
		}

		app.UseCorrelationId();
		app.UseAbpRequestLocalization();
		app.UseStaticFiles();
		app.UseRouting();
		app.UseAbpSecurityHeaders();
		app.UseCors();
		app.UseAuthentication();
		app.UseAbpClaimsMap();
		app.UseMultiTenancy();
		app.UseUnitOfWork();
		//app.UseDynamicClaims();
		app.UseAuthorization(); 
        app.UseSwagger();
		app.UseAbpSwaggerUI(options =>
		{
			var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();
			options.SwaggerEndpoint("/swagger/v1/swagger.json", "CtsIntegrationService API");
			options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
		});

		app.UseAbpSerilogEnrichers();
		app.UseAuditing();
		app.UseConfiguredEndpoints(endpoints => endpoints.MapMetrics());

	}

	public async override Task OnPostApplicationInitializationAsync(ApplicationInitializationContext context)
	{
		using (var scope = context.ServiceProvider.CreateScope())
		{
			await scope.ServiceProvider
				.GetRequiredService<CtsIntegrationDatabaseMigrationChecker>()
				.CheckAndApplyDatabaseMigrationsAsync();
		}
	}
}
