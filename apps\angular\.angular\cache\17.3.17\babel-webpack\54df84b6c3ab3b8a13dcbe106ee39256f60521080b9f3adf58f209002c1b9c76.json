{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@app/shared/services/sweetalert.service\";\nimport * as i4 from \"@angular/material/input\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/cdk/text-field\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@ngx-validate/core\";\nexport let RegeneratePacketDialogComponent = /*#__PURE__*/(() => {\n  class RegeneratePacketDialogComponent {\n    constructor(dialogRef, data, fb, sweetAlert) {\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.fb = fb;\n      this.sweetAlert = sweetAlert;\n      this.form = this.fb.group({\n        comment: [null, [Validators.required, Validators.maxLength(512)]]\n      });\n    }\n    onSubmit() {\n      if (this.form.invalid) {\n        this.form.get('comment')?.markAsTouched();\n        return;\n      }\n      this.dialogRef.close({\n        comment: this.form.value.comment\n      });\n    }\n    onCancel() {\n      if (this.form.dirty) {\n        this.sweetAlert.fireDialog({\n          action: \"delete\",\n          title: \"Are you sure you want to close?\",\n          text: \"Any unsaved changes may be lost\",\n          type: \"confirm\"\n        }, confirm => {\n          if (confirm) {\n            this.dialogRef.close();\n          }\n        });\n      } else {\n        this.dialogRef.close();\n      }\n    }\n    static {\n      this.ɵfac = function RegeneratePacketDialogComponent_Factory(t) {\n        return new (t || RegeneratePacketDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.SweetAlertService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RegeneratePacketDialogComponent,\n        selectors: [[\"app-regenerate-packet-dialog\"]],\n        decls: 18,\n        vars: 2,\n        consts: [[\"mat-dialog-title\", \"\"], [1, \"row\"], [1, \"col-8\", \"title\"], [1, \"col-4\", \"text-end\", \"modal-action-button\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"fill\", 1, \"w-100\"], [\"matInput\", \"\", \"formControlName\", \"comment\", \"rows\", \"3\", \"placeholder\", \"Your Comment\", \"cdkTextareaAutosize\", \"\", \"cdkAutosizeMinRows\", \"3\", \"maxlength\", \"500\"], [\"align\", \"end\", 1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"type\", \"submit\", 1, \"ui-button\", 3, \"disabled\"]],\n        template: function RegeneratePacketDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵtext(3, \"Regenerate Packet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function RegeneratePacketDialogComponent_Template_button_click_5_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(7, \"form\", 6);\n            i0.ɵɵlistener(\"ngSubmit\", function RegeneratePacketDialogComponent_Template_form_ngSubmit_7_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(8, \"mat-dialog-content\")(9, \"mat-form-field\", 7)(10, \"mat-label\");\n            i0.ɵɵtext(11, \"Comment (Maximum Length Allowed: 500 characters)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(12, \"textarea\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"mat-dialog-actions\", 9)(14, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function RegeneratePacketDialogComponent_Template_button_click_14_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(15, \"Cancel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"button\", 11);\n            i0.ɵɵtext(17, \"Regenerate\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"formGroup\", ctx.form);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"disabled\", ctx.form.invalid);\n          }\n        },\n        dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MaxLengthValidator, i2.FormGroupDirective, i2.FormControlName, i4.MatInput, i5.MatFormField, i5.MatLabel, i6.CdkTextareaAutosize, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i7.MatButton, i8.ValidationGroupDirective, i8.ValidationDirective],\n        styles: [\".title[_ngcontent-%COMP%]{font-size:1.3em;color:#00779b;display:block}.modal-action-button[_ngcontent-%COMP%]{font-size:1em}.w-100[_ngcontent-%COMP%]{width:100%}.action-buttons[_ngcontent-%COMP%]{margin-top:16px;display:flex}.required[_ngcontent-%COMP%]{color:red}\"]\n      });\n    }\n  }\n  return RegeneratePacketDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}