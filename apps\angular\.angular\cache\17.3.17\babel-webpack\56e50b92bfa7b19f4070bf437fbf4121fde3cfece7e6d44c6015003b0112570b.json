{"ast": null, "code": "import { BdoTableColumnType, BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\nimport { UploadedFileStatus } from '../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declaration-imports';\nimport { AppComponentBase } from '../../../../app-component-base';\nimport { saveAs } from 'file-saver';\nimport { finalize, interval, switchMap, takeWhile } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/imports\";\nimport * as i3 from \"../../../../shared/services/upload-file.service\";\nimport * as i4 from \"../../../../shared/services/notification-signalr-service\";\nimport * as i5 from \"@abp/ng.core\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/divider\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"../../../../shared/components/bdo-table/bdo-table.component\";\nimport * as i13 from \"@angular/common\";\nconst _c0 = [\"uploadFile\"];\nconst _c1 = () => [10, 20, 50, 100, 200];\nfunction DeclarationImportComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 29)(1, \"a\", 30);\n    i0.ɵɵlistener(\"click\", function DeclarationImportComponent_span_8_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.routeToImportedDataPage());\n    });\n    i0.ɵɵtext(2, \" IMPORTED DATA\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeclarationImportComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\")(2, \"input\", 31, 1);\n    i0.ɵɵlistener(\"change\", function DeclarationImportComponent_div_35_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSelectFile($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\")(5, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function DeclarationImportComponent_div_35_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.uploadFileClick());\n    });\n    i0.ɵɵtext(6, \"Upload\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedFile == null || ctx_r2.hasActiveFile || ctx_r2.uploading);\n  }\n}\nfunction DeclarationImportComponent_div_41_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r5, \" \");\n  }\n}\nfunction DeclarationImportComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"ul\");\n    i0.ɵɵtemplate(2, DeclarationImportComponent_div_41_li_2_Template, 2, 1, \"li\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.items);\n  }\n}\nexport let DeclarationImportComponent = /*#__PURE__*/(() => {\n  class DeclarationImportComponent extends AppComponentBase {\n    ngOnInit() {\n      this.subscribeToEvents();\n      //this.setTableData();\n      this.onLazyLoadEvent(undefined);\n    }\n    ngOnDestroy() {\n      if (this.notificationSubscription) this.notificationSubscription.unsubscribe();\n      console.log(\"declaration import page closed. close notification connection\");\n      this.notificationSignalRService.closeConnection();\n    }\n    constructor(injector, router, declarationImportFileService, fileUploadService, notificationSignalRService, permissionService) {\n      super(injector);\n      this.router = router;\n      this.declarationImportFileService = declarationImportFileService;\n      this.fileUploadService = fileUploadService;\n      this.notificationSignalRService = notificationSignalRService;\n      this.permissionService = permissionService;\n      this.TableId = 'import-results';\n      this.currentPageIndex = 0;\n      this.stopPollingByFile = false;\n      this.stopPolling = false;\n      this.importResultColumns = [{\n        columnId: \"uploadedDateTime\" /* ESSImportTableColumns.DATE */,\n        type: BdoTableColumnType.Date,\n        minWidth: 100,\n        frozenLeft: true,\n        isSortable: true,\n        columnName: 'Date'\n      }, {\n        columnId: \"fileName\" /* ESSImportTableColumns.FILE_NAME */,\n        type: BdoTableColumnType.Link,\n        minWidth: 150,\n        isSortable: true,\n        columnName: 'Name'\n      }, {\n        columnId: \"submitterName\" /* ESSImportTableColumns.SUBMITTED_BY */,\n        type: BdoTableColumnType.String,\n        minWidth: 60,\n        isSortable: true,\n        columnName: 'Submitted By'\n      }, {\n        columnId: \"statusName\" /* ESSImportTableColumns.STATUS */,\n        type: BdoTableColumnType.String,\n        minWidth: 60,\n        isSortable: true,\n        columnName: 'Status'\n      }, {\n        columnId: \"numberOfDeclarations\" /* ESSImportTableColumns.DECLERATIONS_NO */,\n        type: BdoTableColumnType.Number,\n        minWidth: 60,\n        isSortable: true,\n        columnName: '# Declarations'\n      }, {\n        columnId: \"numberOfWithErrorDeclarations\" /* ESSImportTableColumns.DECLERATIONS_WITHERROR */,\n        type: BdoTableColumnType.String,\n        minWidth: 60,\n        isSortable: true,\n        columnName: '# Declarations with errors'\n      }, {\n        columnId: \"numberOfToTriageDeclarations\" /* ESSImportTableColumns.DECLERATIONS_TO_TRIAGE */,\n        type: BdoTableColumnType.String,\n        minWidth: 60,\n        isSortable: true,\n        columnName: '# Declarations To Triage'\n      }];\n      this.PageSize = 10;\n      this.selectedFile = null;\n      this.templateData = [];\n      this.displayError = false;\n      this.UploadStatus = '';\n      this.input = {\n        maxResultCount: 10,\n        skipCount: 0,\n        sorting: \"UploadedDateTime desc\"\n      };\n      this.uploadedDate = undefined;\n      this.importResultRecords = [];\n      this.totalRecords = 0;\n      this.items = [];\n      this.fileErrorStatus = [UploadedFileStatus.FileErrors, UploadedFileStatus.Rejected];\n      this.hasActiveFile = false;\n      this.actionRequiredMessage = '';\n      this.errorFileId = \"\";\n      this.uploading = false;\n    }\n    canUploadFile() {\n      return this.permissionService.getGrantedPolicy(\"EsService.DeclarationImportFile.Import\" /* Permissions.DECLARATION_IMPORT_FILE */);\n    }\n    onLazyLoadEvent(event) {\n      if (event) {\n        if (this.PageSize === (event.pageSize ?? 10)) {\n          this.currentPageIndex = event.pageNumber ?? 0;\n        } else {\n          this.PageSize = event.pageSize ?? 10;\n          this.currentPageIndex = 0;\n        }\n        this.input.skipCount = (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\n        this.input.maxResultCount = this.PageSize ?? 10;\n        if (event.isAscending === false) {\n          this.input.sorting = `${event.sortField} desc`;\n        } else {\n          this.input.sorting = `${event.sortField} asc`;\n        }\n      } else {\n        this.input.uploadedDateTime = undefined;\n        this.input.sorting = 'UploadedDateTime desc';\n        this.currentPageIndex = 0;\n        this.PageSize = 10;\n        this.input.skipCount = 0;\n        this.input.maxResultCount = this.PageSize;\n      }\n      this.declarationImportFileService.getFileListByInput(this.input).subscribe(response => {\n        this.totalRecords = response.totalCount;\n        this.importResultRecords = response.items;\n        setTimeout(() => {\n          this.setTableData();\n        }, 200);\n      });\n      this.declarationImportFileService.hasFileInTriage().subscribe(response => {\n        this.hasActiveFile = response;\n      });\n      this.declarationImportFileService.getFileInTriage().subscribe(response => {\n        if (response != null) {\n          this.activeFileDetail = response;\n          this.actionRequiredMessage = `${this.activeFileDetail.fileName} ready for triage`;\n        } else {\n          this.activeFileDetail = null;\n          this.actionRequiredMessage = '';\n        }\n      });\n    }\n    setTableData() {\n      const tableData = new BdoTableData();\n      tableData.resetToFirstPage = false;\n      tableData.tableId = this.TableId;\n      tableData.totalRecords = this.totalRecords;\n      tableData.data = this.importResultRecords.map(x => {\n        return {\n          id: x.id,\n          rawData: x,\n          cells: [{\n            columnId: \"uploadedDateTime\" /* ESSImportTableColumns.DATE */,\n            value: x.uploadedDateTime\n          }, {\n            columnId: \"fileName\" /* ESSImportTableColumns.FILE_NAME */,\n            value: x.fileName\n          }, {\n            columnId: \"submitterName\" /* ESSImportTableColumns.SUBMITTED_BY */,\n            value: x.submitterName\n          }, {\n            columnId: \"statusName\" /* ESSImportTableColumns.STATUS */,\n            value: x.statusName\n          }, {\n            columnId: \"numberOfDeclarations\" /* ESSImportTableColumns.DECLERATIONS_NO */,\n            value: x.status === UploadedFileStatus.FileErrors ? null : x.numberOfDeclarations\n          }, {\n            columnId: \"numberOfWithErrorDeclarations\" /* ESSImportTableColumns.DECLERATIONS_WITHERROR */,\n            value: x.status === UploadedFileStatus.FileErrors ? null : x.numberOfWithErrorDeclarations\n          }, {\n            columnId: \"numberOfToTriageDeclarations\" /* ESSImportTableColumns.DECLERATIONS_TO_TRIAGE */,\n            value: x.status === UploadedFileStatus.FileErrors ? null : x.numberOfToTriageDeclarations\n          }]\n        };\n      });\n      setTimeout(() => {\n        this.tableService.setGridData(tableData);\n      }, 10);\n    }\n    getFormattedDate(date) {\n      if (!date) return undefined;\n      return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();\n    }\n    uploadFileClick() {\n      this.uploading = true;\n      console.log(\"Upload File button clicked. Start connect Notifcation Hub\");\n      this.notificationSignalRService.startConnection().then(() => {\n        //\n        // When signalR connected, start file import process.\n        //\n        console.log(\"Notification connection is connected. Start file import\");\n        const formData = new FormData();\n        formData.append(\"fileName\", this.selectedFile.name);\n        formData.append(\"file\", this.selectedFile);\n        formData.append(\"fileType\", this.selectedFile.type);\n        this.pollingUploadProgress();\n        this.fileUploadService.uploadDeclarationImportExcelByFile(formData).pipe(finalize(() => {\n          this.uploading = false;\n          this.stopPolling = true;\n        })).subscribe({\n          next: response => {\n            console.log(\"File Import is completed succssfully\", response.statusName);\n            this.onLazyLoadEvent(undefined);\n            this.UploadStatus = response.statusName;\n            this.selectedFile = null;\n            this.fileInput.nativeElement.value = '';\n            if (this.fileErrorStatus.includes(response.statusId) || response.statusId === UploadedFileStatus.ReadyForTriage) {\n              if (!response.validationResult.isValid) {\n                this.displayError = true;\n                this.items = response.validationResult.errors.map(x => x.errorMessage);\n              }\n            } else {\n              this.displayError = false;\n            }\n          },\n          error: err => {\n            if (err.status === 504) {\n              console.log(\"Gateway time out error, please refresh screen\");\n              this.pollingUploadProgressByFile();\n            }\n            this.displayError = true;\n            this.items = [];\n            this.items.push(\"System internal error, please try again\");\n          }\n        });\n      }).catch(err => {\n        //This caused by signal R error\n        console.log(\"Notification Connection failed\", err);\n        //If signal R auto-reconnect, line below should be commented out?\n        this.notificationSignalRService.closeConnection();\n      });\n    }\n    onSelectFile(event) {\n      this.selectedFile = event.target.files[0];\n    }\n    onLinkClick(event) {\n      this.items = [];\n      const data = event.rawData;\n      if (data.status === UploadedFileStatus.FileErrors) {\n        this.declarationImportFileService.getFileErrorsByFileId(data.id).subscribe(response => {\n          this.displayError = true;\n          this.items = response.errors.map(x => x.errorMessage);\n        });\n      } else {\n        this.router.navigate(['/es-import/importdetail'], {\n          queryParams: {\n            id: data.id\n          }\n        });\n      }\n    }\n    rowClicked(event) {\n      this.items = [];\n      this.errorFileId = \"\";\n      this.selectedRowStatusId = null;\n      const data = event.rowData.rawData;\n      if (data.status === UploadedFileStatus.FileErrors) {\n        this.declarationImportFileService.getFileErrorsByFileId(data.id).subscribe(response => {\n          this.errorFileId = data.id;\n          this.selectedRowStatusId = data.status;\n          this.displayError = true;\n          this.items = response.errors.map(x => x.errorMessage);\n        });\n      } else if (data.status === UploadedFileStatus.ReadyForTriage && data.numberOfWithErrorDeclarations > 0) {\n        this.declarationImportFileService.getDataErrorsByFileId(data.id).subscribe(response => {\n          this.errorFileId = data.id;\n          this.selectedRowStatusId = data.status;\n          this.displayError = true;\n          if (!response.isValid) {\n            this.items = response.errors.map(x => x.errorMessage);\n          }\n        });\n      } else {\n        this.errorFileId = \"\";\n        this.displayError = false;\n      }\n    }\n    applyClick() {\n      this.input.uploadedDateTime = this.getFormattedDate(this.uploadedDate);\n      this.declarationImportFileService.getFileListByInput(this.input).subscribe(response => {\n        this.totalRecords = response.totalCount;\n        this.importResultRecords = response.items;\n        setTimeout(() => {\n          this.setTableData();\n        }, 200);\n      });\n    }\n    resetSearchClick() {\n      this.uploadedDate = undefined;\n      this.onLazyLoadEvent(undefined);\n    }\n    routeToImportedDataPage() {\n      if (this.activeFileDetail) this.router.navigate(['/es-import/importdetail'], {\n        queryParams: {\n          id: this.activeFileDetail.id\n        }\n      });\n    }\n    downloadFileErrors() {\n      if (this.errorFileId !== '') {\n        if (this.selectedRowStatusId === UploadedFileStatus.FileErrors) {\n          this.declarationImportFileService.downloadFileErrors(this.errorFileId).subscribe(result => {\n            const decodedData = atob(result.toString());\n            const blob = new Blob([decodedData], {\n              type: 'text/csv'\n            });\n            saveAs(blob, `FileErrors_${this.errorFileId}.csv`);\n          });\n        } else if (this.selectedRowStatusId === UploadedFileStatus.ReadyForTriage) {\n          this.declarationImportFileService.downloadDataErrors(this.errorFileId).subscribe(result => {\n            const decodedData = atob(result.toString());\n            const blob = new Blob([decodedData], {\n              type: 'text/csv'\n            });\n            saveAs(blob, `DataErrors_${this.errorFileId}.csv`);\n          });\n        }\n      }\n    }\n    subscribeToEvents() {\n      this.notificationSubscription = this.notificationSignalRService.messageReceived.subscribe(message => {\n        if (message) {\n          this.UploadStatus = message;\n        }\n      });\n    }\n    pollingUploadProgress() {\n      const apiCallInterval$ = interval(2000);\n      apiCallInterval$.pipe(switchMap(() => this.declarationImportFileService.getUploadProgress()), takeWhile(data => !this.stopPolling && data.statusName !== 'Failed to Import' && data.statusName !== 'Ready For Triage' && data.statusName !== 'Has File Errors')).subscribe({\n        next: response => {\n          //console.log(response);\n          this.UploadStatus = response.message;\n        },\n        error: err => {\n          this.stopPolling = true;\n          console.log(\"Error occured when polling file import progress (event bus message)\", err);\n        }\n      });\n    }\n    pollingUploadProgressByFile() {\n      const apiCallInterval$ = interval(2000);\n      // Use switchMap to trigger an API call at each interval\n      apiCallInterval$.pipe(switchMap(() => this.declarationImportFileService.getLastImportedFile()), takeWhile(data => !this.stopPollingByFile && data.statusName !== 'Failed to Import' && data.statusName !== 'Ready For Triage' && data.statusName !== 'Has File Errors')).subscribe({\n        next: response => {\n          this.displayUploadStatusByFile(response);\n        },\n        error: err => {\n          this.stopPollingByFile = true;\n          console.log(\"Error occured when polling file import progress\", err);\n        }\n      });\n    }\n    displayUploadStatusByFile(data) {\n      this.onLazyLoadEvent(undefined);\n      this.UploadStatus = data.statusName;\n      this.selectedFile = null;\n      this.fileInput.nativeElement.value = '';\n      this.items = [];\n      if (data.status === UploadedFileStatus.FileErrors) {\n        this.declarationImportFileService.getFileErrorsByFileId(data.id).subscribe(response => {\n          this.displayError = true;\n          this.items = response.errors.map(x => x.errorMessage);\n        });\n      } else {\n        this.displayError = false;\n      }\n    }\n    static {\n      this.ɵfac = function DeclarationImportComponent_Factory(t) {\n        return new (t || DeclarationImportComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.DeclarationImportFileService), i0.ɵɵdirectiveInject(i3.FileUploadService), i0.ɵɵdirectiveInject(i4.NotificationSignalRService), i0.ɵɵdirectiveInject(i5.PermissionService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DeclarationImportComponent,\n        selectors: [[\"app-declaration-import\"]],\n        viewQuery: function DeclarationImportComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 42,\n        vars: 20,\n        consts: [[\"uploadDatepicker\", \"\"], [\"uploadFile\", \"\"], [1, \"display-flex-main\"], [1, \"left-column\"], [1, \"display-flex\"], [1, \"top-action-row-import\"], [1, \"upload-import-container\"], [1, \"upload-file\"], [\"id\", \"importedDataUploadFile\"], [\"class\", \"import-file\", 4, \"ngIf\"], [1, \"error-message\"], [1, \"fill-extra-space\"], [1, \"top-action-row\"], [1, \"custom-datepicker\"], [\"matInput\", \"\", \"title\", \"Choose Date\", 3, \"ngModelChange\", \"matDatepicker\", \"ngModel\"], [\"matIconSuffix\", \"\", 3, \"for\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\", \"disabled\"], [1, \"table-container\"], [\"scrollHeight\", \"68vh\", \"defaultSortColumnId\", \"uploadedDateTime\", 3, \"onLinkClick\", \"onLazyLoad\", \"onRowClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\", \"pageSizeOptions\"], [1, \"right-column\"], [1, \"left-column-titles\"], [1, \"divider-margin\"], [1, \"entity-detail-container\"], [4, \"ngIf\"], [1, \"detail-note_header\"], [1, \"processing-message\"], [1, \"detail-note\"], [\"class\", \"errormessage\", 4, \"ngIf\"], [1, \"import-file\"], [\"id\", \"importedDataUploadFile\", 2, \"cursor\", \"pointer\", \"text-decoration\", \"underline\", 3, \"click\"], [\"title\", \"Select File\", \"accept\", \"*\", \"type\", \"file\", \"id\", \"UploadProfileImportFile\", 1, \"form-control\", 3, \"change\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"button-right\", 3, \"click\", \"disabled\"], [1, \"errormessage\"], [4, \"ngFor\", \"ngForOf\"]],\n        template: function DeclarationImportComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"div\", 6)(5, \"span\", 7)(6, \"a\", 8);\n            i0.ɵɵtext(7, \" UPLOAD FILE\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(8, DeclarationImportComponent_span_8_Template, 3, 0, \"span\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 10)(10, \"span\");\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(12, \"span\", 11);\n            i0.ɵɵelementStart(13, \"div\", 12)(14, \"mat-form-field\", 13)(15, \"mat-label\");\n            i0.ɵɵtext(16, \"Search Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function DeclarationImportComponent_Template_input_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.uploadedDate, $event) || (ctx.uploadedDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(18, \"mat-datepicker-toggle\", 15)(19, \"mat-datepicker\", null, 0);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function DeclarationImportComponent_Template_button_click_21_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.applyClick());\n            });\n            i0.ɵɵtext(22, \"Apply\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function DeclarationImportComponent_Template_button_click_23_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.resetSearchClick());\n            });\n            i0.ɵɵtext(24, \"Reset Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function DeclarationImportComponent_Template_button_click_25_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.downloadFileErrors());\n            });\n            i0.ɵɵtext(26, \"Download Errors\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(27, \"div\", 18)(28, \"bdo-table\", 19);\n            i0.ɵɵlistener(\"onLinkClick\", function DeclarationImportComponent_Template_bdo_table_onLinkClick_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onLinkClick($event));\n            })(\"onLazyLoad\", function DeclarationImportComponent_Template_bdo_table_onLazyLoad_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onLazyLoadEvent($event));\n            })(\"onRowClick\", function DeclarationImportComponent_Template_bdo_table_onRowClick_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.rowClicked($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"div\", 20)(30, \"div\")(31, \"span\", 21);\n            i0.ɵɵtext(32, \"SELECT FILE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(33, \"mat-divider\", 22);\n            i0.ɵɵelementStart(34, \"div\", 23);\n            i0.ɵɵtemplate(35, DeclarationImportComponent_div_35_Template, 7, 1, \"div\", 24);\n            i0.ɵɵelementStart(36, \"div\", 25);\n            i0.ɵɵtext(37, \" If the number of records in your file upload exceeds 100, you may need to refresh the page after 5 minutes to see the uploaded records displayed. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"div\", 26);\n            i0.ɵɵtext(39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"div\", 27);\n            i0.ɵɵtemplate(41, DeclarationImportComponent_div_41_Template, 3, 1, \"div\", 28);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            const uploadDatepicker_r6 = i0.ɵɵreference(20);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeFileDetail);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.actionRequiredMessage, \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"matDatepicker\", uploadDatepicker_r6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.uploadedDate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"for\", uploadDatepicker_r6);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"disabled\", ctx.errorFileId === \"\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.importResultColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndex)(\"pageSize\", ctx.PageSize)(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true)(\"pageSizeOptions\", i0.ɵɵpureFunction0(19, _c1));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.canUploadFile());\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" Upload progress: \", ctx.UploadStatus, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.displayError);\n          }\n        },\n        dependencies: [i6.DefaultValueAccessor, i6.NgControlStatus, i7.MatInput, i8.MatFormField, i8.MatLabel, i8.MatSuffix, i9.MatDivider, i10.MatButton, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, i12.BdoTableComponent, i13.NgForOf, i13.NgIf, i6.NgModel],\n        styles: [\".import-title[_ngcontent-%COMP%]{font-size:1.5em;color:#00779b;display:block;margin:0 auto}.table-container[_ngcontent-%COMP%]{z-index:0;position:relative;margin-right:.5em;min-height:100%!important}.left-column[_ngcontent-%COMP%]{flex:1;margin-right:.5em;min-width:70%}.right-column[_ngcontent-%COMP%]{flex:1;background-color:#f3fafd;min-width:30%;height:55em}.display-flex[_ngcontent-%COMP%]{display:flex}.display-flex-main[_ngcontent-%COMP%]{display:flex;height:100%!important}.top-action-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.top-action-row-import[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;margin-top:1.5em}.upload-import-container[_ngcontent-%COMP%]{display:flex}.span-new-line[_ngcontent-%COMP%]{display:block}.custom-datepicker[_ngcontent-%COMP%]{margin-top:20px;height:70px}.import-detail-container[_ngcontent-%COMP%]{max-height:35em;overflow-y:scroll;margin-bottom:.5em}.entity-detail-text[_ngcontent-%COMP%]{font-weight:bolder}.divider-margin[_ngcontent-%COMP%]{margin:1em}.left-column-titles[_ngcontent-%COMP%]{font-size:1.5em;color:#00779b;display:block;margin:1em}.detail-note_header[_ngcontent-%COMP%], .detail-note[_ngcontent-%COMP%]{font-weight:700;font-style:italic;margin-top:50px}.errormessage[_ngcontent-%COMP%]{font-size:14px;color:red}.upload-file[_ngcontent-%COMP%]{font-size:1.5em;color:#00779b}.import-file[_ngcontent-%COMP%]{font-size:1.5em;margin-left:1rem;color:#00779b}.error-message[_ngcontent-%COMP%]{color:red;margin-top:10px}.processing-message[_ngcontent-%COMP%]{color:#00779b;font-size:1.2em;font-weight:700;margin-top:10px}\"]\n      });\n    }\n  }\n  return DeclarationImportComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}