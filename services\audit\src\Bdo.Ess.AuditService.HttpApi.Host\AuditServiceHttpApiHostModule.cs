﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Bdo.Ess.AuditService.DbMigrations;
using Bdo.Ess.AuditService.EntityFrameworkCore;
using Bdo.Ess.Shared.Hosting.Microservices;
using Bdo.Ess.Shared.Hosting.AspNetCore;
using Prometheus;
using Volo.Abp;
using Volo.Abp.Modularity;
using Bdo.Ess.Shared.HttpApi;
using Volo.Abp.Http.Client.IdentityModel;
using Bdo.Ess.IdentityService;
using Volo.Abp.Security.Claims;
using Volo.Abp.Http.Client;

namespace Bdo.Ess.AuditService;

[DependsOn(
    typeof(EssSharedHostingMicroservicesModule),
    typeof(AuditServiceApplicationModule),
    typeof(AuditServiceHttpApiModule),
    typeof(AuditServiceEntityFrameworkCoreModule),
    typeof(ESSSharedHttpApiModule),
    //typeof(IdentityServiceEntityFrameworkCoreModule),
    typeof(AbpHttpClientIdentityModelModule),
    typeof(IdentityServiceHttpApiClientModule),
    typeof(IdentityServiceApplicationContractsModule)
 )]
public class AuditServiceHttpApiHostModule : AbpModule
{

	public override void PreConfigureServices(ServiceConfigurationContext context)
	{
		PreConfigure<AbpHttpClientBuilderOptions>(options =>
		{
			options.ProxyClientActions.Add((remoteServiceName, clientBuilder, client) =>
			{
				client.Timeout = TimeSpan.FromMinutes(55);
			});
		});
	}
	public override void ConfigureServices(ServiceConfigurationContext context)
    {
        //You can disable this setting in production to avoid any potential security risks.
        Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
        
        // Enable if you need these
        // var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();
                
        JwtBearerConfigurationHelper.Configure(context, "AuditService");
        /* Version 7
        SwaggerConfigurationHelper.ConfigureWithAuth(
            context: context,
            authority: configuration["AuthServer:Authority"],
            scopes: new
                Dictionary<string, string> 
                {
                    {"AuditService", "AuditService API"}
                },
            apiTitle: "AuditService API"
        );
        */
        SwaggerConfigurationHelper.ConfigureWithOidc(
            context: context,
            authority: configuration["AuthServer:Authority"]!,
            scopes: new[] { "AuditService" },
            flows: new[] { "authorization_code" },
            discoveryEndpoint: configuration["AuthServer:MetadataAddress"],
            apiTitle: "AuditService API"
        );
        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = false;
        });
        context.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(
                        configuration["App:CorsOrigins"]?
                            .Split(",", StringSplitOptions.RemoveEmptyEntries)
                            .Select(o => o.Trim().RemovePostFix("/"))
                            .ToArray()
                    )
                    .WithAbpExposedHeaders()
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });
        EncryptionConfigurationHelper.ConfigureDataProtection(context, configuration);
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();
               
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseCorrelationId();
        app.UseAbpRequestLocalization();
        app.UseStaticFiles();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        app.UseCors();
        app.UseAuthentication();
        app.UseAbpClaimsMap();
        app.UseMultiTenancy();
        app.UseUnitOfWork();
        //app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "AuditService API");
            options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
        });
        app.UseAbpSerilogEnrichers();
        app.UseAuditing();
        app.UseConfiguredEndpoints(endpoints => endpoints.MapMetrics());
    }

    public async override Task OnPostApplicationInitializationAsync(ApplicationInitializationContext context)
    {
        using (var scope = context.ServiceProvider.CreateScope())
        {
            await scope.ServiceProvider
                .GetRequiredService<AuditServiceDatabaseMigrationChecker>()
                .CheckAndApplyDatabaseMigrationsAsync();
        }
    }
}
