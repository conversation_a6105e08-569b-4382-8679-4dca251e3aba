﻿using Bdo.Ess.CorporateEntityService.CorporateEntities;
using Bdo.Ess.DashboardService.InformationExchangeDashboard.Dto;
using Bdo.Ess.EconomicSubstanceService.Declarations;
using Bdo.Ess.EconomicSubstanceService.InformationExchanges;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using Bdo.Ess.Shared.Hosting.Microservices.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.InformationExchange;
using Bdo.Ess.Shared.Utility.Extensions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Uow;
using Volo.Abp.Users;

namespace Bdo.Ess.DashboardService.InformationExchangeDashboard
{
    public class InformationExchangeAppService : DashboardServiceAppService,
        IInformationExchangeAppService
    {
        private readonly InformationExchangeManager _manager;
        private readonly IInformationExchangesDetailsAppService _informationExchangeDetailsService;
        private readonly IDataFilter _dataFilter;
        private static readonly ExchangeStatusList exchangeStatusList = new ExchangeStatusList();

        /// <summary>
        ///  Note: _auditWebInfo is scoped dependency instance,
        ///  so, it will be shared between HttpApi and AppService
        ///  Work for Auditing purpose to get client IP address.
        /// </summary>
        private readonly IAuditWebInfo _auditWebInfo;

        private readonly IDistributedEventBus _auditEventBus;

        private readonly IDeclarationAppService _declarationAppService;
        private readonly ICorporateEntityAppService _corporateEntityAppService;
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        public InformationExchangeAppService(InformationExchangeManager manager,
            IInformationExchangesDetailsAppService informationExchangeDetailsService,
            IDeclarationAppService declarationAppService,
            ICorporateEntityAppService corporateEntityAppService,
            IAuditWebInfo auditWebInfo,
            IDistributedEventBus auditEventBus,
            IUnitOfWorkManager unitOfWorkManager,
            IDataFilter dataFilter)
        {
            this._manager = manager;
            _informationExchangeDetailsService = informationExchangeDetailsService;
            _dataFilter = dataFilter;
            _auditWebInfo = auditWebInfo;
            _auditEventBus = auditEventBus;
            _declarationAppService = declarationAppService;
            _corporateEntityAppService = corporateEntityAppService;
            _unitOfWorkManager = unitOfWorkManager;
        }

        public async Task<List<ExchangeStatus>> GetExchangeStatus(InformationExchangeStatus currentSatus, ExchangeReason exchangeReason)
        {
            var filteredList = exchangeStatusList.ExchangeList.Where(x => x.CurrentStatus == currentSatus && x.ExchangeReason == exchangeReason).ToList();
            return await Task.FromResult(filteredList);
        }

        public async Task<bool> AddCaInformationExchange(InformationExchangeDto input)
        {
            try
            {
                if (input != null)
                {
                    using (_dataFilter.Disable<IMultiTenant>())
                    {
                        // need to call the economic substance service to add the information exchange details
                        // I need to send the tenant idso the service will know which tenant it is
                        var id = await _informationExchangeDetailsService.AddInformationExchangeDetails(input.InformationExchangeDetail);
                        var exchangeResult = ObjectMapper.Map<InformationExchangeDto, InformationExchange>(input);
                        exchangeResult.InformationExchangeDetailId = id;
                        await _manager.AddInfromationExchange(exchangeResult);

                        // Work for Audit.
                        try
                        {
                            AuditSpontaneousInformationExchangeDetailsDto auditNewValue = new();

                            auditNewValue.InformationExchangeDetailsId = id;
                            auditNewValue.DeclarationId = input.DeclarationId;
                            auditNewValue.InformationExchangeStatus = input.InformationExchangeStatus.GetEnumDescription();
                            auditNewValue.InformationExchangeReason = input.ExchangeReason.GetEnumDescription();
                            auditNewValue.InformationExchangeDetail = input.InformationExchangeDetail;

                            //
                            // Add Audit Log for Add new Spontaneous Information Exchange Details
                            //
                            await AuditSpontaneousInformationExchangeDetails(input.DeclarationId, null, auditNewValue);
                        }
                        catch (Exception ex)
                        {
                            Logger.LogError(ex, "AddCaInformationExchange calling AuditSpontaneousInformationExchangeDetails exception");
                        }

                        return true;
                    }
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
            }
            return false;
        }

        public async Task<bool> UpdateCaInformationExchange(InformationExchangeDto input)
        {
            try
            {
                if (input != null)
                {
                    using (_dataFilter.Disable<IMultiTenant>())
                    {
                        // Work for Audit.
                        AuditSpontaneousInformationExchangeDetailsDto auditOldValue = new AuditSpontaneousInformationExchangeDetailsDto();
                        AuditSpontaneousInformationExchangeDetailsDto auditNewValue = new AuditSpontaneousInformationExchangeDetailsDto();

                        if (input.Id != Guid.Empty)
                        {
                            InformationExchangeDto existingInfoExchange = await this.GetInformationDetails(input.Id);

                            if (existingInfoExchange != null)
                            {
                                InformationExchangeDetailDto existingInfoExchangeDetail = existingInfoExchange.InformationExchangeDetail;

                                // Keep original values for audit log
                                try
                                {
                                    if (existingInfoExchangeDetail != null)
                                    {
                                        auditOldValue.InformationExchangeDetailsId = existingInfoExchangeDetail?.Id;
                                        auditOldValue.DeclarationId = existingInfoExchange?.DeclarationId;
                                        auditOldValue.InformationExchangeStatus = existingInfoExchangeDetail?.Status.GetEnumDescription();
                                        auditOldValue.InformationExchangeReason = existingInfoExchangeDetail?.ExchangeReason.GetEnumDescription();
                                        auditOldValue.InformationExchangeDetail = existingInfoExchangeDetail;

                                        // Keep new values for audit log
                                        auditNewValue.InformationExchangeDetailsId = existingInfoExchangeDetail?.Id;
                                        auditNewValue.DeclarationId = existingInfoExchange?.DeclarationId;
                                        auditNewValue.InformationExchangeStatus = input?.InformationExchangeStatus.GetEnumDescription();
                                        auditNewValue.InformationExchangeReason = input?.ExchangeReason.GetEnumDescription();
                                        auditNewValue.InformationExchangeDetail = input?.InformationExchangeDetail;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Logger.LogError(ex, "AuditSpontaneousInformationExchangeDetailsDto");
                                }
                            }

                            //
                            // Call Economic Substance Service (another microservice) to update InformationExchangeDetails
                            // Note: In informationExchangeDetailsService.UpdateInformationExchangeDetails call, the current user object cannot passed into the service.
                            //
                            var result = await _informationExchangeDetailsService.UpdateInformationExchangeDetails(input.InformationExchangeDetail);

                            if (result)
                            {
                                // we may need to tack it before I save it lets give it a try
                                var exhcangeResult = await _manager.GetInformationExchangeDetailsById(input.Id);
                                var entity = ObjectMapper.Map<InformationExchangeDto, InformationExchange>(input);
                                ObjectMapper.Map(entity, exhcangeResult);
                                await _manager.UpdateInfromationExchange(exhcangeResult);

                                //
                                // Add Audit Log for Update Spontaneous Information Exchange Details
                                //
                                try
                                {
                                    await AuditSpontaneousInformationExchangeDetails(existingInfoExchange?.DeclarationId, auditOldValue, auditNewValue);
                                }
                                catch (Exception ex)
                                {
                                    Logger.LogError(ex, "AuditSpontaneousInformationExchangeDetails");
                                }

                                return true;
                            }
                            return false;
                        }
                    }
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
            }

            return false;
        }

        public async Task<InformationExchangeDto> GetInformationDetails(Guid id)
        {
            using (_dataFilter.Disable<IMultiTenant>())
            {
                // from the local sql
                var result = await _manager.GetInformationExchangeDetails(id);
                // get the details from the Mango db need to know how the service will resolve the tenant in result
                var informationExchangeDetail = await _informationExchangeDetailsService.GetInformationExchangeDetails(result.InformationExchangeDetailId);
                var mappedResult = ObjectMapper.Map<InformationExchange, InformationExchangeDto>(result);
                mappedResult.InformationExchangeDetail = informationExchangeDetail;
                return mappedResult;
            }
        }

        /// <summary>
        ///  Get information exchange history records associated to specified information exchange parent record.
        /// </summary>
        /// <param name="id">It is primary key "Id" in table dbo.InformationExchanges</param>
        /// <returns></returns>
        public async Task<List<InformationExchangeHistoryDto>> GetInformationDetailHistory(Guid id)
        {
            List<InformationExchangeHistoryDto> result = new List<InformationExchangeHistoryDto>();

            using (_dataFilter.Disable<IMultiTenant>())
            {
                result = await _manager.GetInformationDetailHistory(id);
            }

            if (result != null && result.Count > 0)
            {
                //
                // Try to get back the InformationExchangeStatus enumeration description for DTO property "InformationExchangeStatusName".
                //
                foreach (var item in result)
                {
                    item.InformationExchangeStatusName = item.InformationExchangeStatus.GetEnumDescription();
                }
            }
            return result;
        }

        public async Task<PagedResultDto<InformationExchangeDto>> GetAllCaInformationExchange(GetInformationExchangeDto dto)
        {
            using (_dataFilter.Disable<IMultiTenant>())
            {
                using (_dataFilter.Disable<ISoftDelete>())
                {
                    var result = await _manager.GetAllInformationExchangeDetails(dto);
                    return result;
                }

                // Note: No need to convert FiscalEndDate datetime value, which is stored in UTC format in database, to local time zone here.
                // Since the datetime columns in DataTable component in UI will auto swtich UTC to local time zone in front end.
            }
        }

        /// <summary>
        /// Get all the XML zip fle based on the exchange type
        /// </summary>
        /// <param name="reason"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<InformationExchangeTypeOutputDto> GetXMLFilesFilterByExchangeType(ExchangeReason reason, string year)
        {
            using (_dataFilter.Disable<IMultiTenant>())
            {
                InformationExchangeTypeOutputDto output = new InformationExchangeTypeOutputDto();

                var result = await _manager.GetInformationExchangeReadyForExchangeFiles(reason, year);
                if (result.Count > 0)
                {
                    //Remove API call to economic substance service
                    var mapResult = await _informationExchangeDetailsService.MapDetailsToXml(result, CurrentUser.GetId());
                    output.Content = mapResult.XmlContent;
                    var detailList = mapResult.DetailsList;
                    output.FileName = $"{year}-{reason.ToString()}-{DateTime.Now.ToString("yyyy-MM-ddThh-mm-ss")}.zip";

                    //Update databoard Db status to InformationExchanges table
                    await UpdateInformationExchangeStatus(detailList);
                }

                //
                // Add Audit Log for Generate XML
                //
                try
                {
                    await AddAuditLogForGenerateXML(new AuditGenerateXMLDto()
                    {
                        InformationExchangeReason = reason.GetEnumDescription(),
                        Year = year,
                    });
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "AddAuditLogForGenerateXML");
                }

                return output;
            }
        }

        public async Task UpdateInformationExchangeStatus(List<InformationExchangeDetailsEto> detailList)
        {
            foreach (var eto in detailList)
            {
                try
                {
                    using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);

                    var dbExchange = await _manager.GetInformationExchangeByDetailsId(eto.InformationExchangeDetailsId);
                    if (dbExchange != null)
                        dbExchange.InformationExchangeStatus = InformationExchangeStatus.InformationExchanged;

                    await uow.SaveChangesAsync();
                    await uow.CompleteAsync();
                }
                catch (Exception ex)
                {
                    Logger.LogException(ex);
                }
            }
        }

        private async Task<bool> AddAuditLogForGenerateXML(AuditGenerateXMLDto newValue)
        {
            try
            {
                AuditGenerateXMLEto eto = new AuditGenerateXMLEto()
                {
                    UserName = this.CurrentUser.UserName,
                    UserId = this.CurrentUser.Id,
                    IPAddress = _auditWebInfo.IPAddress,
                    Action = AuditActionEnum.GenerateXML,
                    TenantId = CurrentTenant.Id,
                    AuditDateTime = DateTime.UtcNow,
                    NewValue = newValue
                };

                int generateXMLAuditYear = 0;
                if (!string.IsNullOrEmpty(newValue.Year) && int.TryParse(newValue.Year, out generateXMLAuditYear))
                {
                    eto.ESPeriodEnd = generateXMLAuditYear;
                }

                await this._auditEventBus.PublishAsync(eto);

                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "AuditLogForGenerateXML");
                return false;
            }
        }

        public async Task<List<InformationExchangeSummary>> GetSummaryByYear(string year)
        {
            using (_dataFilter.Disable<IMultiTenant>())
            {
                List<InformationExchangeSummary> summaryList = new List<InformationExchangeSummary>();
                var result = await _manager.GetInformationSummary(year);
                summaryList.Add(result);
                return summaryList;
            }
        }

        /// <summary>
        ///  Audit action for Update Spontaneous Information Exchange Details.
        /// </summary>
        /// <returns></returns>
        private async Task<bool> AuditSpontaneousInformationExchangeDetails(
            Guid? declarationId,
            AuditSpontaneousInformationExchangeDetailsDto? oldValue,
            AuditSpontaneousInformationExchangeDetailsDto? newValue
            )
        {
            bool result = false;

            try
            {
                string formationNumber = string.Empty;
                string entityName = string.Empty;
                string entityUniqueId = string.Empty;
                Guid? entityId = null;

                if (declarationId.HasValue && declarationId.Value != Guid.Empty)
                {
                    var dec = await _declarationAppService.GetCADeclaration(declarationId.Value);
                    if (dec != null && dec.SurveyData != null && dec.SurveyData.EntityId != Guid.Empty)
                    {
                        //
                        //Note: here need to ignore tenant filter and soft delete filter to get the entity details.
                        //
                        var entity = await _corporateEntityAppService.GetCAEntityWithoutTenant(dec.SurveyData.EntityId);

                        if (entity != null)
                        {
                            entityName = entity.Name;
                            entityId = entity.Id;
                            entityUniqueId = entity.EntityUniqueId;
                            formationNumber = (!string.IsNullOrEmpty(entity.CorporateTaxNumber) && entity.CorporateTaxNumber.Trim().Length > 0) ?
                                entity.CorporateTaxNumber : entity.FormationNumber ?? "";

                            if (oldValue != null)
                            {
                                oldValue.FormationNumber = formationNumber;
                                oldValue.EntityName = entityName;
                                oldValue.EntityId = entity.Id;
                                oldValue.DeclarationId = declarationId;
                            }

                            if (newValue != null)
                            {
                                newValue.FormationNumber = formationNumber;
                                newValue.EntityName = entityName;
                                newValue.EntityId = entity.Id;
                                newValue.DeclarationId = declarationId;
                            }
                        }
                    }
                }

                AuditSpontaneousInformationExchangeDetailsEto eto = new AuditSpontaneousInformationExchangeDetailsEto()
                {
                    IPAddress = _auditWebInfo?.IPAddress,
                    Action = AuditActionEnum.UpdateSpontaneousInformationExchangeDetails,
                    TenantId = CurrentTenant?.Id,
                    AuditDateTime = DateTime.UtcNow,
                    ESPeriodEnd = newValue?.InformationExchangeDetail?.FiscalEndDate.Year,
                    EntityName = !string.IsNullOrEmpty(entityName) ? entityName : newValue?.InformationExchangeDetail?.EntityName,
                    EntityId = entityId,
                    DeclarationId = declarationId,
                    EntityFormationNumber = formationNumber,
                    EntityUniqueId = entityUniqueId,
                    OldValue = oldValue,
                    NewValue = newValue
                };

                //
                // Note: There would be bug here when calling "UpdateInformationExchangeDetails" method from the dashboard service.
                // TODO. The current user could be null here.
                if (this.CurrentUser != null && this.CurrentUser.Id.HasValue && this.CurrentUser.Id != Guid.Empty)
                {
                    eto.UserId = this.CurrentUser.Id;
                    eto.UserName = this.CurrentUser.UserName;
                }
                else
                {
                    try
                    {
                        if (this._auditWebInfo != null && !string.IsNullOrEmpty(this._auditWebInfo.AuditUserId))
                        {
                            eto.UserId = new Guid(this._auditWebInfo.AuditUserId);
                        }
                    }
                    catch
                    {
                        eto.UserId = null;
                    }
                    //Note: Get User name in AuditAppService. not here.
                }

                await this._auditEventBus.PublishAsync(eto);

                result = true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "AuditSpontaneousInformationExchangeDetails");
                result = false;
            }

            return result;
        }
    }
}