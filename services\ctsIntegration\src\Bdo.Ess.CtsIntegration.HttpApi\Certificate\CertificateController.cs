﻿using Bdo.Ess.CtsIntegration.Certificate;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.HttpApi.Audit;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Volo.Abp;
using static Bdo.Ess.CtsIntegration.Permissions.CtsIntegrationPermissions;

namespace Bdo.Ess.CtsIntegration.Certificate;

[RemoteService(Name = CtsIntegrationRemoteServiceConsts.RemoteServiceName)]
[Area(CtsIntegrationRemoteServiceConsts.RemoteServiceName)]
[Route($"api/{CtsIntegrationRemoteServiceConsts.RemoteServiceName}/certificate")]
public class CertificateController : CtsIntegrationController//, ICertificateAppService
{
    private readonly ICertificateAppService _certificateAppService;

    /// <summary>
    ///  Note: _auditWebInfo is scoped dependency instance, 
    ///  so, it will be shared between HttpApi and AppService
    ///  Work for Auditing purpose to get client IP address.
    /// </summary>
    private readonly IAuditWebInfo _auditWebInfo;

    public CertificateController(ICertificateAppService certificateAppService, IAuditWebInfo auditWebInfo, AuditWebInfoService webInfoService)
    {
        _certificateAppService = certificateAppService;
        this._auditWebInfo = auditWebInfo;
        this._auditWebInfo.IPAddress = webInfoService.GetClientIpAddress();
        this._auditWebInfo.AuditUserId = webInfoService.GetAuditUserId();
    }

    [HttpGet]
    [Route(nameof(GetBahamasCertificateInfo))]    
    public async Task<BahamasCertificateDto?> GetBahamasCertificateInfo()
    {
        return await _certificateAppService.GetBahamasCertificateInfoForDisplay();
    }

    [HttpPost]
    [Route(nameof(UploadBahamasCertificate))]
    [Authorize(UpdateCACertificatePermissions.Default)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Consumes("multipart/form-data")]
    public async Task<BahamasCertificateDto> UploadBahamasCertificate([FromForm] UploadCertificateDto input)
    {
        _ = await _certificateAppService.UploadBahamasCertificate(input);
        return await _certificateAppService.GetBahamasCertificateInfoForDisplay() ?? new BahamasCertificateDto();
    }

}
