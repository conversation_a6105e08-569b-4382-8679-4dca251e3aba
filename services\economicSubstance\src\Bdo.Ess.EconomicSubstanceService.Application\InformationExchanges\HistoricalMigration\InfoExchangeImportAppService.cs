﻿using Bdo.Ess.EconomicSubstanceService.DeclarationImports;
using Bdo.Ess.EconomicSubstanceService.DeclarationImports.Dtos;
using Bdo.Ess.EconomicSubstanceService.DeclarationImports.Exports;
using Bdo.Ess.EconomicSubstanceService.DeclarationImports.Validators;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.DeclarationImports;
using Bdo.Ess.Shared.Utility.Extensions;
using FluentValidation.Results;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Auditing;
using Volo.Abp.Timing;
using Volo.Abp.Uow;

namespace Bdo.Ess.EconomicSubstanceService.InformationExchanges.HistoricalMigration
{
    public class InfoExchangeImportAppService : EconomicSubstanceServiceAppService, IInfoExchangeImportAppService
    {
        private readonly IAuditingManager _auditingManager;
        private readonly IDeclarationBlobAppService _declarationBlobAppService;
        private readonly IDeclarationImportCacheAppService _imortCacheAppService;
        private readonly IInfoExchangeImportFileRepository _importFileRepository;
        private readonly IInfoExchangeImportValidationAppService _validationAppService;
        private readonly IInfoExchangeImportDbService _dbAppService;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        public InfoExchangeImportAppService(
            IInfoExchangeImportFileRepository importFileRepository,
            IUnitOfWorkManager unitOfWorkManager,
            IAuditingManager auditingManager,
            IDeclarationImportCacheAppService imortCacheAppService,
            IDeclarationBlobAppService declarationBlobAppService,
            IInfoExchangeImportValidationAppService validationAppService,
            IInfoExchangeImportDbService dbAppService
            )
        {
            _importFileRepository = importFileRepository;
            _unitOfWorkManager = unitOfWorkManager;
            _auditingManager = auditingManager;
            _imortCacheAppService = imortCacheAppService;
            _declarationBlobAppService = declarationBlobAppService;
            _validationAppService = validationAppService;
            _dbAppService = dbAppService;
        }

        public async Task<byte[]> DownloadDataErrors(Guid fileId)
        {
            var errors = await GetDataErrorsForCsv(fileId);
            var data = CsvExporter.ExportData<ExcelValidationErrorCsvDto, ExcelValidationErrorCsvDtoMap>(errors);
            return data;
        }

        public async Task<byte[]> DownloadFileErrors(Guid fileId)
        {
            var errors = await GetFileErrorsForCsv(fileId);
            var data = CsvExporter.ExportData<ExcelValidationErrorCsvDto, ExcelValidationErrorCsvDtoMap>(errors);
            return data;
        }

        public async Task<InfoExchangeImportFileDto> GetAsync(Guid id)
        {
            try
            {
                var file = await _importFileRepository.GetAsync(id);
                return ObjectMapper.Map<InfoExchangeImportFile, InfoExchangeImportFileDto>(file);
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        public async Task<ValidationResult> GetDataErrors(Guid fileId)
        {
            var file = await _importFileRepository.GetAsync(fileId);
            var result = new ValidationResult();
            CompareTool.AddExcelErrorToValidationResult(result, file.DataErrors);
            return result;
        }

        public async Task<ValidationResult> GetFileErrors(Guid fileId)
        {
            var file = await _importFileRepository.GetAsync(fileId);
            var result = new ValidationResult();
            CompareTool.AddExcelErrorToValidationResult(result, file.Errors);
            return result;
        }

        public async Task<List<ExcelValidationErrorCsvDto>> GetFileErrorsForCsv(Guid fileId)
        {
            var file = await _importFileRepository.GetAsync(fileId);
            var errors = ObjectMapper.Map<List<ExcelValidationError>, List<ExcelValidationErrorCsvDto>>(file.Errors);
            return errors;
        }

        public async Task<InfoExchangeImportFileDto> GetFileReadyToSubmit()
        {
            var file = await _importFileRepository.GetFileReadyToSubmit();
            if (file == null) return new InfoExchangeImportFileDto();
            var fileDto = ObjectMapper.Map<InfoExchangeImportFile, InfoExchangeImportFileDto>(file);
            return fileDto;
        }

        public async Task<PagedResultDto<InfoExchangeImportFileDto>> GetFileList(GetInfoExchangeImportFileDto input)
        {
            var fileTuple = await _importFileRepository.GetListAsync(
                input.SkipCount,
                input.MaxResultCount,
                input.Sorting,
                input.UploadedDateTime
            );

            return new PagedResultDto<InfoExchangeImportFileDto>(
               fileTuple.Count,
               ObjectMapper.Map<List<InfoExchangeImportFile>, List<InfoExchangeImportFileDto>>(fileTuple.FileList)
           );
        }

        public async Task<InfoExchangeImportFileDto> GetLastImportedFile()
        {
            var file = await _importFileRepository.GetLastImportedFile();
            if (file == null) return new InfoExchangeImportFileDto();
            var fileDto = ObjectMapper.Map<InfoExchangeImportFile, InfoExchangeImportFileDto>(file);
            return fileDto;
        }

        public async Task<ProgressNotficiationEto> GetUploadProgress()
        {
            return await _imortCacheAppService.GetUploadProgressAsync(ImportFileType.InfoExchange);
        }

        public async Task<bool> HasFileReadyToSumbit()
        {
            return await _importFileRepository.HasFileReadyToSumbitAsyc();
        }

        [UnitOfWork(IsDisabled = true)]
        public async Task<ValidationResult> SubmitFile(Guid fileId)
        {
            try
            {
                return await _dbAppService.SubmitFile(fileId);
            }
            catch (Exception ex)
            {
                var err = ex;
                throw;
            }
            
        }

        public async Task DiscardFile(Guid fileId)
        {
            await _dbAppService.DiscardFile(fileId);
        }

        [UnitOfWork(IsDisabled = true)]
        public async Task<ExcelUploadInfoExchangeResultDto> UploadInfoExchangeImportExcel(IFormFile file)
        {
            throw new UserFriendlyException("The historical migration has been completed, no longer accept new upload.");
            Guid? fileId = null;
            try
            {
                var validationResult = await ShouldDirectReject(file.FileName);
                if (!validationResult.IsValid)
                {
                    return new ExcelUploadInfoExchangeResultDto()
                    {
                        StatusId = UploadedInfoExchangeFileStatus.Rejected,
                        StatusName = UploadedInfoExchangeFileStatus.Rejected.GetEnumDescription(),
                        ValidationResult = validationResult
                    };
                }

                await PublishProgressAsync($"Uploading {file.FileName}", isFirstMessage: true);

                fileId = await UploadToStorage(file);
                if (fileId == null)
                {
                    return new ExcelUploadInfoExchangeResultDto();
                }
                var statusName = UploadedInfoExchangeFileStatus.Uploaded.GetEnumDescription();
                this.Logger.LogDebug("Start Import Progress Message: {0} DateTime: {1}", "Check Excel size is done.", DateTime.Now);

                await PublishProgressAsync($"Check Excel size is done.", fileId, statusName);

                var rt = await _validationAppService.ExecuteExcelProcessAsync(fileId.Value, file);
                statusName = rt.StatusId.GetEnumDescription();
                await PublishProgressAsync($"Process Finished.", fileId, statusName);
                return rt;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                _auditingManager.Current.Log.Exceptions.Add(ex);
                if (fileId.HasValue)
                {
                    try
                    {
                        await _dbAppService.UpdateFileStatus(fileId.Value, UploadedInfoExchangeFileStatus.FailedToImport);
                    }
                    catch 
                    { //ignore exception
                    }
                }
                await PublishProgressAsync($"Failed to Import.", fileId, UploadedFileStatus.FailedToImport.GetEnumDescription());
                throw;
            }
        }

        private async Task<List<ExcelValidationErrorCsvDto>> GetDataErrorsForCsv(Guid fileId)
        {
            var file = await _importFileRepository.GetAsync(fileId);
            var errors = ObjectMapper.Map<List<ExcelValidationError>, List<ExcelValidationErrorCsvDto>>(file.DataErrors);
            return errors;
        }

        

        private async Task PublishProgressAsync(string message, Guid? fileId = null, string status = "", bool isFirstMessage = false)
        {
            var eto = new ProgressNotficiationEto(CurrentTenant.Id, CurrentUser.Id, message, fileId, status);
            eto.IsFirstMessage = isFirstMessage;
            await _imortCacheAppService.PublishToCacheAsync(eto, ImportFileType.InfoExchange);
        }

        private async Task<ValidationResult> ShouldDirectReject(string fileName)
        {
            var validationResult = new ValidationResult();

            if (!new List<string> { ".xlsx", ".xls" }.Contains(Path.GetExtension(fileName).ToLower()))
            {
                validationResult.Errors.Add(new ValidationFailure("Reject", "The file extension is incorrect, it only supports Excel files."));
                return validationResult;
            }
            var alreadyHasFileInTriage = false;

            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                alreadyHasFileInTriage = await _importFileRepository.HasFileReadyToSumbitAsyc();
                await uow.CompleteAsync();
            }
            if (alreadyHasFileInTriage)
            {
                validationResult.Errors.Add(new ValidationFailure("Reject", "New upload is not accepted when there is file in triage."));
            }
            return validationResult;
        }

        /// <summary>
        ///  Upload information exchange excel file and save the file name to Mongo Db "EconomicSubstanceService", "InfoExchangeImportFile" table.
        ///  Then, save file content in Azure Blob Storage.
        /// </summary>
        /// <param name="uploadFile"></param>
        /// <returns></returns>
        private async Task<Guid> UploadToStorage(IFormFile uploadFile)
        {
            Guid fileId;
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
            {
                //Always inserting new record, don't overwrite previous failed uploaded file
                fileId = await _dbAppService.InsertInfoExchangeImportFile(uploadFile.FileName);

                await uow.CompleteAsync();
            }

            //var fileNamePath = await _declarationBlobAppService.UploadImportedExcel(uploadFile, fileId, ImportFileType.InfoExchange);
            var fileNamePath = "";
            //Sometimes the event bus not working, so we need to update the file status here
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
            {
                var file = await _importFileRepository.GetAsync(fileId);
                if (!string.IsNullOrWhiteSpace(file.FileName))
                {
                    file.Status = UploadedInfoExchangeFileStatus.Uploaded;
                    file.FileName = uploadFile.FileName;
                    file.FileUrl = fileNamePath;
                    await _importFileRepository.UpdateAsync(file, true);
                }
                await uow.CompleteAsync();
            }
            return fileId;
        }
    }
}