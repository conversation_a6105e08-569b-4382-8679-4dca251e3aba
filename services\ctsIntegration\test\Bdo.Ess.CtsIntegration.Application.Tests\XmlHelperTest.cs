﻿using Shouldly;
using System;
using System.Linq;
using System.Text;
using System.Xml;
using Xunit;
using Bdo.Ess.Shared.Utility.Utils;
using System.IO;

namespace Bdo.Ess.CtsIntegration.Application.Tests
{
    public class XmlHelperTests
    {
        #region CheckElement(string, string, bool) Tests

        [Fact]
        public void CheckElement_WithStringXml_SimpleElement_CheckFirstTrue_ShouldReturnSingleValue()
        {
            // Arrange
            string xml = "<root><element>Value1</element><element>Value2</element></root>";
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, true);

            // Assert
            result.ShouldBe("Value1");
        }

        [Fact]
        public void CheckElement_WithStringXml_SimpleElement_CheckFirstFalse_ShouldReturnCommaSeparatedValues()
        {
            // Arrange
            string xml = "<root><element>Value1</element><element>Value2</element></root>";
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, false);

            // Assert
            result.ShouldBe("Value1,Value2");
        }

        [Fact]
        public void CheckElement_WithStringXml_NoElement_ShouldReturnEmpty()
        {
            // Arrange
            string xml = "<root><other>Value</other></root>";
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, true);

            // Assert
            result.ShouldBe("");
        }

        [Fact]
        public void CheckElement_WithStringXml_EmptyElement_ShouldReturnEmpty()
        {
            // Arrange
            string xml = "<root><element></element></root>";
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, true);

            // Assert
            result.ShouldBe("");
        }

        [Fact]
        public void CheckElement_WithStringXml_NestedElement_ShouldReturnText()
        {
            // Arrange
            string xml = "<root><element><nested>Value</nested></element></root>";
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, true);

            // Assert
            result.ShouldBe("Value");
        }

        [Fact]
        public void CheckElement_WithStringXml_ElementWithNamespaces_ShouldReturnValue()
        {
            // Arrange
            string xml = "<root xmlns:ns=\"http://example.com\"><ns:element>Value</ns:element></root>";
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, true);

            // Assert
            result.ShouldBe("Value");
        }

        [Fact]
        public void CheckElement_WithStringXml_NullXml_ShouldThrowException()
        {
            // Arrange
            string xml = null;
            string elementName = "element";

            // Act & Assert
            Should.Throw<InvalidDataException>(() => XmlHelper.CheckElement(xml, elementName, true));
        }

        [Fact]
        public void CheckElement_WithStringXml_EmptyXml_ShouldThrowException()
        {
            // Arrange
            string xml = "";
            string elementName = "element";

            // Act & Assert
            Should.Throw<InvalidDataException>(() => XmlHelper.CheckElement(xml, elementName, true));
        }

        [Fact]
        public void CheckElement_WithStringXml_InvalidXml_ShouldThrowXmlException()
        {
            // Arrange
            string xml = "<root><element>unclosed";
            string elementName = "element";

            // Act & Assert
            Should.Throw<XmlException>(() => XmlHelper.CheckElement(xml, elementName, true));
        }

        #endregion

        #region CheckElement(byte[], string, bool) Tests

        [Fact]
        public void CheckElement_WithByteArrayXml_SimpleElement_CheckFirstTrue_ShouldReturnSingleValue()
        {
            // Arrange
            string xmlString = "<root><element>Value1</element><element>Value2</element></root>";
            byte[] xml = Encoding.UTF8.GetBytes(xmlString);
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, true);

            // Assert
            result.ShouldBe("Value1");
        }

        [Fact]
        public void CheckElement_WithByteArrayXml_SimpleElement_CheckFirstFalse_ShouldReturnCommaSeparatedValues()
        {
            // Arrange
            string xmlString = "<root><element>Value1</element><element>Value2</element></root>";
            byte[] xml = Encoding.UTF8.GetBytes(xmlString);
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, false);

            // Assert
            result.ShouldBe("Value1,Value2");
        }

        [Fact]
        public void CheckElement_WithByteArrayXml_NoElement_ShouldReturnEmpty()
        {
            // Arrange
            string xmlString = "<root><other>Value</other></root>";
            byte[] xml = Encoding.UTF8.GetBytes(xmlString);
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, true);

            // Assert
            result.ShouldBe("");
        }

        [Fact]
        public void CheckElement_WithByteArrayXml_NullArray_ShouldThrowException()
        {
            // Arrange
            byte[] xml = null;
            string elementName = "element";

            // Act & Assert
            Should.Throw<InvalidDataException>(() => XmlHelper.CheckElement(xml, elementName, true));
        }

        [Fact]
        public void CheckElement_WithByteArrayXml_EmptyArray_ShouldThrowException()
        {
            // Arrange
            byte[] xml = new byte[0];
            string elementName = "element";

            // Act & Assert
            Should.Throw<Exception>(() => XmlHelper.CheckElement(xml, elementName, true));
        }

        [Fact]
        public void CheckElement_WithByteArrayXml_UTF8WithBOM_ShouldParseCorrectly()
        {
            // Arrange
            string xmlString = "<root><element>Value</element></root>";
            byte[] xml = Encoding.UTF8.GetPreamble().Concat(Encoding.UTF8.GetBytes(xmlString)).ToArray();
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, true);

            // Assert
            result.ShouldBe("Value");
        }

        #endregion

        #region CheckElement(XmlDocument, string, bool) Tests

        [Fact]
        public void CheckElement_WithXmlDocument_SimpleElement_CheckFirstTrue_ShouldReturnSingleValue()
        {
            // Arrange
            var doc = new XmlDocument();
            doc.LoadXml("<root><element>Value1</element><element>Value2</element></root>");
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(doc, elementName, true);

            // Assert
            result.ShouldBe("Value1");
        }

        [Fact]
        public void CheckElement_WithXmlDocument_SimpleElement_CheckFirstFalse_ShouldReturnCommaSeparatedValues()
        {
            // Arrange
            var doc = new XmlDocument();
            doc.LoadXml("<root><element>Value1</element><element>Value2</element></root>");
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(doc, elementName, false);

            // Assert
            result.ShouldBe("Value1,Value2");
        }

        [Fact]
        public void CheckElement_WithXmlDocument_NoElement_ShouldReturnEmpty()
        {
            // Arrange
            var doc = new XmlDocument();
            doc.LoadXml("<root><other>Value</other></root>");
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(doc, elementName, true);

            // Assert
            result.ShouldBe("");
        }

        [Fact]
        public void CheckElement_WithXmlDocument_NullDocument_ShouldThrowException()
        {
            // Arrange
            XmlDocument doc = null;
            string elementName = "element";

            // Act & Assert
            Should.Throw<NullReferenceException>(() => XmlHelper.CheckElement(doc, elementName, true));
        }

        [Fact]
        public void CheckElement_WithXmlDocument_ComplexStructure_ShouldReturnInnerText()
        {
            // Arrange
            var doc = new XmlDocument();
            doc.LoadXml("<root><element><child1>Text1</child1><child2>Text2</child2></element></root>");
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(doc, elementName, true);

            // Assert
            result.ShouldBe("Text1Text2");
        }

        [Fact]
        public void CheckElement_WithXmlDocument_ElementWithAttributes_ShouldReturnText()
        {
            // Arrange
            var doc = new XmlDocument();
            doc.LoadXml("<root><element attr='value'>Text</element></root>");
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(doc, elementName, true);

            // Assert
            result.ShouldBe("Text");
        }

        #endregion

        #region Edge Cases and Integration Tests

        [Fact]
        public void CheckElement_AllOverloads_SameXml_ShouldReturnSameResult()
        {
            // Arrange
            string xmlString = "<root><element>Value1</element><element>Value2</element></root>";
            byte[] xmlBytes = Encoding.UTF8.GetBytes(xmlString);
            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(xmlString);
            string elementName = "element";

            // Act
            string resultString = XmlHelper.CheckElement(xmlString, elementName, true);
            string resultBytes = XmlHelper.CheckElement(xmlBytes, elementName, true);
            string resultDoc = XmlHelper.CheckElement(xmlDoc, elementName, true);

            // Assert
            resultString.ShouldBe(resultBytes);
            resultBytes.ShouldBe(resultDoc);
            resultString.ShouldBe("Value1");
        }

        [Fact]
        public void CheckElement_WithCommasInValues_CheckFirstFalse_ShouldHandleCommas()
        {
            // Arrange
            string xml = "<root><element>Value,with,comma</element><element>Another,value</element></root>";
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, false);

            // Assert
            result.ShouldBe("Value,with,comma,Another,value");
        }

        [Fact]
        public void CheckElement_WithSpecialCharacters_ShouldHandleCorrectly()
        {
            // Arrange
            string xml = "<root><element>&lt;special&gt;</element></root>";
            string elementName = "element";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, true);

            // Assert
            result.ShouldBe("<special>");
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void CheckElement_WithNullElementName_ShouldThrowException(bool checkFirst)
        {
            // Arrange
            string xml = "<root><element>Value</element></root>";
            string elementName = null;

            // Act & Assert
            Should.Throw<ArgumentNullException>(() => XmlHelper.CheckElement(xml, elementName, checkFirst));
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void CheckElement_WithEmptyElementName_ShouldReturnEmpty(bool checkFirst)
        {
            // Arrange
            string xml = "<root><element>Value</element></root>";
            string elementName = "";

            // Act
            string result = XmlHelper.CheckElement(xml, elementName, checkFirst);

            // Assert
            result.ShouldBe("");
        }

        [Fact]
        public void CheckElement_WithLargeXmlDocument_ShouldPerformReasonably()
        {
            // Arrange
            var xmlBuilder = new StringBuilder("<root>");
            for (int i = 0; i < 1000; i++)
            {
                xmlBuilder.Append($"<element>Value{i}</element>");
            }
            xmlBuilder.Append("</root>");
            string xml = xmlBuilder.ToString();
            string elementName = "element";

            // Act
            string resultFirst = XmlHelper.CheckElement(xml, elementName, true);
            string resultAll = XmlHelper.CheckElement(xml, elementName, false);

            // Assert
            resultFirst.ShouldBe("Value0");
            resultAll.ShouldContain("Value0");
            resultAll.ShouldContain("Value999");
            resultAll.Split(',').Length.ShouldBe(1000);
        }

        #endregion
    }
}