﻿using Bdo.Ess.Shared.Utility.Extensions;
using IdentityModel;
using IdentityModel.Client;
using Microsoft.Extensions.Configuration;
using System.Linq.Dynamic.Core.Tokenizer;
using Volo.Abp.DependencyInjection;
using Volo.Abp.IdentityModel;

namespace Bdo.Ess.Jobs.Dashboard
{
    public class DashboardProcessService : ITransientDependency
    {
        private readonly IIdentityModelAuthenticationService _authenticationService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly TokenManager _tokenManager;
        private readonly IConfiguration _configuration;

        public DashboardProcessService(IIdentityModelAuthenticationService authenticationService,
            IHttpClientFactory httpClientFactory,
            TokenManager tokenManager,
            IConfiguration configuration)
        {
            _authenticationService = authenticationService;
            _httpClientFactory = httpClientFactory;
            _tokenManager = tokenManager;
            _configuration = configuration;
        }
        public async Task RunAsync()
        {
            try
            {
                //Below code taken from following example using HttpClient:
                //https://github.com/abpframework/eShopOnAbp/blob/main/services/ordering/test/EShopOnAbp.OrderingService.HttpApi.Client.ConsoleTestApp/ClientDemoService.cs

                var daySetting = _configuration["Job:SyncPreviousNDays"];
                int? previousNdays = string.IsNullOrWhiteSpace(daySetting) ? null : Convert.ToInt32(daySetting);


                var batchSetting = _configuration["Job:BatchSize"];
                int batchSize = string.IsNullOrWhiteSpace(batchSetting) ? 500 : Convert.ToInt32(batchSetting);

                var syncTypes = GetSyncTypes();

                if (syncTypes.Contains(SyncContentType.CorporateEntity))
                {
                    Console.WriteLine("Starts sync Entities");
                    await SyncCorporateEntities(previousNdays, batchSize);

                    Console.WriteLine("Waiting for Enities sync final batch to be processed");
                    WaitBetweenTasks();
                }

                if (syncTypes.Contains(SyncContentType.Declaration))
                {
                    Console.WriteLine("Starts sync Declarations");
                    await SyncDeclarations(previousNdays, batchSize);

                    Console.WriteLine("Waiting for Declarations sync final batch to be processed");
                    WaitBetweenTasks();
                }

                if (syncTypes.Contains(SyncContentType.InformationExchange))
                {
                    Console.WriteLine("Starts sync Information Exchange");
                    await SyncHighRiskInformationExchange(previousNdays, batchSize);
                    Console.WriteLine("End sync Information Exchange");
                }

                if (syncTypes.Contains(SyncContentType.DashboardStats))
                {
                    Console.WriteLine("Starts update Dashboard Stats");
                    await UpdateDashboardStats();
                    Console.WriteLine("End update Dashboard Stats");
                }

                if (syncTypes.Contains(SyncContentType.RefreshCountryCertificate))
                {
                    Console.WriteLine("Starts refresh country certificates");
                    await RefreshCountryCertificates();
                    Console.WriteLine("End refresh country certificates");
                }

                if (syncTypes.Contains(SyncContentType.RefreshTransmissionStatus))
                {
                    Console.WriteLine("Starts refresh transmission status");
                    await RefreshTransmissionStatus();
                    Console.WriteLine("End refresh transmission status");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                throw;
            }
        }

        private List<SyncContentType> GetSyncTypes()
        {
            var addAll = false;
            List<SyncContentType> types = new();
            var strSyncContents = _configuration["Job:SyncContentTypes"];
            if (string.IsNullOrWhiteSpace(strSyncContents))
            {
                addAll = true;
            }
            else
            {
                var arry = strSyncContents.Split(',');
                foreach (var t in arry)
                {
                    SyncContentType enumValue;
                    if (Enum.TryParse(t.Trim(), out enumValue))
                    {
                        types.Add(enumValue);
                    }
                }
                if (types.Count == 0)
                {
                    addAll = true;
                }
            }

            if (addAll)
            {
                types.Add(SyncContentType.CorporateEntity);
                types.Add(SyncContentType.Declaration);
                types.Add(SyncContentType.InformationExchange);
            }
            return types;
        }
        private void WaitBetweenTasks()
        {
            //Sleep for a while, waiting for previous task finishing while dashboard serve saving from event bus
            var waitSetting = _configuration["Job:WaitBetweenTasksInMinutes"];
            var waitMinutes = string.IsNullOrWhiteSpace(waitSetting) ? 5 : Convert.ToInt32(waitSetting);
            Thread.Sleep(60 * 1000 * waitMinutes);
        }

        private async Task<int> GetTotalCount(HttpClient httpClient, string baseUrl, string apiPath, string msgPrefix)
        {
            var url = baseUrl + apiPath;
            var responseMessage = await httpClient.GetAsync(url);
            var count = 0;
            if (responseMessage.IsSuccessStatusCode)
            {
                count = Convert.ToInt32(await responseMessage.Content.ReadAsStringAsync());
            }
            else
            {
                Console.WriteLine($"{msgPrefix} Sync Get Total Count- returns error code: " + responseMessage.StatusCode);
            }
            return count;
        }

        private async Task<int> ProcessOneBatch(HttpClient httpClient, string baseUrl, string apiPath, string msgPrefix)
        {
            var url = baseUrl + apiPath;
            var responseMessage = await httpClient.PostAsync(url, null);
            if (responseMessage.IsSuccessStatusCode)
            {
                try
                {
                    var responseString = await responseMessage.Content.ReadAsStringAsync();
                    //Console.WriteLine($"{msgPrefix} Sync Result: " + responseString);
                    return Convert.ToInt32(responseString);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to parse response content {responseMessage.Content} - {ex.Message}");
                    return 0;
                }
            }
            else
            {
                Console.WriteLine($"{msgPrefix} Sync Batch - returns error code: " + responseMessage.StatusCode);
                return 0;
            }
        }

        private async Task DoSync(string serviceName, SyncContentType syncContentType, int batchSize, string apiTotalPath, string apiBatchPath, bool addBatchFlag = false)
        {
            var strContentType = syncContentType.GetEnumDescription();
            var accessToken = _tokenManager.GetValidToken();
            Console.WriteLine("AccessToken aquired");
            //Perform the actual HTTP request
            using var httpClient = _httpClientFactory.CreateClient("EssClient");
            httpClient.SetBearerToken(accessToken);

            var baseUrl = _configuration[$"RemoteServices:{serviceName}:BaseUrl"] ?? "";

            var totalCount = await GetTotalCount(httpClient, baseUrl, apiTotalPath, serviceName);
            Console.WriteLine($"{strContentType} sync - total count: {totalCount}");

            var batches = (int)Math.Ceiling((double)totalCount / batchSize);
            Console.WriteLine($"{strContentType} sync - total batch: {batches}");

            var actualCount = 0;
            for (int i = 0; i < batches; i++)
            {
                var skipCount = i * batchSize;
                Console.WriteLine($"{strContentType} sync - batch: {i} - skip count: {skipCount}");
                var apiBatchPathOneCall = apiBatchPath + $"&skipCount={skipCount}";
                if (addBatchFlag && i == batches - 1)
                {
                    apiBatchPathOneCall += $"&isLastBatch=true&successCount={actualCount}&totalCount={totalCount}"; 
                }
                if (_tokenManager.IsTokenExpired())
                {
                    accessToken = _tokenManager.GetValidToken();
                    httpClient.SetBearerToken(accessToken);
                }
                
                actualCount += await ProcessOneBatch(httpClient, baseUrl, apiBatchPathOneCall, strContentType);
            }
        }
        private async Task SyncCorporateEntities(int? previousNdays, int batchSize)
        {
            try
            {
                var apiTotalPath = "api/CEService/integration/GetTotalEntityCount";
                if (previousNdays.HasValue) apiTotalPath += $"?previousNdays={previousNdays}";
                var apiBatchPath = $"api/CEService/integration/SyncDataInBatch?previousNdays={previousNdays}&batchSize={batchSize}";
                await DoSync("CorporateEntityService", SyncContentType.CorporateEntity, batchSize, apiTotalPath, apiBatchPath, true);
            }
            catch (Exception ex)
            {
                Console.WriteLine("CorporateEntityService {0}", ex.Message);
            }
        }

        private async Task SyncDeclarations(int? previousNdays, int batchSize)
        {
            try
            {
                var apiTotalPath = "api/ESService/integration/GetTotalDeclarationCount";
                if (previousNdays.HasValue) apiTotalPath += $"?previousNdays={previousNdays}";
                var apiBatchPath = $"api/ESService/integration/SyncDataInBatch?previousNdays={previousNdays}&batchSize={batchSize}";

                await DoSync("EconomicSubstanceService", SyncContentType.Declaration, batchSize, apiTotalPath, apiBatchPath, true);
            }
            catch (Exception ex)
            {
                Console.WriteLine("EconomicSubstanceService {0}", ex.Message);
            }
        }

        private async Task SyncHighRiskInformationExchange(int? previousNdays, int batchSize)
        {
            try
            {
                var apiTotalPath = "api/ESService/integration/GetTotalSubmittedDeclarationCount";
                if (previousNdays.HasValue) apiTotalPath += $"?previousNdays={previousNdays}";
                var apiBatchPath = $"api/ESService/integration/SyncInfoExchangeDataInBatch?previousNdays={previousNdays}&batchSize={batchSize}";

                await DoSync("EconomicSubstanceService", SyncContentType.InformationExchange, batchSize, apiTotalPath, apiBatchPath, true);
            }
            catch (Exception ex)
            {
                Console.WriteLine("InformationExchange {0}", ex.Message);
            }

        }

        private async Task UpdateDashboardStats()
        {
            try
            {
                var apiPath = "api/DashboardService/CADashboard/GenerateDashboardStatisticData";

                var accessToken = await _tokenManager.RefreshTokenAsync();
                Console.WriteLine($"Dashboard AccessToken aquired {accessToken.Length}");
                //Perform the actual HTTP request
                using var httpClient = _httpClientFactory.CreateClient("EssClient");
                httpClient.SetBearerToken(accessToken);

                var baseUrl = _configuration[$"RemoteServices:DashboardService:BaseUrl"] ?? "";

                var url = baseUrl + apiPath;
                var responseMessage = await httpClient.GetAsync(url);
                if (!responseMessage.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Dashboard Stats Update - returns error code: " + responseMessage.StatusCode);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("UpdateDashboardStats {0}", ex.Message);
            }
        }

        private async Task RefreshCountryCertificates()
        {
            try
            {
                var apiPath = "api/CtsIntegrationService/country-certificates/refresh";

                var accessToken = await GetAccessToken();
                Console.WriteLine("RefreshCountryCertificates AccessToken acquired");
                
                using var httpClient = _httpClientFactory.CreateClient("EssClient");
                httpClient.SetBearerToken(accessToken);

                var baseUrl = _configuration["RemoteServices:CtsIntegrationService:BaseUrl"] ?? "";

                var url = baseUrl + apiPath;
                Console.WriteLine("RefreshCountryCertificates - calling url: " + url);
                var responseMessage = await httpClient.PostAsync(url, null);
                
                if (!responseMessage.IsSuccessStatusCode)
                {
                    Console.WriteLine($"RefreshCountryCertificates - returns error code: " + responseMessage.StatusCode);
                }
                else
                {
                    Console.WriteLine("RefreshCountryCertificates completed successfully");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("RefreshCountryCertificates {0}", ex.Message);
            }
        }

        private async Task RefreshTransmissionStatus()
        {
            try
            {
                var apiPath = "api/CtsIntegrationService/CtsPackageRequest/refresh-transmission-status";

                var accessToken = await GetAccessToken();
                Console.WriteLine("RefreshTransmissionStatus AccessToken acquired");
                
                using var httpClient = _httpClientFactory.CreateClient("EssClient");
                httpClient.SetBearerToken(accessToken);

                var baseUrl = _configuration["RemoteServices:CtsIntegrationService:BaseUrl"] ?? "";

                var url = baseUrl + apiPath;
                Console.WriteLine("RefreshTransmissionStatus - calling url: " + url);
                var responseMessage = await httpClient.PostAsync(url, null);
                
                if (!responseMessage.IsSuccessStatusCode)
                {
                    Console.WriteLine($"RefreshTransmissionStatus - returns error code: " + responseMessage.StatusCode);
                }
                else
                {
                    Console.WriteLine("RefreshTransmissionStatus completed successfully");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("RefreshTransmissionStatus {0}", ex.Message);
            }
        }

        private async Task<string> GetAccessToken()
        {
            var accessToken = await _authenticationService.GetAccessTokenAsync(
                new IdentityClientConfiguration(
                    _configuration["IdentityClients:Default:Authority"]!,
                    _configuration["IdentityClients:Default:Scope"]!,
                    _configuration["IdentityClients:Default:ClientId"]!,
                    _configuration["IdentityClients:Default:ClientSecret"]!,
                    grantType: OidcConstants.GrantTypes.ClientCredentials,
                    requireHttps: false
                )
            );
            return accessToken;
        }
        
    }
}
