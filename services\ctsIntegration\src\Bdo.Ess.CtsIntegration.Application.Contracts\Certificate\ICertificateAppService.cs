﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace Bdo.Ess.CtsIntegration.Certificate;

public interface ICertificateAppService : IApplicationService
{
    Task<string> GetBahamasCertificatePublicKey();
    Task<BahamasCertificateDto?> GetBahamasCertificateInfo();
    Task<BahamasCertificateDto?> GetBahamasCertificateInfoForDisplay();
    Task<BahamasCertificateDto> UploadBahamasCertificate(UploadCertificateDto input);
    Task<BahamasCertificateDto?> GetBahamasCertificateByCreationTimeAsync(DateTime creationTime);
}
