﻿using Bdo.Ess.EconomicSubstanceService.InformationExchanges.Dtos;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.InformationExchange;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace Bdo.Ess.EconomicSubstanceService.InformationExchanges
{
    public interface IInformationExchangesDetailsAppService  : IApplicationService
    {

        Task<Guid> AddInformationExchangeDetails(InformationExchangeDetailDto input);

        Task<bool> UpdateInformationExchangeDetails(InformationExchangeDetailDto informationExchangeDetails);

        Task<InformationExchangeDetailDto> GetInformationExchangeDetails(Guid id);
        Task<EssInformationExchangeXMLDto> GetEssInformationXml(Guid essInfoXmlId);
        Task ReTriggerXMLGenerationFlowAsync(Guid tenantId, Guid userId, Guid essXmlId);

        Task<InformationExchangeDetailsListEto> MapDetailsToXml(List<Guid> exchangesIds, Guid userId);
        Task<string> StandardMonitoringFromYear();
        Task UpdateInformationExchangeDetailStatus(Guid id, InformationExchangeStatus status);


    }
}
