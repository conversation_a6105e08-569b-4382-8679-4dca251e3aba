﻿using Bdo.Ess.EconomicSubstanceService.DeclarationImports;
using Bdo.Ess.EconomicSubstanceService.Declarations;
using Bdo.Ess.EconomicSubstanceService.Encryption;
using Bdo.Ess.SearchService.ElasticSearch;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Declaration;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.SearchEto;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities.Events.Distributed;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Volo.Abp.ObjectMapping;

namespace Bdo.Ess.EconomicSubstanceService.SearchHelper
{
    public class SearchHelperAppService : EconomicSubstanceServiceAppService, ISearchHelperAppService, 
        IDistributedEventHandler<DeclarationImportSubmitEto>
    {
        private readonly IDeclarationRepository _declarationRepository;
        private readonly ICurrentTenant _currentTenant;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IDataFilter _filter;
        private readonly ILogger<SearchHelperAppService> _logger;
        private readonly ISearchHelperService _searchServiceHelperService;
        private readonly IDeclarationEncryptionManager _encryptionManager;
        private readonly IHttpClientFactory _httpClientFactory;

        //
        //protected ILogger<ImportProgressEventHandler> Logger;

        public SearchHelperAppService(
			IDeclarationRepository declarationRepository,
			ICurrentTenant currentTenant,
			IDistributedEventBus distributedEventBus,
			IDataFilter filter,
			ILogger<SearchHelperAppService> logger,
			IElasticSearchService elasticSearchService,
			ISearchHelperService searchServiceHelperService,
            IDeclarationEncryptionManager encryptionManager,
        IHttpClientFactory httpClientFactory)
		{
			_declarationRepository = declarationRepository;
			_currentTenant = currentTenant;
			_distributedEventBus = distributedEventBus;
			_filter = filter;
			_logger = logger;
			_searchServiceHelperService = searchServiceHelperService;
            _encryptionManager = encryptionManager;
            _httpClientFactory = httpClientFactory;
		}

		public async Task<bool> ReIndexSearch(Guid tenantId, int pageSize = 250)
        {
            try
            {
                using (_currentTenant.Change(tenantId))
                {
                    var totalCount = await _declarationRepository.GetCountAsync();
                    _logger.LogInformation("Total Declarations for tenant {0}: {1}", tenantId, totalCount);
                    
                    var currentSkip = 0;
                    var totalProcessed = 0;

                    while (currentSkip < totalCount)
                    {
                        var page = await _declarationRepository.GetPagedListAsync(currentSkip, pageSize, "");
                        
                        if (page?.Count > 0)
                        {
                            foreach (var dec in page)
                            {
                                // Decrypt the declaration before indexing
                                _encryptionManager.DecryptDeclaration(dec);
                            }
                            var declarationList = ObjectMapper.Map<List<Declaration>, List<DeclarationEto>>(page);
                            
                            await _searchServiceHelperService.IndexManyDeclarations(declarationList);
                            totalProcessed += page.Count;
                        }
                        
                        _logger.LogInformation("Total Declarations Re-indexed for tenant {0}: {1}", tenantId, totalProcessed);

                        currentSkip += pageSize;
                    }
                    
                    _logger.LogInformation("Re-indexing completed for tenant {0}. Total declarations processed: {1}", tenantId, totalProcessed);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during re-indexing for tenant {0}", tenantId);
                return false;
            }
        }
        public async Task<bool> ReIndexSearchAllTenants(int pageSize = 250)
        {
            try
            {
                using (_filter.Disable<IMultiTenant>())
                {
                    var totalCount = await _declarationRepository.GetCountAsync();
                    _logger.LogInformation("Total Declarations: {0}", totalCount);
                    
                    var currentSkip = 0;
                    var totalProcessed = 0;

                    while (currentSkip < totalCount)
                    {
                        var page = await _declarationRepository.GetPagedListAsync(currentSkip, pageSize, "");
                        
                        if (page?.Count > 0)
                        {
                            // No sensitive data sent to elastic search, no need to decrypt
                            var declarationList = ObjectMapper.Map<List<Declaration>, List<DeclarationEto>>(page);
                            await _searchServiceHelperService.IndexManyDeclarations(declarationList);
                            totalProcessed += page.Count;
                        }

                        _logger.LogInformation("Total Declarations Re-indexed: {0}", totalProcessed);
                        currentSkip += pageSize;
                    }
                    
                    _logger.LogInformation("Re-indexing completed. Total declarations processed: {0}", totalProcessed);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during re-indexing all tenants");
                return false;
            }
        }

        public async Task<bool> DropIndexes()
        {
            var dropIndexEvent = new SearchDropIndexEto();
            await _distributedEventBus.PublishAsync(dropIndexEvent);
            return true;
        }


        public async Task HandleEventAsync(DeclarationImportSubmitEto eventData)
        {
            try
            {
                var page = await _declarationRepository.GetDeclarationList(eventData.DeclarationIds);
                
                if (page?.Count > 0)
                {
                    foreach (var dec in page)
                    {
                        // Decrypt the declaration before indexing
                        _encryptionManager.DecryptDeclaration(dec);
                    }
                    var declarationList = ObjectMapper.Map<List<Declaration>, List<DeclarationEto>>(page);
                    await _searchServiceHelperService.IndexManyDeclarations(declarationList);

                    _logger.LogInformation("Total Declarations Re-indexed from import: {0}", page.Count);
                }
                else
                {
                    _logger.LogWarning("No declarations found for import event with {0} IDs", eventData.DeclarationIds?.Count ?? 0);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during re-indexing declarations from import event");
                throw; // Re-throw to ensure the event handler failure is properly handled
            }
        }
    }
}
