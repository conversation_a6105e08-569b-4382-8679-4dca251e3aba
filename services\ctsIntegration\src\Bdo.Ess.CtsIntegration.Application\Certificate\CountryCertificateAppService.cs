﻿using Bdo.Ess.CtsIntegration.CtsApi;
using Bdo.Ess.CtsIntegration.Encryption;
using Bdo.Ess.CtsIntegration.Entities;
using DeviceDetectorNET.Class.Client;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.Uow;

namespace Bdo.Ess.CtsIntegration.Certificate
{
    public class CountryCertificateAppService : CtsIntegrationAppService, ICountryCertificateAppService
    {
        private readonly ICountryCertificateRepository _repository;
        private readonly ICtsEncryptionManager _ctsEncryptionManager;
        private readonly ICtsLookupDataProvider _lookupDataProvider;
        private readonly ICtsApiClient _ctsApiClient;
        private readonly IDataFilter _dataFilter;
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        public CountryCertificateAppService(
            ICountryCertificateRepository repository
            , ICtsEncryptionManager ctsEncryptionManager
            , ICtsLookupDataProvider lookupDataProvider
            , IDataFilter dataFilter
            , IConfiguration configuration
            , ICtsApiClient ctsApiClient
            , IUnitOfWorkManager unitOfWorkManager)
        {
            _repository = repository;
            _ctsEncryptionManager = ctsEncryptionManager;
            _lookupDataProvider = lookupDataProvider;
            _ctsApiClient = ctsApiClient;
            _dataFilter = dataFilter;
            _configuration = configuration;
            _unitOfWorkManager = unitOfWorkManager;
        }

        [RemoteService(false)]
        public async Task<List<CountryCertificateDto>> GetAllAsync()
        {
            var items = await _repository.GetListAsync();
            var decryptedItems = new List<CountryCertificate>();
            foreach (var cert in items)
            {
               decryptedItems.Add(_ctsEncryptionManager.DecryptCountryCertificate(cert, true));
            }
            return decryptedItems.Select(ObjectMapper.Map<CountryCertificate, CountryCertificateDto>).ToList();
        }
        [RemoteService(false)]
        public async Task<CountryCertificateDto?> GetByCountryCode2Async(string countryCode)
        {
            countryCode = countryCode.Replace(".00", "");
            var cert = await _repository.FindByCountryCodeAsync(countryCode);
            if (cert == null) return null;
            cert = _ctsEncryptionManager.DecryptCountryCertificate(cert,true);
            return ObjectMapper.Map<CountryCertificate, CountryCertificateDto>(cert);
        }
        [RemoteService(false)]
        public async Task<CountryCertificateDto?> GetByCountryCode2ByCreationTimeAsync(string countryCode, DateTime creationTime)
        {
            countryCode = countryCode.Replace(".00", "");
            using (_dataFilter.Disable<ISoftDelete>())
            {
                var cert = (await _repository.GetListAsync(x => x.ValidFrom <= creationTime && x.ExpiredAt >= creationTime && x.CountryCode == countryCode))
                    .OrderByDescending(x => x.CreationTime).FirstOrDefault();
                if (cert == null) return null;

                cert = _ctsEncryptionManager.DecryptCountryCertificate(cert, true);

                return ObjectMapper.Map<CountryCertificate, CountryCertificateDto>(cert);
            }
        }
        [RemoteService(false)]
        public async Task<CountryCertificateDto> InsertOrUpdateAsync(IFormFile pubCertFile, string countryCode, string opsKey)
        {
            var settingKey = _configuration["MaintenanceKey"];
            if (settingKey != opsKey)
            {
                throw new InvalidOperationException("Bad Request: Invalid Operation key provided.");
            }
            // Validate if the country code exists in the lookup service
            var country = await _lookupDataProvider.GetCountryListAsync();
            if (!country.Any(c => c.Code2.Equals(countryCode, StringComparison.OrdinalIgnoreCase))) 
            {
                throw new UserFriendlyException($"Country code '{countryCode}' does not exist in the system.");
            }

            var existing = await _repository.FindByCountryCodeAsync(countryCode);

            await using var ms = new MemoryStream();
            await pubCertFile.OpenReadStream().CopyToAsync(ms);
            var rawData = ms.ToArray();
            var publicKey = Convert.ToBase64String(rawData);

            var cert = new X509Certificate2(rawData);
            var expiredAt = DateTime.SpecifyKind(cert.NotAfter, DateTimeKind.Utc);
            var validFrom = DateTime.SpecifyKind(cert.NotBefore, DateTimeKind.Utc);

            if (existing != null)
            {
                existing.PublicKey = publicKey;
                existing.ExpiredAt = expiredAt;
                existing.ValidFrom = validFrom;
                _ctsEncryptionManager.EncryptCountryCertificate(existing);
                await _repository.UpdateAsync(existing, true);
                var clonedExist = _ctsEncryptionManager.DecryptCountryCertificate(existing, true);
                return ObjectMapper.Map<CountryCertificate, CountryCertificateDto>(clonedExist);
            }

            // The object initializer is necessary here because the 'required' properties
            // in CountryCertificate must be set, and the constructor does not set the
            // 'required' flag for the compiler. Using both the constructor and the
            // object initializer ensures all 'required' properties are satisfied and
            // avoids compile-time errors in C# 11+ with 'required' members.
            var newCert = new CountryCertificate(
                GuidGenerator.Create()
            )
            {
                CountryCode = countryCode,
                PublicKey = publicKey,
                ExpiredAt = expiredAt,
                ValidFrom = validFrom
            };

            _ctsEncryptionManager.EncryptCountryCertificate(newCert);
            newCert = await _repository.InsertAsync(newCert, true);
            var cloned = _ctsEncryptionManager.DecryptCountryCertificate(newCert,true);
            return ObjectMapper.Map<CountryCertificate, CountryCertificateDto>(cloned);
        }

        [RemoteService(false)]
        public async Task InsertOrUpdateAsync(string base64Cert, string countryCode)
        {
            if (string.IsNullOrWhiteSpace(base64Cert))
            {
                Logger.LogInformation("No certificate content for country {Country}. Skipping.", countryCode);
                return;
            }
            countryCode = countryCode.Replace(".00", "");
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);

            var cert = new X509Certificate2(Convert.FromBase64String(base64Cert));

            var expiredAt = DateTime.SpecifyKind(cert.NotAfter, DateTimeKind.Utc);
            var validFrom = DateTime.SpecifyKind(cert.NotBefore, DateTimeKind.Utc);
            var existing = await _repository.FindByCountryCodeAsync(countryCode);

            if (existing != null)
            {

                //Soft delete previous certificate for unpacking receiving package
                if (expiredAt != existing.ExpiredAt || validFrom != existing.ValidFrom )
                {
                    await _repository.DeleteAsync(existing,true);
                }
                else
                {
                    //Certificate not changed
                    await uow.CompleteAsync();
                    return;
                }
            }

            var newCert = new CountryCertificate()
            {
                CountryCode = countryCode,
                PublicKey = base64Cert,
                ExpiredAt = expiredAt,
                ValidFrom = validFrom
            };
            _ctsEncryptionManager.EncryptCountryCertificate(newCert);
            await _repository.InsertAsync(newCert,true);
            Logger.LogInformation("Inserted new certificate for country {Country}", countryCode);

            await uow.CompleteAsync();
        }
        [RemoteService(false)]
        public async Task<bool> IsEnrolledAsync(string countryCode)
        {
            countryCode = countryCode.Replace(".00", "");
            var cert = await _repository.FindByCountryCodeAsync(countryCode);

            // Log time current and expired
            if (cert != null)
            {
                Logger.LogInformation("Checking enrollment for country {CountryCode}: Current Time = {CurrentTime}, Expired At = {ExpiredAt}",
                    countryCode, Clock.Now, cert.ExpiredAt);
            }
            else
            {
                Logger.LogInformation("No certificate found for country {CountryCode}. Assuming not enrolled.", countryCode);
            }

            return cert != null && cert.ExpiredAt > Clock.Now;
        }

        public async Task RefreshCertificates()
        {
            Logger.LogInformation("Starting certificate refresh using CTS API");

            var countries = await _lookupDataProvider.GetCountryListAsync();
            var countryCodes = countries.Select(x => x.Code2).Where(c => !string.IsNullOrWhiteSpace(c)).ToList();

            if (!countryCodes.Any())
            {
                Logger.LogWarning("No countries found for certificate refresh");
                return;
            }

            //If using "QM" for testing, need to add it to country list
            var useTestCountry = _configuration["UseTestCountry"];
            if (!string.IsNullOrWhiteSpace(useTestCountry))
            {
                countryCodes.Add(useTestCountry);
            }
            // Use bulk download for better performance
            //var certificates = await _ctsApiClient.GetCertificatesAsync(countryCodes);

            foreach (var code in countryCodes)
            {
               
                try 
                {

                    var pem = await _ctsApiClient.GetCertificateAsync(code); 
                    await InsertOrUpdateAsync(pem, code);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error while processing certificate for country {Country}", code);
                }
            }

            Logger.LogInformation("Certificate refresh completed");
        }

        [RemoteService(false)]
        // Returns the raw public key PEM as byte[] for a given country code
        public async Task<byte[]> GetPublicKeyBytesByCountryCodeAsync(string countryCode)
        {
            countryCode = countryCode.Replace(".00", "");
            var cert = await _repository.FindByCountryCodeAsync(countryCode);
            if (cert == null)
            {
                throw new UserFriendlyException($"No certificate found for country code: {countryCode}");
            }
            cert = _ctsEncryptionManager.DecryptCountryCertificate(cert, true);
            return Convert.FromBase64String(cert.PublicKey);
        }

        [RemoteService(false)]
        // Returns the X509Certificate2 object from the PEM for a given country code
        public async Task<X509Certificate2> GetX509CertificateByCountryCodeAsync(string countryCode)
        {
            var cert = await _repository.FindByCountryCodeAsync(countryCode);
            if (cert == null)
            {
                throw new UserFriendlyException($"No certificate found for country code: {countryCode}");
            }
            cert = _ctsEncryptionManager.DecryptCountryCertificate(cert, true);
            var pem = cert.PublicKey;
            var base64 = pem.Replace("-----BEGIN CERTIFICATE-----", string.Empty)
                            .Replace("-----END CERTIFICATE-----", string.Empty)
                            .Replace("\r", string.Empty)
                            .Replace("\n", string.Empty)
                            .Trim();
            var rawData = Convert.FromBase64String(base64);
            return new X509Certificate2(rawData);
        }
    }
}