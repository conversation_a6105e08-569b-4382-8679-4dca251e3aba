{"ast": null, "code": "import { InformationExchangeStatus } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\nimport { AppComponentBase } from '../../../../app-component-base';\nimport { DateHelper } from '../../../../shared/utils/date-helper';\nimport { BdoTableColumnType, BdoTableData } from '../../../../shared/components/bdo-table/bdo-table.model';\nimport { ExchangeReasonDic, InformationExchangeStatusDic, CTSUploadStatusDic, CTSUploadExchangeReasonDic } from '../../../../shared/constants';\nimport Swal from 'sweetalert2';\nimport { InformationExchangeHistoryComponent } from '../information-exchange-history/information-exchange-history.component';\nimport { UpdateCaCertificateDialogComponent } from '../update-ca-certificate-dialog/update-ca-certificate-dialog.component';\nimport { ViewAssociatedExchangeRecordsComponent } from '../view-associated-exchange-records/view-associated-exchange-records.component';\nimport { UploadHistoricalXmlDialogComponent } from '../upload-historical-xml-dialog/upload-historical-xml-dialog.component';\nimport { DecryptDataPacketDialogComponent } from '../decrypt-data-packet-dialog/decrypt-data-packet-dialog.component';\nimport { RegeneratePacketDialogComponent } from '../regenerate-packet-dialog/regenerate-packet-dialog.component';\nimport { ViewCommentDialogComponent } from '../view-comment-dialog/view-comment-dialog.component';\nimport { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/enums/ctsupload-status.enum';\nimport { UpdateCtsSettingDialogComponent } from '../update-cts-setting-dialog/update-cts-setting-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/controllers\";\nimport * as i3 from \"../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges\";\nimport * as i4 from \"@abp/ng.core\";\nimport * as i5 from \"proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/certificate.service\";\nimport * as i8 from \"../../../../shared/services/upload-file.service\";\nimport * as i9 from \"@abp/ng.theme.shared\";\nimport * as i10 from \"proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration\";\nimport * as i11 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-request/cts-package-request.service\";\nimport * as i12 from \"@app/shared/services/sweetalert.service\";\nimport * as i13 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"@angular/material/input\";\nimport * as i16 from \"@angular/material/form-field\";\nimport * as i17 from \"@angular/material/icon\";\nimport * as i18 from \"@angular/material/button\";\nimport * as i19 from \"@angular/material/select\";\nimport * as i20 from \"@angular/material/core\";\nimport * as i21 from \"@angular/material/tabs\";\nimport * as i22 from \"../../../../shared/components/bdo-table/bdo-table.component\";\nimport * as i23 from \"@angular/common\";\nconst _c0 = () => [10, 20, 50, 100];\nfunction InformationExchangeMainComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"CA Certificate expires at \", i0.ɵɵpipeBind2(2, 1, ctx_r0.certificateExpirationDate, \"dd/MM/yyyy\"), \"\");\n  }\n}\nfunction InformationExchangeMainComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openUpdateCtsSettingDialog());\n    });\n    i0.ɵɵtext(1, \" Update CTS Settings \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openUpdateCaCertificateDialog());\n    });\n    i0.ɵɵtext(1, \" Update CA Certificate \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(0));\n    });\n    i0.ɵɵtext(1, \" Non-compliance XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(1));\n    });\n    i0.ɵɵtext(1, \" High Risk IP XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(2));\n    });\n    i0.ɵɵtext(1, \" Non-resident XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(3));\n    });\n    i0.ɵɵtext(1, \" Other Cases XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r8, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r9.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r9.description, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_mat_tab_38_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.openDecryptDialog());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"lock_open\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Decrypt Received Data Packet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_mat_tab_38_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.openUploadHistoricalXmlDialog());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Upload Historical XML\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_mat_tab_38_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.openCtsUploadDialog());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" CTS Upload\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_mat_tab_38_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.refreshAllTransmissionStatus());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Refresh CTS Transmission Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_mat_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r15);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r15, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_mat_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r16.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r16.description, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_mat_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r17.code2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r17.name, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_mat_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r18.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r18.description, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab\", 25)(1, \"div\", 26)(2, \"div\", 3);\n    i0.ɵɵelement(3, \"bdo-table\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 5)(5, \"div\", 6);\n    i0.ɵɵtemplate(6, InformationExchangeMainComponent_mat_tab_38_button_6_Template, 4, 0, \"button\", 8)(7, InformationExchangeMainComponent_mat_tab_38_button_7_Template, 4, 0, \"button\", 8)(8, InformationExchangeMainComponent_mat_tab_38_button_8_Template, 4, 0, \"button\", 8)(9, InformationExchangeMainComponent_mat_tab_38_button_9_Template, 4, 0, \"button\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 15)(12, \"mat-label\", 28);\n    i0.ɵɵtext(13, \"Financial Period End\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 12)(15, \"mat-select\", 13);\n    i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.ctsUploadSelectedYear, $event) || (ctx_r0.ctsUploadSelectedYear = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadYearChange($event));\n    });\n    i0.ɵɵtemplate(16, InformationExchangeMainComponent_mat_tab_38_mat_option_16_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 15)(18, \"mat-label\", 28);\n    i0.ɵɵtext(19, \"Exchange Reason\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"mat-form-field\", 12)(21, \"mat-select\", 29);\n    i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectExchangeReason, $event) || (ctx_r0.selectExchangeReason = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadExchangeReasonChange($event));\n    });\n    i0.ɵɵtemplate(22, InformationExchangeMainComponent_mat_tab_38_mat_option_22_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 15)(24, \"mat-label\", 28);\n    i0.ɵɵtext(25, \"Receiving Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-form-field\", 12)(27, \"mat-select\", 30);\n    i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectReceivingCountry, $event) || (ctx_r0.selectReceivingCountry = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadReceivingCountryChange($event));\n    });\n    i0.ɵɵtemplate(28, InformationExchangeMainComponent_mat_tab_38_mat_option_28_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 15)(30, \"mat-label\", 28);\n    i0.ɵɵtext(31, \"CTS Upload Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-form-field\", 12)(33, \"mat-select\", 31);\n    i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectCtsUploadStatus, $event) || (ctx_r0.selectCtsUploadStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadStatusChange($event));\n    });\n    i0.ɵɵtemplate(34, InformationExchangeMainComponent_mat_tab_38_mat_option_34_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(35, \"div\", 2)(36, \"div\", 3)(37, \"bdo-table\", 32);\n    i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onLazyLoad_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadLazyLoadEvent($event));\n    })(\"onLinkClick\", function InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onLinkClick_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadLinkClick($event));\n    })(\"onActionClick\", function InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onActionClick_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadActionClick($event));\n    })(\"onCheckboxClick\", function InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onCheckboxClick_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCheckboxClick($event));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", ctx_r0.ctsDashboardTableId)(\"columns\", ctx_r0.ctsDashboardColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx_r0.currentPageIndexS)(\"pageSize\", ctx_r0.PageSizeS)(\"isVirtualScroll\", false)(\"hidePagination\", true)(\"rowSelectable\", false)(\"lazyLoad\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showDecryptReceivedDataPacket);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showUploadHistoricalXml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showCTSUpload);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showRefreshStatus);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r0.ctsUploadSelectedYear);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.year);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r0.selectExchangeReason);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.ctsUploadExchangeReasonDic);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r0.selectReceivingCountry);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.countries);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r0.selectCtsUploadStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.ctsUploadStatusDic);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", ctx_r0.ctsUploadTableId)(\"columns\", ctx_r0.ctsUploadColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx_r0.ctsUploadCurrentPage)(\"pageSize\", ctx_r0.ctsUploadPageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(31, _c0))(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n  }\n}\nexport class InformationExchangeMainComponent extends AppComponentBase {\n  constructor(injector, router, informationExchangeService, informationExchangeDetailService, permissionService, dashboardService, dialog, certificateService, fileUploadService, toasterService, countryService, ctsPackageRequestService, sweetAlert, bahamasCtsSettingService) {\n    super(injector);\n    this.router = router;\n    this.informationExchangeService = informationExchangeService;\n    this.informationExchangeDetailService = informationExchangeDetailService;\n    this.permissionService = permissionService;\n    this.dashboardService = dashboardService;\n    this.dialog = dialog;\n    this.certificateService = certificateService;\n    this.fileUploadService = fileUploadService;\n    this.toasterService = toasterService;\n    this.countryService = countryService;\n    this.ctsPackageRequestService = ctsPackageRequestService;\n    this.sweetAlert = sweetAlert;\n    this.bahamasCtsSettingService = bahamasCtsSettingService;\n    this.input = {\n      maxResultCount: 10,\n      skipCount: 0,\n      sorting: 'ExchangeReason asc',\n      informationExchangeStatus: InformationExchangeStatus.None,\n      entityName: '',\n      year: ''\n    };\n    this.TableId = 'information_ex-results';\n    /* Work for pagination. Default value = 0, it is rendering first page by default. */\n    this.currentPageIndex = 0;\n    /** It is string year number array. */\n    this.year = [];\n    /** Selected year from Financial Period End Years dropdown, default is current year. */\n    this.selectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default.\n    this.exchangeResultColumns = [{\n      columnId: \"exchangeReason\" /* InformationExchangeTableColumns.EXCHANGE_REASON */,\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: true,\n      columnName: 'Exchange Reason'\n    }, {\n      columnId: \"raCode\" /* InformationExchangeTableColumns.RA_CODE */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'RA Name'\n    }, {\n      columnId: \"entityName\" /* InformationExchangeTableColumns.ENTITY_NAME */,\n      type: BdoTableColumnType.String,\n      minWidth: 100,\n      isSortable: true,\n      columnName: 'Entity Name'\n    }, {\n      columnId: \"incopFormationNo\" /* InformationExchangeTableColumns.INCROP_NUMBER */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Incop#/Formation#'\n    }, {\n      columnId: \"financialPeriod\" /* InformationExchangeTableColumns.FINANCIAL_PERIOD */,\n      type: BdoTableColumnType.Date,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Financial Period End Date'\n    }, {\n      columnId: \"dueDate\" /* InformationExchangeTableColumns.DUE_DATE */,\n      type: BdoTableColumnType.Date,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Due Date'\n    }, {\n      columnId: \"informationExchangeStatus\" /* InformationExchangeTableColumns.INFORMATIONEXCH_STATUS */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Information Exchange Status'\n    }, {\n      columnId: \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */,\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      columnName: 'View Declaration'\n    }, {\n      columnId: \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */,\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      columnName: 'XML Data'\n    }, {\n      columnId: \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */,\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      columnName: 'View History'\n    }];\n    /** Page size setting for \"TableId\" grid */\n    this.PageSize = 10;\n    this.exchangeInformationResultRecords = [];\n    this.selectReportStatus = InformationExchangeStatus.None;\n    this.informationExchangedDic = InformationExchangeStatusDic;\n    this.TableIdS = 'information_ex_summary';\n    this.currentPageIndexS = 0;\n    this.summaryExchangeColumns = [{\n      columnId: \"totalReport\" /* ExchangeSummaryTableColumns.TOTAL_REPORT */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports'\n    }, {\n      columnId: \"totalReportSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports XML Generated'\n    }, {\n      columnId: \"totalReportReady\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_READY */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports Ready for Exchange'\n    }, {\n      columnId: \"totalReportReview\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW */,\n      type: BdoTableColumnType.Number,\n      minWidth: 200,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports for Review<br>(Include: Not required, Waiting for Review/Appeal)'\n    }, {\n      columnId: \"totalReportNotSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT */,\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: 'Total # of Reports Not Started'\n    }];\n    /** Page Size setting for \"TableIdS\" grid. Note: It is not the same as PageSize variable. Don't confuse. */\n    this.PageSizeS = 10;\n    this.totalRecords = 0;\n    /** Note: Only logon user with permission \"Generate XML\"\n     * is able to see the \"Non-compliance XML\",\"High Risk IP XML\",\"Non-resident XML\" buttons. */\n    this.showButton = true;\n    this.showOtherCase = true;\n    /* Default current year value as string. */\n    this.currnetYear = new Date().getFullYear().toString();\n    this.certificateExpirationDate = null;\n    this.bahamasCtsSetting = null;\n    this.isCaSystemAdmin = false;\n    // Dashboard columns for CTS Upload & Transmission\n    this.ctsDashboardColumns = [{\n      columnId: 'totalNotUploaded',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Do Not Upload'\n    }, {\n      columnId: 'totalReadyForUpload',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Ready For Upload'\n    }, {\n      columnId: 'totalFailedUpload',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Failed in Upload'\n    }, {\n      columnId: 'totalUploadedToCTS',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Uploaded to CTS'\n    }, {\n      columnId: 'totalNotEnrolled',\n      type: BdoTableColumnType.Number,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: false,\n      columnName: '# of Packets Receiving Country Not Enrolled'\n    }];\n    // Dashboard data for CTS Upload & Transmission\n    this.ctsDashboardList = [{\n      id: 1,\n      totalNotUploaded: 0,\n      totalReadyForUpload: 1,\n      totalFailedUpload: 1,\n      totalUploadedToCTS: 2,\n      totalNotEnrolled: 2\n    }];\n    // Grid columns for CTS Upload & Transmission\n    this.ctsUploadColumns = [{\n      columnId: 'exchangeReason',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      frozenLeft: true,\n      isSortable: true,\n      columnName: 'Exchange Reason'\n    }, {\n      columnId: 'dataPacket',\n      type: BdoTableColumnType.Link,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'Data Packet'\n    }, {\n      columnId: 'fileCreationDate',\n      type: BdoTableColumnType.Date,\n      minWidth: 120,\n      isSortable: true,\n      columnName: 'File Creation Date'\n    }, {\n      columnId: 'receivingCountry',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'Receiving Country'\n    }, {\n      columnId: 'ctsUploadStatus',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'CTS Upload Status'\n    }, {\n      columnId: 'uploadedAt',\n      type: BdoTableColumnType.DateTime,\n      minWidth: 120,\n      isSortable: true,\n      columnName: 'Uploaded At'\n    }, {\n      columnId: 'ctsTransmissionStatus',\n      type: BdoTableColumnType.String,\n      minWidth: 120,\n      isSortable: false,\n      columnName: 'CTS Transmission Status'\n    }, {\n      columnId: 'viewExchangeRecords',\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'View Exchange Records'\n    }, {\n      columnId: 'viewComments',\n      type: BdoTableColumnType.Link,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'View Comments'\n    }, {\n      columnId: 'regeneratePacket',\n      type: BdoTableColumnType.SingleActionButton,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'Regenerate Packet'\n    }, {\n      columnId: 'ctsUpload',\n      type: BdoTableColumnType.SingleActionButton,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'CTS Upload'\n    }, {\n      columnId: 'excludeFromCtsUpload',\n      type: BdoTableColumnType.Checkbox,\n      minWidth: 60,\n      isSortable: false,\n      columnName: 'Exclude From CTS Upload'\n    }];\n    // CTS Upload & Transmission Dashboard\n    this.ctsUploadExchangeReasonDic = CTSUploadExchangeReasonDic;\n    this.ctsUploadStatusDic = CTSUploadStatusDic;\n    this.ctsUploadSelectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default. \n    this.ctsUploadResultRecords = [];\n    this.selectExchangeReason = -1;\n    this.selectCtsUploadStatus = CTSUploadStatus.NotStarted;\n    this.selectReceivingCountry = '';\n    // Table IDs and page settings\n    this.ctsDashboardTableId = 'cts_dashboard';\n    this.ctsUploadTableId = 'cts_upload_grid';\n    this.ctsUploadPageSize = 10;\n    this.ctsUploadCurrentPage = 0;\n    this.ctsUploadTotalRecords = 0;\n    this.ctsUploadInput = {\n      maxResultCount: 10,\n      skipCount: 0,\n      sorting: 'ExchangeReason asc',\n      ctsUploadStatus: null,\n      exchangeReason: null,\n      financialEndYear: '',\n      receivingCountry: ''\n    };\n    this.showBahamasSettings = false;\n    this.showDataPacketDashboard = false;\n    this.showUpdateCACertificate = false;\n    this.showCTSUpload = false;\n    this.showRefreshStatus = false;\n    this.showRegenerateDataPacket = false;\n    this.showDecryptReceivedDataPacket = false;\n    this.showUploadHistoricalXml = false;\n    this.summaryExchangeList = [{\n      id: 1,\n      totalNoReport: 100,\n      totalNoReportSent: 10,\n      totalNoReportRExchange: 5,\n      totalNoReportRReview: 2,\n      totalNoReportNotSent: 5\n    }];\n  }\n  ngOnInit() {\n    this.getFiscalYears().subscribe(response => {\n      if (response && response.length > 0) {\n        this.year = [];\n        response.forEach(element => {\n          this.year.push(element.toString());\n        });\n      }\n      ;\n    });\n    if (localStorage.getItem('selectedYear')) {\n      this.selectedYear = localStorage.getItem('selectedYear') ?? this.currnetYear;\n    }\n    if (localStorage.getItem('selectReportStatus')) {\n      this.selectReportStatus = Number(localStorage.getItem('selectReportStatus'));\n    }\n    this.informationExchangeDetailService.standardMonitoringFromYear().subscribe(response => {\n      this.standardMonitoringYear = response;\n      this.IsShowOtherCase(this.selectedYear);\n    });\n    this.onLazyLoadEvent(undefined);\n    this.onLazyLoadEventS(undefined);\n    this.showButton = this.checkUserPermission();\n    // CTS Upload & Transmission Dashboard\n    // Check CA System Admin role\n    const currentUser = this.configState.getOne('currentUser');\n    this.isCaSystemAdmin = !!currentUser?.roles?.includes('CA System Admin');\n    if (localStorage.getItem('ctsUploadSelectedYear')) {\n      this.ctsUploadSelectedYear = localStorage.getItem('ctsUploadSelectedYear') ?? this.currnetYear;\n    }\n    if (localStorage.getItem('selectExchangeReason')) {\n      this.selectExchangeReason = Number(localStorage.getItem('selectExchangeReason'));\n    }\n    if (localStorage.getItem('selectCtsUploadStatus')) {\n      this.selectCtsUploadStatus = Number(localStorage.getItem('selectCtsUploadStatus'));\n    }\n    if (localStorage.getItem('selectReceivingCountry')) {\n      this.selectReceivingCountry = localStorage.getItem('selectReceivingCountry');\n    }\n    this.showBahamasSettings = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.BahamasCtsSetting\" /* Permissions.BAHAMAS_CTS_SETTING */);\n    this.showDataPacketDashboard = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.DataPacketDashboard\" /* Permissions.DATA_PACKET_DASHBOARD */);\n    this.showUpdateCACertificate = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.UpdateCACertificate\" /* Permissions.UPDATE_CA_CERTIFICATE */);\n    this.showCTSUpload = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.CTSUpload\" /* Permissions.CTS_UPLOAD */);\n    this.showRefreshStatus = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.RefreshStatus\" /* Permissions.REFRESH_STATUS */);\n    this.showRegenerateDataPacket = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.RegenerateDataPacket\" /* Permissions.REGENERATE_DATA_PACKET */);\n    this.showDecryptReceivedDataPacket = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.DecryptReceivedDataPacket\" /* Permissions.DECRYPT_RECEIVED_DATA_PACKET */);\n    this.showUploadHistoricalXml = this.permissionService.getGrantedPolicy(\"CtsIntegration.UploadHistoricalXml\" /* Permissions.UPLOAD_HISTORICAL_XML */);\n    this.getCountries();\n    if (this.showUpdateCACertificate) {\n      this.getBahamasCertificateInfo();\n    }\n    if (this.showBahamasSettings) {\n      this.getBahamasCtsSettingInfo();\n    }\n    if (this.showDataPacketDashboard) {\n      this.onCtsUploadLazyLoadEvent(undefined);\n      this.onCtsDashboardLazyLoadEvent(undefined);\n    }\n  }\n  IsShowOtherCase(year) {\n    const selectedYearAsInt = parseInt(year, 10);\n    const standardMonitoringYearAsInt = parseInt(this.standardMonitoringYear, 10);\n    this.showOtherCase = selectedYearAsInt <= standardMonitoringYearAsInt ? true : false;\n  }\n  /** Lazy load event works for \"TableIds\" grid only. */\n  onLazyLoadEventS(event) {\n    this.currentPageIndexS = 0;\n    this.informationExchangeService.getSummaryByYearByYear(this.selectedYear).subscribe(response => {\n      this.summaryExchangeList = response;\n      setTimeout(() => {\n        this.setTableDataS();\n      }, 200);\n    });\n  }\n  /** Lazy load event works for grid \"TableId\" only. */\n  onLazyLoadEvent(event) {\n    if (event) {\n      if (this.PageSize === (event.pageSize ?? 10)) {\n        this.currentPageIndex = event.pageNumber ?? 0;\n      } else {\n        //\n        // if Page size got changed through pagination control,\n        // need to reset current page index to 0.\n        //\n        this.PageSize = event.pageSize ?? 10;\n        this.currentPageIndex = 0;\n      }\n      this.input.skipCount = (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\n      this.input.maxResultCount = this.PageSize ?? 10;\n      if (event.isAscending === false) {\n        this.input.sorting = `${event.sortField} desc`;\n      } else {\n        this.input.sorting = `${event.sortField} asc`;\n      }\n    } else {\n      this.currentPageIndex = 0;\n      this.PageSize = 10;\n      this.input.informationExchangeStatus = this.selectReportStatus;\n      this.input.year = this.selectedYear;\n      this.input.skipCount = 0;\n      this.input.maxResultCount = this.PageSize;\n    }\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      if (response) {\n        this.totalRecords = response.totalCount;\n        this.exchangeInformationResultRecords = response.items;\n      } else {\n        this.totalRecords = 0;\n        this.exchangeInformationResultRecords = [];\n      }\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n  }\n  setTableDataS() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.TableIdS;\n    tableData.totalRecords = 1;\n    tableData.data = this.summaryExchangeList.map(x => {\n      return {\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: \"totalReport\" /* ExchangeSummaryTableColumns.TOTAL_REPORT */,\n          value: x.totalNoofReports\n        }, {\n          columnId: \"totalReportSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT */,\n          value: x.totalNoofExchangedReports\n        }, {\n          columnId: \"totalReportReady\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_READY */,\n          value: x.totalNoofReadyExchangedReports\n        }, {\n          columnId: \"totalReportReview\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW */,\n          value: x.totalNoofReviewReports\n        }, {\n          columnId: \"totalReportNotSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT */,\n          value: x.totalNotSentReports\n        }]\n      };\n    });\n    setTimeout(() => {\n      this.tableService.setGridData(tableData);\n    }, 10);\n  }\n  getExchangeReasonDescription(input) {\n    const foundStatus = ExchangeReasonDic.find(status => status.value === input);\n    if (foundStatus) return foundStatus.description;\n    return '';\n  }\n  getInformationExchangeStatusDescription(input) {\n    const foundStatus = InformationExchangeStatusDic.find(status => status.value === input);\n    if (foundStatus) return foundStatus.description;\n    return '';\n  }\n  base64ToUint8Array(x) {\n    const raw = atob(x);\n    var rawLength = raw.length;\n    var array = new Uint8Array(new ArrayBuffer(rawLength));\n    for (let i = 0; i < rawLength; i++) {\n      array[i] = raw.charCodeAt(i);\n    }\n    return array;\n  }\n  downloadFile(content, name) {\n    var file = new Blob([this.base64ToUint8Array(content)]);\n    var fileURL = window.URL.createObjectURL(file);\n    var element = document.createElement('a');\n    document.body.appendChild(element);\n    element.style.display = 'none';\n    element.href = fileURL;\n    element.download = name;\n    element.click();\n    element.remove();\n  }\n  GenerateXMlByType(exchangeType) {\n    this.informationExchangeService.getXMLFilesFilterByExchangeTypeByReasonAndYear(exchangeType, this.selectedYear).subscribe(result => {\n      this.onLazyLoadEvent(undefined);\n      if (result.fileName != '') {\n        this.downloadFile(result.content.toString(), result.fileName);\n      } else Swal.fire({\n        icon: 'info',\n        title: 'XML Import',\n        text: 'No data to export.',\n        allowOutsideClick: false\n      });\n    });\n  }\n  tabChanged(event) {\n    if (event.index === 1) {\n      if (this.showDataPacketDashboard) {\n        this.onCtsUploadLazyLoadEvent(undefined);\n        this.onCtsDashboardLazyLoadEvent(undefined);\n      }\n    }\n  }\n  setTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.TableId;\n    tableData.totalRecords = this.totalRecords;\n    tableData.data = this.exchangeInformationResultRecords.map(x => {\n      return {\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: \"exchangeReason\" /* InformationExchangeTableColumns.EXCHANGE_REASON */,\n          value: this.getExchangeReasonDescription(x.exchangeReason)\n        }, {\n          columnId: \"raCode\" /* InformationExchangeTableColumns.RA_CODE */,\n          value: x.raCode\n        }, {\n          columnId: \"entityName\" /* InformationExchangeTableColumns.ENTITY_NAME */,\n          value: x.entityName\n        }, {\n          columnId: \"incopFormationNo\" /* InformationExchangeTableColumns.INCROP_NUMBER */,\n          value: x.companyFormationNumber\n        }, {\n          columnId: \"financialPeriod\" /* InformationExchangeTableColumns.FINANCIAL_PERIOD */,\n          value: x.fiscalEndDate\n        }, {\n          columnId: \"dueDate\" /* InformationExchangeTableColumns.DUE_DATE */,\n          value: x.dueDate\n        }, {\n          columnId: \"informationExchangeStatus\" /* InformationExchangeTableColumns.INFORMATIONEXCH_STATUS */,\n          value: this.getInformationExchangeStatusDescription(x.informationExchangeStatus)\n        }, {\n          columnId: \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */,\n\n          /** If the underneath data \"IsMigrated\" flag is true, then disable the link, otherwise enable the link to view declaration page */\n          value: x.isMigrated === false ? 'view' : ''\n        }, {\n          columnId: \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */,\n          value: 'XML Data'\n        }, {\n          columnId: \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */,\n          value: x.hasHistoryRecord ? 'View History' : ''\n        }]\n      };\n    });\n    setTimeout(() => {\n      this.tableService.setGridData(tableData);\n    }, 100);\n  }\n  onYearChange(ob) {\n    this.selectedYear = ob.value;\n    this.IsShowOtherCase(this.selectedYear);\n    // Keep the selected Year in local storage.\n    localStorage.setItem('selectedYear', ob.value);\n    this.input.informationExchangeStatus = this.selectReportStatus;\n    this.input.year = this.selectedYear;\n    this.input.entityName = this.searchEntityName;\n    this.input.skipCount = 0;\n    this.input.maxResultCount = this.PageSize;\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.exchangeInformationResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n    this.onLazyLoadEventS(undefined);\n  }\n  onSearch() {\n    this.input.informationExchangeStatus = this.selectReportStatus;\n    this.input.year = this.selectedYear;\n    this.input.entityName = this.searchEntityName;\n    this.input.skipCount = 0;\n    this.input.maxResultCount = this.PageSize;\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.exchangeInformationResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n    this.onLazyLoadEventS(undefined);\n  }\n  onReportChange(ob) {\n    this.selectReportStatus = Number(ob.value);\n    this.input.informationExchangeStatus = this.selectReportStatus;\n    this.input.year = this.selectedYear;\n    this.input.entityName = this.searchEntityName;\n    this.input.skipCount = 0;\n    this.input.maxResultCount = this.PageSize;\n    // Keep the selected Report Status in local storage.\n    localStorage.setItem('selectReportStatus', ob.value);\n    this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.exchangeInformationResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n  }\n  onLinkClick(event) {\n    const data = event.rawData;\n    if (event.columnId === \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */) {\n      //\n      // Note: /es-info-exchange/exchangedetail page is shared with \"XML Data\" view which parameter \"id\" = \"Id\" of table dbo.InformationExchanges,\n      // and \"Information Exchange History Page\" view, which paramter \"id\" = \"InformationExchangeDetailId\" of table dbo.InformationExchangeHistories.\n      //\n      this.router.navigate(['/es-info-exchange/exchangedetail'], {\n        //\n        // Passed \"Id\" of table dbo.InformationExchanges.\n        //\n        queryParams: {\n          id: data.id,\n          ishistory: false\n        }\n      });\n    } else if (event.columnId === \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */) {\n      //\n      // When click the \"view\" link button in the Information Exchange records grid.\n      // Route to CaActionPageComponent.ts component\n      //\n      this.router.navigate(['/action-page'], {\n        queryParams: {\n          declarationid: data.declarationId,\n          entityid: data.corporateEntityId,\n          from: 'info-exchange'\n        }\n      });\n    } else if (event.columnId === \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */) {\n      //\n      // Open dialog to show history records. informantion-exchange-history.component.ts\n      //\n      this.openInformationExchangeHistoryDialog(data.id);\n    }\n  }\n  openInformationExchangeHistoryDialog(informationExchangeId) {\n    const dialogRef = this.dialog.open(InformationExchangeHistoryComponent, {\n      height: '750px',\n      width: '1200px',\n      data: {\n        informationExchangeId: informationExchangeId\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      console.log('The dialog was closed', result);\n    });\n  }\n  /** Check if logon user has permission \"Generate XML\".\n   *  Work for show/hide three xml buttons.\n   */\n  checkUserPermission() {\n    let result = false;\n    // Get current logon user object.\n    const currentUser = this.configState.getOne('currentUser');\n    if (currentUser) {\n      result = this.permissionService.getGrantedPolicy(\"DashboardService.Dashboard.GenerateXML\" /* Permissions.DASHBOARD_GENERATE_XML */);\n    }\n    return result;\n  }\n  /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */\n  getFiscalYears() {\n    return this.dashboardService.getFiscalYears().pipe();\n  }\n  // CTS Upload & Transmission Dashboard Methods\n  getCTSUploadStatusDescription(input) {\n    const foundStatus = CTSUploadStatusDic.find(status => status.value === input);\n    if (foundStatus) return foundStatus.description;\n    return '';\n  }\n  getReceivingCountryName(input) {\n    const foundCountry = this.countries.find(status => status.code2 === input);\n    if (foundCountry) return foundCountry.name;\n    return '';\n  }\n  getCountries() {\n    this.countryService.getList({\n      sorting: \"name asc\",\n      maxResultCount: 1000\n    }).subscribe(response => {\n      this.countries = response.items;\n      this.uploadHistoricalCountries = response.items;\n      // Remove code2 with empty string and null values in countries      \n      this.countries = this.countries.filter(country => country.code2 && country.code2.trim() !== '');\n      this.uploadHistoricalCountries = this.uploadHistoricalCountries.filter(country => country.code2 && country.code2.trim() !== '');\n      // add new country ALL in countries \n      this.countries.unshift({\n        name: 'All',\n        code2: ''\n      });\n    });\n  }\n  getBahamasCertificateInfo() {\n    return this.certificateService.getBahamasCertificateInfo().subscribe({\n      next: info => {\n        this.certificateExpirationDate = info?.expiredAt || null;\n      },\n      error: () => {\n        this.certificateExpirationDate = null;\n      }\n    });\n  }\n  getBahamasCtsSettingInfo() {\n    return this.bahamasCtsSettingService.getCurrentSettings().subscribe({\n      next: info => {\n        this.bahamasCtsSetting = info || null;\n      },\n      error: () => {\n        this.bahamasCtsSetting = null;\n      }\n    });\n  }\n  openUpdateCtsSettingDialog() {\n    const dialogRef = this.dialog.open(UpdateCtsSettingDialogComponent, {\n      width: '1200px',\n      data: this.bahamasCtsSetting || null\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (!result) return;\n      const formData = new FormData();\n      if (result.file) {\n        formData.append('fileName', result.file.name);\n        formData.append('file', result.file);\n        formData.append('fileType', result.file.type);\n      }\n      const certificateFileFormData = new FormData();\n      if (result.certificateFile) {\n        certificateFileFormData.append('fileName', result.certificateFile.name);\n        certificateFileFormData.append('file', result.certificateFile);\n        certificateFileFormData.append('fileType', result.certificateFile.type);\n      }\n      const isCreating = result.id == null;\n      const serviceCall = isCreating ? this.fileUploadService.createBahamasCtsSettings(result, formData, certificateFileFormData) : this.fileUploadService.updateBahamasCtsSettings(result, formData, certificateFileFormData);\n      const action = isCreating ? 'create' : 'update';\n      serviceCall.subscribe({\n        next: response => {\n          if (response) {\n            this.getBahamasCtsSettingInfo();\n            this.toasterService.success(`CTS Settings successfully ${action}d.`, '', {\n              life: 5000\n            });\n          } else {\n            this.toasterService.warn(`CTS Settings could not be ${action}d. Please try again later.`, null, {\n              life: 7000\n            });\n          }\n        },\n        error: error => {\n          this.toasterService.error(`An error occurred while trying to ${action} CTS Settings.`, null, {\n            life: 5000\n          });\n          console.error(`Error ${action}ing CTS Settings:`, error);\n        }\n      });\n    });\n  }\n  openUpdateCaCertificateDialog() {\n    const dialogRef = this.dialog.open(UpdateCaCertificateDialogComponent, {\n      width: '500px',\n      data: {}\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.file) {\n        const formData = new FormData();\n        formData.append('fileName', result.file.name);\n        formData.append('file', result.file);\n        formData.append('fileType', result.file.type);\n        // Only call upload if file is present\n        this.fileUploadService.uploadBahamasCertificate(formData, result.password).subscribe({\n          next: response => {\n            if (response) {\n              // Fetch certificate expiration date\n              this.getBahamasCertificateInfo();\n              this.toasterService.success('Bahamas Certificate successfully uploaded', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn('Bahamas Certificate couldn’t be uploaded. Please try again later', null, {\n                life: 7000\n              });\n            }\n          },\n          error: error => {\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n            console.error('Error uploading Bahamas certificate:', error);\n          }\n        });\n      }\n    });\n  }\n  // CTS Upload & Transmission Dashboard Methods\n  setCtsDashboardTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.ctsDashboardTableId;\n    tableData.totalRecords = 1;\n    tableData.data = this.ctsDashboardList.map(x => ({\n      id: x.id,\n      rawData: x,\n      cells: [{\n        columnId: 'totalNotUploaded',\n        value: x.totalNotUploaded\n      }, {\n        columnId: 'totalReadyForUpload',\n        value: x.totalReadyForUpload\n      }, {\n        columnId: 'totalFailedUpload',\n        value: x.totalFailedUpload\n      }, {\n        columnId: 'totalUploadedToCTS',\n        value: x.totalUploadedToCTS\n      }, {\n        columnId: 'totalNotEnrolled',\n        value: x.totalNotEnrolled\n      }]\n    }));\n    setTimeout(() => this.tableService.setGridData(tableData), 10);\n  }\n  setCtsUploadTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.ctsUploadTableId;\n    tableData.totalRecords = this.ctsUploadTotalRecords;\n    tableData.data = this.ctsUploadResultRecords.map(x => ({\n      id: x.id,\n      rawData: x,\n      cells: [{\n        columnId: 'exchangeReason',\n        value: this.getExchangeReasonDescription(x.exchangeReason)\n      }, {\n        columnId: 'dataPacket',\n        value: x.dataPacket\n      }, {\n        columnId: 'fileCreationDate',\n        value: DateHelper.convertUtcToLocalDate(x.fileCreationDate)\n      }, {\n        columnId: 'receivingCountry',\n        value: this.getReceivingCountryName(x.receivingCountry)\n      }, {\n        columnId: 'ctsUploadStatus',\n        value: this.getCTSUploadStatusDescription(x.ctsUploadStatus)\n      }, {\n        columnId: 'uploadedAt',\n        value: DateHelper.convertUtcToLocalDate(x.uploadedAt)\n      }, {\n        columnId: 'ctsTransmissionStatus',\n        value: x.ctsTransmissionStatus\n      }, {\n        columnId: 'viewExchangeRecords',\n        value: x.viewExchangeRecords === true ? 'View' : null\n      }, {\n        columnId: 'viewComments',\n        value: x.viewComments.length > 0 ? 'View Comments' : null\n      }, {\n        columnId: 'regeneratePacket',\n        value: this.showRegenerateDataPacket && x.allowedActions?.includes(0) ? {\n          actionType: 'RegenerateDataPacket',\n          icon: 'refresh',\n          tooltip: \"Regenerate Packet\"\n        } : null\n      }, {\n        columnId: 'ctsUpload',\n        value: this.showCTSUpload && x.allowedActions?.includes(1) ? {\n          actionType: 'ctsUpload',\n          icon: 'cloud_upload',\n          tooltip: \"CTS Upload\"\n        } : null\n      }, {\n        columnId: 'excludeFromCtsUpload',\n        hide: x.allowedActions?.includes(2) || x.allowedActions?.includes(3) ? false : true,\n        value: x.excludeFromCtsUpload\n      }]\n    }));\n    setTimeout(() => this.tableService.setGridData(tableData), 100);\n  }\n  onCtsDashboardLazyLoadEvent(event) {\n    this.currentPageIndexS = 0;\n    this.ctsPackageRequestService.getSummaryByYearByYear(this.ctsUploadSelectedYear).subscribe(response => {\n      this.ctsDashboardList = response;\n      setTimeout(() => {\n        this.setCtsDashboardTableData();\n      }, 200);\n    });\n  }\n  onCtsUploadLazyLoadEvent(event) {\n    if (event) {\n      if (this.ctsUploadPageSize === (event.pageSize ?? 10)) {\n        this.ctsUploadCurrentPage = event.pageNumber ?? 0;\n      } else {\n        //\n        // if Page size got changed through pagination control,\n        // need to reset current page index to 0.\n        //\n        this.ctsUploadPageSize = event.pageSize ?? 10;\n        this.ctsUploadCurrentPage = 0;\n      }\n      this.ctsUploadInput.skipCount = (this.ctsUploadCurrentPage ?? 0) * (this.ctsUploadPageSize ?? 10);\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize ?? 10;\n      if (event.isAscending === false) {\n        this.ctsUploadInput.sorting = `${event.sortField} desc`;\n      } else {\n        this.ctsUploadInput.sorting = `${event.sortField} asc`;\n      }\n    } else {\n      this.ctsUploadCurrentPage = 0;\n      this.ctsUploadPageSize = 10;\n      this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n      this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n      this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n      this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n      this.ctsUploadInput.skipCount = 0;\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n    }\n    this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n      if (response) {\n        this.ctsUploadTotalRecords = response.totalCount;\n        this.ctsUploadResultRecords = response.items;\n      } else {\n        this.ctsUploadTotalRecords = 0;\n        this.ctsUploadResultRecords = [];\n      }\n      setTimeout(() => {\n        this.setCtsUploadTableData();\n      }, 200);\n    });\n  }\n  onCtsUploadYearChange(ob) {\n    this.ctsUploadSelectedYear = ob.value;\n    localStorage.setItem('ctsUploadSelectedYear', ob.value);\n    this.onCtsUploadSearch();\n    this.onCtsDashboardLazyLoadEvent(undefined);\n  }\n  onCtsUploadSearch() {\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n    this.ctsUploadInput.skipCount = 0;\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n    this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n      this.ctsUploadTotalRecords = response.totalCount;\n      this.ctsUploadResultRecords = response.items;\n      setTimeout(() => {\n        this.setCtsUploadTableData();\n      }, 200);\n    });\n  }\n  onCtsUploadExchangeReasonChange(ob) {\n    this.selectExchangeReason = Number(ob.value);\n    // Keep the selected Exchange Reason in local storage.\n    localStorage.setItem('selectExchangeReason', ob.value);\n    this.onCtsUploadSearch();\n  }\n  onCtsUploadStatusChange(ob) {\n    this.selectCtsUploadStatus = Number(ob.value);\n    // Keep the selected Upload Status in local storage.\n    localStorage.setItem('selectCtsUploadStatus', ob.value);\n    this.onCtsUploadSearch();\n  }\n  onCtsUploadReceivingCountryChange(ob) {\n    this.selectReceivingCountry = ob.value;\n    // Keep the selected Receiving Country in local storage.\n    localStorage.setItem('selectReceivingCountry', ob.value);\n    this.onCtsUploadSearch();\n  }\n  onCtsUploadLinkClick(event) {\n    if (event.columnId === 'viewExchangeRecords') {\n      this.dialog.open(ViewAssociatedExchangeRecordsComponent, {\n        width: '1200px',\n        data: {\n          row: event.rawData\n        }\n      });\n    } else if (event.columnId === 'viewComments') {\n      this.dialog.open(ViewCommentDialogComponent, {\n        width: '1200px',\n        data: {\n          row: event.rawData\n        }\n      });\n    } else if (event.columnId === 'dataPacket') {\n      this.downloadCtsDataPacket(event.rawData);\n    }\n  }\n  onCtsUploadActionClick(event) {\n    if (event.action === 'RegenerateDataPacket') {\n      const dialogRef = this.dialog.open(RegeneratePacketDialogComponent, {\n        width: '500px',\n        data: {\n          row: event?.data?.rawData\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result && result.comment) {\n          this.ctsPackageRequestService.regeneratePackage(event.data?.rawData?.ctsPackageId, result.comment).subscribe({\n            next: response => {\n              if (response.success) {\n                this.toasterService.success(response.message || 'Regenerate Package successfully requested', '', {\n                  life: 5000\n                });\n              } else {\n                this.toasterService.warn(response.message || 'Regenerate Package couldn’t be requested. Please try again later', null, {\n                  life: 7000\n                });\n              }\n              this.onCtsUploadLazyLoadEvent(undefined);\n              this.onCtsDashboardLazyLoadEvent(undefined);\n            },\n            error: error => {\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n              console.error('Error requesting Regenerate Package:', error);\n            }\n          });\n        }\n      });\n    } else if (event.action === 'ctsUpload') {\n      this.sweetAlert.fireDialog({\n        action: \"submit\",\n        title: \"CTS Upload\",\n        text: \"Are you sure you would like to proceed?\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.ctsPackageRequestService.uploadToCts(event.data?.rawData?.ctsPackageId).subscribe({\n            next: response => {\n              if (response.success) {\n                this.toasterService.success(response.message || 'CTS Upload successfully completed', '', {\n                  life: 5000\n                });\n              } else {\n                this.toasterService.warn(response.message || 'CTS Upload couldn’t be completed. Please try again later', null, {\n                  life: 7000\n                });\n              }\n              this.onCtsUploadLazyLoadEvent(undefined);\n              this.onCtsDashboardLazyLoadEvent(undefined);\n            },\n            error: error => {\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n              console.error('Error requesting CTS Upload:', error);\n            }\n          });\n        }\n      });\n    }\n  }\n  onCheckboxClick(event) {\n    // Commented out: console.log('Checkbox clicked:', event.selectedRows[0]?.rawData?.ctsPackageId);\n    // Commented out: console.log('Checkbox clicked:', event.rowId);\n    if (event.isChecked) {\n      this.ctsPackageRequestService.markAsDoNotUpload(event.rowId).subscribe({\n        next: response => {\n          if (response.success) {\n            this.toasterService.success(response.message || 'Exclude packet from uploading successfully completed', '', {\n              life: 5000\n            });\n          } else {\n            this.toasterService.warn(response.message || 'Exclude packet from uploading  couldn’t be completed. Please try again later', null, {\n              life: 7000\n            });\n          }\n          this.onCtsUploadLazyLoadEvent(undefined);\n          this.onCtsDashboardLazyLoadEvent(undefined);\n        },\n        error: error => {\n          console.error('Error marking as Do Not Upload:', error);\n        }\n      });\n    } else {\n      this.ctsPackageRequestService.unMarkAsDoNotUpload(event.rowId).subscribe({\n        next: response => {\n          if (response.success) {\n            this.toasterService.success(response.message || 'Include packet for uploading successfully completed', '', {\n              life: 5000\n            });\n          } else {\n            this.toasterService.warn(response.message || 'Include packet for uploading couldn’t be completed. Please try again later', null, {\n              life: 7000\n            });\n          }\n          this.onCtsUploadLazyLoadEvent(undefined);\n          this.onCtsDashboardLazyLoadEvent(undefined);\n        },\n        error: error => {\n          console.error('Error unmarking as Do Not Upload:', error);\n        }\n      });\n    }\n  }\n  // Add this method to open the Upload Historical XML dialog\n  openUploadHistoricalXmlDialog() {\n    const dialogRef = this.dialog.open(UploadHistoricalXmlDialogComponent, {\n      width: '500px',\n      data: {\n        receivingCountries: this.uploadHistoricalCountries || [],\n        fiscalYears: this.year || []\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.success) {\n        this.onCtsUploadLazyLoadEvent(undefined);\n        this.onCtsDashboardLazyLoadEvent(undefined);\n      }\n    });\n  }\n  openCtsUploadDialog() {\n    this.sweetAlert.fireDialog({\n      action: \"submit\",\n      title: \"CTS Upload\",\n      text: `Are you sure you want to upload data packets with a Financial Period End in ${this.ctsUploadSelectedYear}? Once confirmed, all data packets with a status of Not Started for the year will be uploaded to the CTS platform.`,\n      type: \"confirm\"\n    }, confirm => {\n      if (confirm) {\n        this.ctsPackageRequestService.batchUploadToCts(Number(this.ctsUploadSelectedYear)).subscribe({\n          next: response => {\n            if (response.success) {\n              this.toasterService.success(response.message || 'CTS Upload successfully requested', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, {\n                life: 7000\n              });\n            }\n            this.onCtsUploadLazyLoadEvent(undefined);\n            this.onCtsDashboardLazyLoadEvent(undefined);\n          },\n          error: error => {\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n            console.error('Error requesting CTS Upload:', error);\n          }\n        });\n      }\n    });\n    // const dialogRef = this.dialog.open(UploadCtsDialogComponent, {\n    //   width: '500px',\n    //   data: {\n    //     fiscalYears: this.year || []\n    //   }\n    // });\n    // dialogRef.afterClosed().subscribe((result) => {\n    //   if (result && result.fiscalYear) {\n    //     this.ctsPackageRequestService\n    //       .batchUploadToCts(result.fiscalYear).subscribe({\n    //         next: (response) => {\n    //           if (response.success) {\n    //             this.toasterService.success(response.message || 'CTS Upload successfully requested', '', { life: 5000 });\n    //           }\n    //           else {\n    //             this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, { life: 7000 });\n    //           }\n    //           this.onCtsUploadLazyLoadEvent(undefined);\n    //           this.onCtsDashboardLazyLoadEvent(undefined);\n    //         },\n    //         error: (error) => {\n    //           //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n    //           console.error('Error requesting CTS Upload:', error);\n    //         }\n    //       })\n    //   }\n    // });\n  }\n  openDecryptDialog() {\n    const dialogRef = this.dialog.open(DecryptDataPacketDialogComponent, {\n      width: '500px'\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.file) {\n        const formData = new FormData();\n        formData.append('fileName', result.file.name);\n        formData.append('file', result.file);\n        formData.append('fileType', result.file.type);\n        // Only call upload if file is present        \n        this.fileUploadService.unpackCtsPackage(formData).subscribe({\n          next: response => {\n            if (response.success) {\n              this.toasterService.success(response.message || 'Decrypt Data Packet successfully completed', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn(response.message || 'Decrypt Data Packet couldn’t be completed. Please try again later', null, {\n                life: 7000\n              });\n            }\n            this.onCtsUploadLazyLoadEvent(undefined);\n            this.onCtsDashboardLazyLoadEvent(undefined);\n          },\n          error: error => {\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n            console.error('Error decrypting data packet:', error);\n          }\n        });\n      }\n    });\n  }\n  refreshAllTransmissionStatus() {\n    this.sweetAlert.fireDialog({\n      action: \"submit\",\n      title: \"Refresh All Transmission Status\",\n      text: \"Are you sure you would like to proceed?\",\n      type: \"confirm\"\n    }, confirm => {\n      if (confirm) {\n        this.ctsPackageRequestService.refreshTransmissionStatus().subscribe({\n          next: response => {\n            if (response.success) {\n              this.toasterService.success(response.message || 'Refresh All Transmission Status successfully requested', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn(response.message || 'Refresh All Transmission Status couldn’t be requested. Please try again later', null, {\n                life: 7000\n              });\n            }\n            this.onCtsUploadLazyLoadEvent(undefined);\n            this.onCtsDashboardLazyLoadEvent(undefined);\n          },\n          error: error => {\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n            console.error('Error requesting Refresh All Transmission Status:', error);\n          }\n        });\n      }\n    });\n  }\n  downloadCtsDataPacket(row) {\n    this.sweetAlert.fireDialog({\n      action: \"submit\",\n      title: \"Download CTS Data Packet\",\n      text: \"Are you sure you would like to download?\",\n      type: \"confirm\"\n    }, confirm => {\n      if (confirm) {\n        this.ctsPackageRequestService.downloadDataPacketFile(row?.ctsPackageId).subscribe({\n          next: response => {\n            if (response) {\n              this.downloadFile(response, row?.dataPacket);\n              this.toasterService.success('Download CTS Data Packet successfully completed', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn('Download CTS Data Packet couldn’t be completed. Please try again later', null, {\n                life: 7000\n              });\n            }\n          },\n          error: error => {\n            // this.toasterService.error('Error Download CTS Data Packet', null, { life: 200000 });            \n            console.error('Error Download CTS Data Packet:', error);\n          }\n        });\n      }\n    });\n  }\n  static {\n    this.ɵfac = function InformationExchangeMainComponent_Factory(t) {\n      return new (t || InformationExchangeMainComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.InformationExchangeService), i0.ɵɵdirectiveInject(i3.InformationExchangeDetailsService), i0.ɵɵdirectiveInject(i4.PermissionService), i0.ɵɵdirectiveInject(i5.CADashboardContorllerService), i0.ɵɵdirectiveInject(i6.MatDialog), i0.ɵɵdirectiveInject(i7.CertificateService), i0.ɵɵdirectiveInject(i8.FileUploadService), i0.ɵɵdirectiveInject(i9.ToasterService), i0.ɵɵdirectiveInject(i10.CountryService), i0.ɵɵdirectiveInject(i11.CtsPackageRequestService), i0.ɵɵdirectiveInject(i12.SweetAlertService), i0.ɵɵdirectiveInject(i13.BahamasCtsSettingService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: InformationExchangeMainComponent,\n      selectors: [[\"app-information-exchange-main\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 39,\n      vars: 35,\n      consts: [[3, \"selectedTabChange\"], [\"label\", \"Info Exchange Readiness\"], [1, \"top-action-row-exchange\", \"row\"], [1, \"table-container\"], [\"scrollHeight\", \"100%\", \"defaultSortColumnId\", \"uploadedDateTime\", 3, \"onLazyLoad\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\", \"pageSizeOptions\"], [1, \"top-action-column-exchange\", \"row\", \"justify-content-end\"], [1, \"col-md-auto\"], [\"class\", \"certificate-text\", 4, \"ngIf\"], [\"type\", \"button\", \"mat-raised-button\", \"\", \"class\", \"ui-button margin-l-5\", 3, \"click\", 4, \"ngIf\"], [1, \"top-action-column-exchange\", \"row\"], [1, \"col-md-4\", \"col-sm-12\", \"margin-top\"], [1, \"outside-mat-label\"], [1, \"form-field-reduce-length\"], [\"placeholder\", \"Financial Period End\", 3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-3\", \"col-sm-12\", \"margin-top\"], [\"placeholder\", \"Report Status\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"matInput\", \"\", \"placeholder\", \"File Name\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-2\", \"col-sm-12\", \"margin-top\", \"search-button-column\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"search-button\", 3, \"click\"], [\"scrollHeight\", \"36vh\", \"defaultSortColumnId\", \"ExchangeReason\", 3, \"onLazyLoad\", \"onLinkClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [\"label\", \"CTS Upload & Transmission\", 4, \"ngIf\"], [1, \"certificate-text\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\"], [3, \"value\"], [\"label\", \"CTS Upload & Transmission\"], [1, \"row\", \"top-action-row-exchange\"], [\"scrollHeight\", \"auto\", 3, \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [1, \"outside-label\", \"outside-mat-label\"], [\"placeholder\", \"Exchange Reason\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"placeholder\", \"Receiving Country\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"placeholder\", \"CTS Upload Status\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"scrollHeight\", \"36vh\", \"defaultSortColumnId\", \"ExchangeReason\", 3, \"onLazyLoad\", \"onLinkClick\", \"onActionClick\", \"onCheckboxClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"]],\n      template: function InformationExchangeMainComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-tab-group\", 0);\n          i0.ɵɵlistener(\"selectedTabChange\", function InformationExchangeMainComponent_Template_mat_tab_group_selectedTabChange_0_listener($event) {\n            return ctx.tabChanged($event);\n          });\n          i0.ɵɵelementStart(1, \"mat-tab\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"bdo-table\", 4);\n          i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_4_listener($event) {\n            return ctx.onLazyLoadEventS($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵtemplate(7, InformationExchangeMainComponent_span_7_Template, 3, 4, \"span\", 7)(8, InformationExchangeMainComponent_button_8_Template, 2, 0, \"button\", 8)(9, InformationExchangeMainComponent_button_9_Template, 2, 0, \"button\", 8)(10, InformationExchangeMainComponent_button_10_Template, 2, 0, \"button\", 8)(11, InformationExchangeMainComponent_button_11_Template, 2, 0, \"button\", 8)(12, InformationExchangeMainComponent_button_12_Template, 2, 0, \"button\", 8)(13, InformationExchangeMainComponent_button_13_Template, 2, 0, \"button\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"mat-label\", 11);\n          i0.ɵɵtext(17, \"Financial Period End\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-form-field\", 12)(19, \"mat-select\", 13);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_19_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedYear, $event) || (ctx.selectedYear = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_19_listener($event) {\n            return ctx.onYearChange($event);\n          });\n          i0.ɵɵtemplate(20, InformationExchangeMainComponent_mat_option_20_Template, 2, 2, \"mat-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"mat-label\", 11);\n          i0.ɵɵtext(23, \"Report Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-form-field\", 12)(25, \"mat-select\", 16);\n          i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_25_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectReportStatus, $event) || (ctx.selectReportStatus = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_25_listener($event) {\n            return ctx.onReportChange($event);\n          });\n          i0.ɵɵtemplate(26, InformationExchangeMainComponent_mat_option_26_Template, 2, 2, \"mat-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 15)(28, \"mat-label\", 11);\n          i0.ɵɵtext(29, \"Entity Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 12)(31, \"input\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function InformationExchangeMainComponent_Template_input_ngModelChange_31_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchEntityName, $event) || (ctx.searchEntityName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 18)(33, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_33_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵtext(34, \" Search \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 2)(36, \"div\", 3)(37, \"bdo-table\", 20);\n          i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_37_listener($event) {\n            return ctx.onLazyLoadEvent($event);\n          })(\"onLinkClick\", function InformationExchangeMainComponent_Template_bdo_table_onLinkClick_37_listener($event) {\n            return ctx.onLinkClick($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(38, InformationExchangeMainComponent_mat_tab_38_Template, 38, 32, \"mat-tab\", 21);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"id\", ctx.TableIdS)(\"columns\", ctx.summaryExchangeColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndexS)(\"pageSize\", ctx.PageSizeS)(\"isVirtualScroll\", false)(\"hidePagination\", true)(\"rowSelectable\", true)(\"lazyLoad\", true)(\"pageSizeOptions\", i0.ɵɵpureFunction0(33, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.certificateExpirationDate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showBahamasSettings);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showUpdateCACertificate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showButton && ctx.showOtherCase);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectedYear);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.year);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectReportStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.informationExchangedDic);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchEntityName);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.exchangeResultColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndex)(\"pageSize\", ctx.PageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(34, _c0))(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showDataPacketDashboard);\n        }\n      },\n      dependencies: [i14.DefaultValueAccessor, i14.NgControlStatus, i15.MatInput, i16.MatFormField, i16.MatLabel, i17.MatIcon, i18.MatButton, i19.MatSelect, i20.MatOption, i21.MatTab, i21.MatTabGroup, i22.BdoTableComponent, i23.NgForOf, i23.NgIf, i14.NgModel, i23.DatePipe],\n      styles: [\".search-title[_ngcontent-%COMP%] {\\n  font-size: 2em;\\n  color: #00779b;\\n  display: block;\\n}\\n\\n.display-flex-main[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100% !important;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  z-index: 0;\\n  position: relative;\\n  margin-right: 0.5em;\\n  min-height: 100% !important;\\n  max-width: 100% !important;\\n}\\n\\n.display-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.top-action-row-exchange[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  padding-top: 10px;\\n}\\n\\n.top-action-row-exchange-noalign[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n\\n.top-action-column-exchange[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: flex-start;\\n  margin-top: 1em;\\n}\\n\\n.margin-top[_ngcontent-%COMP%] {\\n  margin-top: 1.5em;\\n}\\n\\n.margin-left-push[_ngcontent-%COMP%] {\\n  margin-left: 30em;\\n}\\n\\n.margin-left[_ngcontent-%COMP%] {\\n  margin-left: 2em;\\n}\\n\\n.top-action-row-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.upload-import-container[_ngcontent-%COMP%] {\\n  display: flex; \\n\\n}\\n\\n.outside-mat-label[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n  margin-right: 10px;\\n  margin-top: 2em;\\n}\\n\\n.form-field-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.mat-form-field[_ngcontent-%COMP%] {\\n  margin-right: 10px; \\n\\n}\\n\\n.search-button-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  vertical-align: middle;\\n  height: 100px !important;\\n}\\n\\n@media (max-width: 1750px) {\\n  .outside-mat-label[_ngcontent-%COMP%] {\\n    font-size: 1.2em;\\n    margin-right: 10px;\\n    margin-top: 0em;\\n  }\\n  .search-button[_ngcontent-%COMP%] {\\n    margin-top: 1.9em;\\n  }\\n}\\n@media (max-width: 900px) {\\n  .outside-mat-label[_ngcontent-%COMP%] {\\n    font-size: 1.2em;\\n    margin-right: 10px;\\n  }\\n  .search-button[_ngcontent-%COMP%] {\\n    margin-top: 0em;\\n  }\\n}\\n.certificate-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  margin-right: 15px;\\n}\\n\\n@media (min-width: 768px) {\\n  .col-md-2-5[_ngcontent-%COMP%] {\\n    flex: 0 0 auto;\\n    max-width: 20.8333%;\\n  }\\n  .col-md-2-75[_ngcontent-%COMP%] {\\n    flex: 0 0 auto;\\n    max-width: 22.9166%;\\n  }\\n}\\n@media (min-width: 1750px) and (max-width: 3840px) {\\n  .search-button-m-top[_ngcontent-%COMP%] {\\n    margin-top: 3.9em;\\n  }\\n}\\n.outside-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-start;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["InformationExchangeStatus", "AppComponentBase", "Date<PERSON>elper", "BdoTableColumnType", "BdoTableData", "ExchangeReasonDic", "InformationExchangeStatusDic", "CTSUploadStatusDic", "CTSUploadExchangeReasonDic", "<PERSON><PERSON>", "InformationExchangeHistoryComponent", "UpdateCaCertificateDialogComponent", "ViewAssociatedExchangeRecordsComponent", "UploadHistoricalXmlDialogComponent", "DecryptDataPacketDialogComponent", "RegeneratePacketDialogComponent", "ViewCommentDialogComponent", "CTSUploadStatus", "UpdateCtsSettingDialogComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r0", "certificateExpirationDate", "ɵɵlistener", "InformationExchangeMainComponent_button_8_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "openUpdateCtsSettingDialog", "InformationExchangeMainComponent_button_9_Template_button_click_0_listener", "_r3", "openUpdateCaCertificateDialog", "InformationExchangeMainComponent_button_10_Template_button_click_0_listener", "_r4", "GenerateXMlByType", "InformationExchangeMainComponent_button_11_Template_button_click_0_listener", "_r5", "InformationExchangeMainComponent_button_12_Template_button_click_0_listener", "_r6", "InformationExchangeMainComponent_button_13_Template_button_click_0_listener", "_r7", "ɵɵproperty", "element_r8", "element_r9", "value", "description", "InformationExchangeMainComponent_mat_tab_38_button_6_Template_button_click_0_listener", "_r11", "openDecryptDialog", "InformationExchangeMainComponent_mat_tab_38_button_7_Template_button_click_0_listener", "_r12", "openUploadHistoricalXmlDialog", "InformationExchangeMainComponent_mat_tab_38_button_8_Template_button_click_0_listener", "_r13", "openCtsUploadDialog", "InformationExchangeMainComponent_mat_tab_38_button_9_Template_button_click_0_listener", "_r14", "refreshAllTransmissionStatus", "element_r15", "element_r16", "element_r17", "code2", "name", "element_r18", "ɵɵelement", "ɵɵtemplate", "InformationExchangeMainComponent_mat_tab_38_button_6_Template", "InformationExchangeMainComponent_mat_tab_38_button_7_Template", "InformationExchangeMainComponent_mat_tab_38_button_8_Template", "InformationExchangeMainComponent_mat_tab_38_button_9_Template", "ɵɵtwoWayListener", "InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_15_listener", "$event", "_r10", "ɵɵtwoWayBindingSet", "ctsUploadSelectedYear", "InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_15_listener", "onCtsUploadYearChange", "InformationExchangeMainComponent_mat_tab_38_mat_option_16_Template", "InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_21_listener", "selectExchangeReason", "InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_21_listener", "onCtsUploadExchangeReasonChange", "InformationExchangeMainComponent_mat_tab_38_mat_option_22_Template", "InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_27_listener", "selectReceivingCountry", "InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_27_listener", "onCtsUploadReceivingCountryChange", "InformationExchangeMainComponent_mat_tab_38_mat_option_28_Template", "InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_33_listener", "selectCtsUploadStatus", "InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_33_listener", "onCtsUploadStatusChange", "InformationExchangeMainComponent_mat_tab_38_mat_option_34_Template", "InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onLazyLoad_37_listener", "onCtsUploadLazyLoadEvent", "InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onLinkClick_37_listener", "onCtsUploadLinkClick", "InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onActionClick_37_listener", "onCtsUploadActionClick", "InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onCheckboxClick_37_listener", "onCheckboxClick", "ctsDashboardTableId", "ctsDashboardColumns", "currentPageIndexS", "PageSizeS", "showDecryptReceivedDataPacket", "showUploadHistoricalXml", "showCTSUpload", "showRefreshStatus", "ɵɵtwoWayProperty", "year", "ctsUploadExchangeReasonDic", "countries", "ctsUploadStatusDic", "ctsUploadTableId", "ctsUploadColumns", "ctsUploadCurrentPage", "ctsUploadPageSize", "ɵɵpureFunction0", "_c0", "InformationExchangeMainComponent", "constructor", "injector", "router", "informationExchangeService", "informationExchangeDetailService", "permissionService", "dashboardService", "dialog", "certificateService", "fileUploadService", "toasterService", "countryService", "ctsPackageRequestService", "<PERSON><PERSON><PERSON><PERSON>", "bahamasCtsSettingService", "input", "maxResultCount", "skip<PERSON><PERSON>nt", "sorting", "informationExchangeStatus", "None", "entityName", "TableId", "currentPageIndex", "selected<PERSON>ear", "Date", "getFullYear", "toString", "exchangeResultColumns", "columnId", "type", "String", "min<PERSON><PERSON><PERSON>", "frozenLeft", "isSortable", "columnName", "Link", "PageSize", "exchangeInformationResultRecords", "selectReportStatus", "informationExchangedDic", "TableIdS", "summaryExchangeColumns", "Number", "totalRecords", "showButton", "showOtherCase", "currnetYear", "bahamasCtsSetting", "isCaSystemAdmin", "ctsDashboardList", "id", "totalNotUploaded", "totalReadyForUpload", "totalFailedUpload", "totalUploadedToCTS", "totalNotEnrolled", "DateTime", "SingleActionButton", "Checkbox", "ctsUploadResultRecords", "NotStarted", "ctsUploadTotalRecords", "ctsUploadInput", "ctsUploadStatus", "exchangeReason", "financialEndYear", "receivingCountry", "showBahamasSettings", "showDataPacketDashboard", "showUpdateCACertificate", "showRegenerateDataPacket", "summaryExchangeList", "totalNoReport", "totalNoReportSent", "totalNoReportRExchange", "totalNoReportRReview", "totalNoReportNotSent", "ngOnInit", "getFiscalYears", "subscribe", "response", "length", "for<PERSON>ach", "element", "push", "localStorage", "getItem", "standardMonitoringFromYear", "standardMonitoringYear", "IsShowOtherCase", "onLazyLoadEvent", "undefined", "onLazyLoadEventS", "checkUserPermission", "currentUser", "configState", "getOne", "roles", "includes", "getGrantedPolicy", "getCountries", "getBahamasCertificateInfo", "getBahamasCtsSettingInfo", "onCtsDashboardLazyLoadEvent", "selectedYearAsInt", "parseInt", "standardMonitoringYearAsInt", "event", "getSummaryByYearByYear", "setTimeout", "setTableDataS", "pageSize", "pageNumber", "isAscending", "sortField", "getALLInformationExchangeByInput", "totalCount", "items", "setTableData", "tableData", "resetToFirstPage", "tableId", "data", "map", "x", "rawData", "cells", "totalNoofReports", "totalNoofExchangedReports", "totalNoofReadyExchangedReports", "totalNoofReviewReports", "totalNotSentReports", "tableService", "setGridData", "getExchangeReasonDescription", "found<PERSON><PERSON><PERSON>", "find", "status", "getInformationExchangeStatusDescription", "base64ToUint8Array", "raw", "atob", "<PERSON><PERSON><PERSON><PERSON>", "array", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "charCodeAt", "downloadFile", "content", "file", "Blob", "fileURL", "window", "URL", "createObjectURL", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "style", "display", "href", "download", "click", "remove", "exchangeType", "getXMLFilesFilterByExchangeTypeByReasonAndYear", "result", "fileName", "fire", "icon", "title", "text", "allowOutsideClick", "tabChanged", "index", "raCode", "companyFormationNumber", "fiscalEndDate", "dueDate", "isMigrated", "hasHistoryRecord", "onYearChange", "ob", "setItem", "searchEntityName", "onSearch", "onReportChange", "onLinkClick", "navigate", "queryParams", "ishistory", "declarationid", "declarationId", "entityid", "corporateEntityId", "from", "openInformationExchangeHistoryDialog", "informationExchangeId", "dialogRef", "open", "height", "width", "afterClosed", "console", "log", "pipe", "getCTSUploadStatusDescription", "getReceivingCountryName", "foundCountry", "getList", "uploadHistoricalCountries", "filter", "country", "trim", "unshift", "next", "info", "expiredAt", "error", "getCurrentSettings", "formData", "FormData", "append", "certificateFileFormData", "certificateFile", "isCreating", "serviceCall", "createBahamasCtsSettings", "updateBahamasCtsSettings", "action", "success", "life", "warn", "uploadBahamasCertificate", "password", "setCtsDashboardTableData", "setCtsUploadTableData", "dataPacket", "convertUtcToLocalDate", "fileCreationDate", "uploadedAt", "ctsTransmissionStatus", "viewExchangeRecords", "viewComments", "allowedActions", "actionType", "tooltip", "hide", "excludeFromCtsUpload", "getAllCtsPackageRequestByInput", "onCtsUploadSearch", "row", "downloadCtsDataPacket", "comment", "regeneratePackage", "ctsPackageId", "message", "fireDialog", "confirm", "uploadToCts", "isChecked", "markAsDoNotUpload", "rowId", "unMarkAsDoNotUpload", "receivingCountries", "fiscalYears", "batchUploadToCts", "unpackCtsPackage", "refreshTransmissionStatus", "downloadDataPacketFile", "ɵɵdirectiveInject", "Injector", "i1", "Router", "i2", "InformationExchangeService", "i3", "InformationExchangeDetailsService", "i4", "PermissionService", "i5", "CADashboardContorllerService", "i6", "MatDialog", "i7", "CertificateService", "i8", "FileUploadService", "i9", "ToasterService", "i10", "CountryService", "i11", "CtsPackageRequestService", "i12", "SweetAlertService", "i13", "BahamasCtsSettingService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "InformationExchangeMainComponent_Template", "rf", "ctx", "InformationExchangeMainComponent_Template_mat_tab_group_selectedTabChange_0_listener", "InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_4_listener", "InformationExchangeMainComponent_span_7_Template", "InformationExchangeMainComponent_button_8_Template", "InformationExchangeMainComponent_button_9_Template", "InformationExchangeMainComponent_button_10_Template", "InformationExchangeMainComponent_button_11_Template", "InformationExchangeMainComponent_button_12_Template", "InformationExchangeMainComponent_button_13_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_19_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_19_listener", "InformationExchangeMainComponent_mat_option_20_Template", "InformationExchangeMainComponent_Template_mat_select_valueChange_25_listener", "InformationExchangeMainComponent_Template_mat_select_selectionChange_25_listener", "InformationExchangeMainComponent_mat_option_26_Template", "InformationExchangeMainComponent_Template_input_ngModelChange_31_listener", "InformationExchangeMainComponent_Template_button_click_33_listener", "InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_37_listener", "InformationExchangeMainComponent_Template_bdo_table_onLinkClick_37_listener", "InformationExchangeMainComponent_mat_tab_38_Template"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\information-exchange-main\\information-exchange-main.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\information-exchange-main\\information-exchange-main.component.html"], "sourcesContent": ["import { Component, Injector, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { InformationExchangeService } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/controllers';\r\nimport {\r\n  GetInformationExchangeDto,\r\n  InformationExchangeDto,\r\n} from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/information-exchange-dashboard/dto';\r\nimport {\r\n  ExchangeReason,\r\n  InformationExchangeStatus,\r\n} from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\r\nimport { AppComponentBase } from '../../../../app-component-base';\r\nimport { DateHelper } from '../../../../shared/utils/date-helper';\r\nimport {\r\n  BdoTableCellLinkClickEvent,\r\n  BdoTableCheckboxClickEvent,\r\n  BdoTableColumnDefinition,\r\n  BdoTableColumnType,\r\n  BdoTableData,\r\n  BdoTableRowActionClickEvent,\r\n} from '../../../../shared/components/bdo-table/bdo-table.model';\r\nimport {\r\n  ExchangeReasonDic,\r\n  ExchangeSummaryTableColumns,\r\n  InformationExchangeStatusDic,\r\n  InformationExchangeTableColumns,\r\n  Permissions,\r\n  CTSUploadStatusDic,\r\n  CTSUploadExchangeReasonDic,\r\n} from '../../../../shared/constants';\r\nimport Swal from 'sweetalert2';\r\nimport { CurrentUserDto, PermissionService } from '@abp/ng.core';\r\nimport { InformationExchangeDetailsService } from '../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { InformationExchangeHistoryComponent } from '../information-exchange-history/information-exchange-history.component';\r\nimport { CADashboardContorllerService } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers';\r\nimport { UpdateCaCertificateDialogComponent } from '../update-ca-certificate-dialog/update-ca-certificate-dialog.component';\r\nimport { CertificateService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/certificate.service';\r\nimport { FileUploadService } from '../../../../shared/services/upload-file.service';\r\nimport { ToasterService } from '@abp/ng.theme.shared';\r\nimport { CountryService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';\r\nimport { CtsPackageRequestService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-request/cts-package-request.service';\r\nimport { ViewAssociatedExchangeRecordsComponent } from '../view-associated-exchange-records/view-associated-exchange-records.component';\r\nimport { UploadHistoricalXmlDialogComponent } from '../upload-historical-xml-dialog/upload-historical-xml-dialog.component';\r\nimport { DecryptDataPacketDialogComponent } from '../decrypt-data-packet-dialog/decrypt-data-packet-dialog.component';\r\nimport { RegeneratePacketDialogComponent } from '../regenerate-packet-dialog/regenerate-packet-dialog.component';\r\nimport { ViewCommentDialogComponent } from '../view-comment-dialog/view-comment-dialog.component';\r\nimport { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/enums/ctsupload-status.enum';\r\nimport { GetCtsPackageRequestDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/application/contracts/cts-package-requests';\r\nimport { SweetAlertService } from '@app/shared/services/sweetalert.service';\r\nimport { UploadCtsDialogComponent } from '../upload-cts-dialog/upload-cts-dialog.component';\r\nimport { UpdateCtsSettingDialogComponent } from '../update-cts-setting-dialog/update-cts-setting-dialog.component';\r\nimport { BahamasCtsSettingDto, BahamasCtsSettingService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings';\r\n\r\n@Component({\r\n  selector: 'app-information-exchange-main',\r\n  templateUrl: './information-exchange-main.component.html',\r\n  styleUrls: ['./information-exchange-main.component.scss']\r\n})\r\nexport class InformationExchangeMainComponent\r\n  extends AppComponentBase\r\n  implements OnInit {\r\n  input: GetInformationExchangeDto = {\r\n    maxResultCount: 10,\r\n    skipCount: 0,\r\n    sorting: 'ExchangeReason asc',\r\n    informationExchangeStatus: InformationExchangeStatus.None,\r\n    entityName: '',\r\n    year: '',\r\n  };\r\n  TableId = 'information_ex-results';\r\n  /* Work for pagination. Default value = 0, it is rendering first page by default. */\r\n  currentPageIndex = 0;\r\n  InformationExchange: InformationExchangeDto[];\r\n  /** It is string year number array. */\r\n  year = [];\r\n  /** Selected year from Financial Period End Years dropdown, default is current year. */\r\n  selectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default.\r\n  exchangeResultColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: InformationExchangeTableColumns.EXCHANGE_REASON,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: true,\r\n      columnName: 'Exchange Reason',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.RA_CODE,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'RA Name',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.ENTITY_NAME,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 100,\r\n      isSortable: true,\r\n      columnName: 'Entity Name',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.INCROP_NUMBER,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Incop#/Formation#',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.FINANCIAL_PERIOD,\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Financial Period End Date',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.DUE_DATE,\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Due Date',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.INFORMATIONEXCH_STATUS,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Information Exchange Status',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.VIEW_DECLARATION,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      columnName: 'View Declaration',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.XML_DATA,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      columnName: 'XML Data',\r\n    },\r\n    {\r\n      columnId: InformationExchangeTableColumns.VIEW_HISTORY,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      columnName: 'View History',\r\n    },\r\n  ];\r\n  /** Page size setting for \"TableId\" grid */\r\n  PageSize = 10;\r\n  exchangeInformationResultRecords = [];\r\n  selectReportStatus: number = InformationExchangeStatus.None;\r\n  searchEntityName: any;\r\n  informationExchangedDic: any = InformationExchangeStatusDic;\r\n  TableIdS = 'information_ex_summary';\r\n  currentPageIndexS = 0;\r\n  summaryExchangeColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_REPORT,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports XML Generated',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_READY,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports Ready for Exchange',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 200,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName:\r\n        'Total # of Reports for Review<br>(Include: Not required, Waiting for Review/Appeal)',\r\n    },\r\n    {\r\n      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: 'Total # of Reports Not Started',\r\n    },\r\n  ];\r\n  /** Page Size setting for \"TableIdS\" grid. Note: It is not the same as PageSize variable. Don't confuse. */\r\n  PageSizeS = 10;\r\n  totalRecords = 0;\r\n  /** Note: Only logon user with permission \"Generate XML\"\r\n   * is able to see the \"Non-compliance XML\",\"High Risk IP XML\",\"Non-resident XML\" buttons. */\r\n  showButton = true;\r\n  showOtherCase = true;\r\n  standardMonitoringYear: any;\r\n  /* Default current year value as string. */\r\n  currnetYear = new Date().getFullYear().toString();\r\n\r\n  certificateExpirationDate: string | null = null;\r\n  bahamasCtsSetting: BahamasCtsSettingDto | null = null;\r\n  isCaSystemAdmin: boolean = false;\r\n\r\n  // Dashboard columns for CTS Upload & Transmission\r\n  ctsDashboardColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: 'totalNotUploaded',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Do Not Upload',\r\n    },\r\n    {\r\n      columnId: 'totalReadyForUpload',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Ready For Upload',\r\n    },\r\n    {\r\n      columnId: 'totalFailedUpload',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Failed in Upload',\r\n    },\r\n    {\r\n      columnId: 'totalUploadedToCTS',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Uploaded to CTS',\r\n    },\r\n    {\r\n      columnId: 'totalNotEnrolled',\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: false,\r\n      columnName: '# of Packets Receiving Country Not Enrolled',\r\n    },\r\n  ];\r\n\r\n  // Dashboard data for CTS Upload & Transmission\r\n  ctsDashboardList: any[] = [\r\n    {\r\n      id: 1,\r\n      totalNotUploaded: 0,\r\n      totalReadyForUpload: 1,\r\n      totalFailedUpload: 1,\r\n      totalUploadedToCTS: 2,\r\n      totalNotEnrolled: 2,\r\n    },\r\n  ];\r\n\r\n  // Grid columns for CTS Upload & Transmission\r\n  ctsUploadColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: 'exchangeReason',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      frozenLeft: true,\r\n      isSortable: true,\r\n      columnName: 'Exchange Reason',\r\n    },\r\n    {\r\n      columnId: 'dataPacket',\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'Data Packet',\r\n    },\r\n    {\r\n      columnId: 'fileCreationDate',\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 120,\r\n      isSortable: true,\r\n      columnName: 'File Creation Date',\r\n    },\r\n    {\r\n      columnId: 'receivingCountry',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'Receiving Country',\r\n    },\r\n    {\r\n      columnId: 'ctsUploadStatus',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'CTS Upload Status',\r\n    },\r\n    {\r\n      columnId: 'uploadedAt',\r\n      type: BdoTableColumnType.DateTime,\r\n      minWidth: 120,\r\n      isSortable: true,\r\n      columnName: 'Uploaded At',\r\n    },\r\n    {\r\n      columnId: 'ctsTransmissionStatus',\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 120,\r\n      isSortable: false,\r\n      columnName: 'CTS Transmission Status',\r\n    },\r\n    {\r\n      columnId: 'viewExchangeRecords',\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'View Exchange Records',\r\n    },\r\n    {\r\n      columnId: 'viewComments',\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'View Comments',\r\n    },\r\n    {\r\n      columnId: 'regeneratePacket',\r\n      type: BdoTableColumnType.SingleActionButton,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'Regenerate Packet',\r\n    },\r\n    {\r\n      columnId: 'ctsUpload',\r\n      type: BdoTableColumnType.SingleActionButton,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'CTS Upload',\r\n    },\r\n    {\r\n      columnId: 'excludeFromCtsUpload',\r\n      type: BdoTableColumnType.Checkbox,\r\n      minWidth: 60,\r\n      isSortable: false,\r\n      columnName: 'Exclude From CTS Upload',\r\n    },\r\n  ];\r\n\r\n  // CTS Upload & Transmission Dashboard\r\n  ctsUploadExchangeReasonDic: any = CTSUploadExchangeReasonDic;\r\n  ctsUploadStatusDic: any = CTSUploadStatusDic;\r\n  countries: any;\r\n  uploadHistoricalCountries: any;\r\n  ctsUploadSelectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default. \r\n  ctsUploadResultRecords = [];\r\n  selectExchangeReason: number = -1;\r\n  selectCtsUploadStatus: number = CTSUploadStatus.NotStarted;\r\n  selectReceivingCountry: string = '';\r\n\r\n  // Table IDs and page settings\r\n  ctsDashboardTableId = 'cts_dashboard';\r\n  ctsUploadTableId = 'cts_upload_grid';\r\n  ctsUploadPageSize = 10;\r\n  ctsUploadCurrentPage = 0;\r\n  ctsUploadTotalRecords = 0;\r\n  ctsUploadInput: GetCtsPackageRequestDto = {\r\n    maxResultCount: 10,\r\n    skipCount: 0,\r\n    sorting: 'ExchangeReason asc',\r\n    ctsUploadStatus: null,\r\n    exchangeReason: null,\r\n    financialEndYear: '',\r\n    receivingCountry: '',\r\n  };\r\n  showBahamasSettings: boolean = false;\r\n  showDataPacketDashboard: boolean = false;\r\n  showUpdateCACertificate: boolean = false;\r\n  showCTSUpload: boolean = false;\r\n  showRefreshStatus: boolean = false;\r\n  showRegenerateDataPacket: boolean = false;\r\n  showDecryptReceivedDataPacket: boolean = false;\r\n  showUploadHistoricalXml: boolean = false;\r\n\r\n  constructor(\r\n    injector: Injector,\r\n    private router: Router,\r\n    private informationExchangeService: InformationExchangeService,\r\n    private informationExchangeDetailService: InformationExchangeDetailsService,\r\n    private permissionService: PermissionService,\r\n    private dashboardService: CADashboardContorllerService,\r\n    public dialog: MatDialog,\r\n    private certificateService: CertificateService,\r\n    private fileUploadService: FileUploadService,\r\n    private toasterService: ToasterService,\r\n    private countryService: CountryService,\r\n    private ctsPackageRequestService: CtsPackageRequestService,\r\n    private sweetAlert: SweetAlertService,\r\n    private bahamasCtsSettingService: BahamasCtsSettingService,\r\n  ) {\r\n    super(injector);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getFiscalYears().subscribe((response) => {\r\n      if (response && response.length > 0) {\r\n        this.year = [];\r\n        response.forEach((element) => {\r\n          this.year.push(element.toString());\r\n        });\r\n      };\r\n    });\r\n\r\n    if (localStorage.getItem('selectedYear')) {\r\n      this.selectedYear =\r\n        localStorage.getItem('selectedYear') ?? this.currnetYear;\r\n    }\r\n\r\n    if (localStorage.getItem('selectReportStatus')) {\r\n      this.selectReportStatus = Number(\r\n        localStorage.getItem('selectReportStatus')\r\n      );\r\n    }\r\n    this.informationExchangeDetailService\r\n      .standardMonitoringFromYear()\r\n      .subscribe((response) => {\r\n        this.standardMonitoringYear = response;\r\n\r\n        this.IsShowOtherCase(this.selectedYear);\r\n      });\r\n\r\n    this.onLazyLoadEvent(undefined);\r\n    this.onLazyLoadEventS(undefined);\r\n\r\n    this.showButton = this.checkUserPermission();\r\n\r\n    // CTS Upload & Transmission Dashboard\r\n    // Check CA System Admin role\r\n    const currentUser = this.configState.getOne('currentUser') as CurrentUserDto;\r\n    this.isCaSystemAdmin = !!currentUser?.roles?.includes('CA System Admin');\r\n\r\n    if (localStorage.getItem('ctsUploadSelectedYear')) {\r\n      this.ctsUploadSelectedYear =\r\n        localStorage.getItem('ctsUploadSelectedYear') ?? this.currnetYear;\r\n    }\r\n\r\n    if (localStorage.getItem('selectExchangeReason')) {\r\n      this.selectExchangeReason = Number(\r\n        localStorage.getItem('selectExchangeReason')\r\n      );\r\n    }\r\n\r\n    if (localStorage.getItem('selectCtsUploadStatus')) {\r\n      this.selectCtsUploadStatus = Number(\r\n        localStorage.getItem('selectCtsUploadStatus')\r\n      );\r\n    }\r\n\r\n    if (localStorage.getItem('selectReceivingCountry')) {\r\n      this.selectReceivingCountry = localStorage.getItem('selectReceivingCountry');\r\n    }\r\n\r\n    this.showBahamasSettings = this.permissionService.getGrantedPolicy(Permissions.BAHAMAS_CTS_SETTING);\r\n    this.showDataPacketDashboard = this.permissionService.getGrantedPolicy(Permissions.DATA_PACKET_DASHBOARD);\r\n    this.showUpdateCACertificate = this.permissionService.getGrantedPolicy(Permissions.UPDATE_CA_CERTIFICATE);\r\n    this.showCTSUpload = this.permissionService.getGrantedPolicy(Permissions.CTS_UPLOAD);\r\n    this.showRefreshStatus = this.permissionService.getGrantedPolicy(Permissions.REFRESH_STATUS);\r\n    this.showRegenerateDataPacket = this.permissionService.getGrantedPolicy(Permissions.REGENERATE_DATA_PACKET);\r\n    this.showDecryptReceivedDataPacket = this.permissionService.getGrantedPolicy(Permissions.DECRYPT_RECEIVED_DATA_PACKET);\r\n    this.showUploadHistoricalXml = this.permissionService.getGrantedPolicy(Permissions.UPLOAD_HISTORICAL_XML);\r\n\r\n    this.getCountries();\r\n    if (this.showUpdateCACertificate) {\r\n      this.getBahamasCertificateInfo();\r\n    }\r\n    if (this.showBahamasSettings) {\r\n      this.getBahamasCtsSettingInfo();\r\n    }\r\n    if (this.showDataPacketDashboard) {\r\n      this.onCtsUploadLazyLoadEvent(undefined);\r\n      this.onCtsDashboardLazyLoadEvent(undefined);\r\n    }\r\n  }\r\n\r\n  IsShowOtherCase(year: string) {\r\n    const selectedYearAsInt: number = parseInt(year, 10);\r\n    const standardMonitoringYearAsInt: number = parseInt(\r\n      this.standardMonitoringYear,\r\n      10\r\n    );\r\n    this.showOtherCase =\r\n      selectedYearAsInt <= standardMonitoringYearAsInt ? true : false;\r\n  }\r\n\r\n  /** Lazy load event works for \"TableIds\" grid only. */\r\n  onLazyLoadEventS(event): void {\r\n    this.currentPageIndexS = 0;\r\n    this.informationExchangeService\r\n      .getSummaryByYearByYear(this.selectedYear)\r\n      .subscribe((response) => {\r\n        this.summaryExchangeList = response;\r\n        setTimeout(() => {\r\n          this.setTableDataS();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  /** Lazy load event works for grid \"TableId\" only. */\r\n  onLazyLoadEvent(event): void {\r\n    if (event) {\r\n      if (this.PageSize === (event.pageSize ?? 10)) {\r\n        this.currentPageIndex = event.pageNumber ?? 0;\r\n      } else {\r\n        //\r\n        // if Page size got changed through pagination control,\r\n        // need to reset current page index to 0.\r\n        //\r\n        this.PageSize = event.pageSize ?? 10;\r\n        this.currentPageIndex = 0;\r\n      }\r\n\r\n      this.input.skipCount =\r\n        (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\r\n\r\n      this.input.maxResultCount = this.PageSize ?? 10;\r\n\r\n      if (event.isAscending === false) {\r\n        this.input.sorting = `${event.sortField} desc`;\r\n      } else {\r\n        this.input.sorting = `${event.sortField} asc`;\r\n      }\r\n    } else {\r\n      this.currentPageIndex = 0;\r\n      this.PageSize = 10;\r\n      this.input.informationExchangeStatus = this.selectReportStatus;\r\n      this.input.year = this.selectedYear;\r\n      this.input.skipCount = 0;\r\n      this.input.maxResultCount = this.PageSize;\r\n    }\r\n\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        if (response) {\r\n          this.totalRecords = response.totalCount;\r\n          this.exchangeInformationResultRecords = response.items;\r\n        } else {\r\n          this.totalRecords = 0;\r\n          this.exchangeInformationResultRecords = [];\r\n        }\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  summaryExchangeList: any[] = [\r\n    {\r\n      id: 1,\r\n      totalNoReport: 100,\r\n      totalNoReportSent: 10,\r\n      totalNoReportRExchange: 5,\r\n      totalNoReportRReview: 2,\r\n      totalNoReportNotSent: 5,\r\n    },\r\n  ];\r\n\r\n  setTableDataS(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.TableIdS;\r\n    tableData.totalRecords = 1;\r\n    tableData.data = this.summaryExchangeList.map((x) => {\r\n      return {\r\n        id: x.id,\r\n        rawData: x,\r\n        cells: [\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_REPORT,\r\n            value: x.totalNoofReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT,\r\n            value: x.totalNoofExchangedReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_READY,\r\n            value: x.totalNoofReadyExchangedReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW,\r\n            value: x.totalNoofReviewReports,\r\n          },\r\n          {\r\n            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT,\r\n            value: x.totalNotSentReports,\r\n          },\r\n        ],\r\n      };\r\n    });\r\n    setTimeout(() => {\r\n      this.tableService.setGridData(tableData);\r\n    }, 10);\r\n  }\r\n\r\n  getExchangeReasonDescription(input: any) {\r\n    const foundStatus = ExchangeReasonDic.find(\r\n      (status) => status.value === input\r\n    );\r\n    if (foundStatus) return foundStatus.description;\r\n    return '';\r\n  }\r\n\r\n  getInformationExchangeStatusDescription(input: any) {\r\n    const foundStatus = InformationExchangeStatusDic.find(\r\n      (status) => status.value === input\r\n    );\r\n    if (foundStatus) return foundStatus.description;\r\n    return '';\r\n  }\r\n\r\n  base64ToUint8Array(x: string) {\r\n    const raw = atob(x);\r\n    var rawLength = raw.length;\r\n    var array = new Uint8Array(new ArrayBuffer(rawLength));\r\n\r\n    for (let i = 0; i < rawLength; i++) {\r\n      array[i] = raw.charCodeAt(i);\r\n    }\r\n    return array;\r\n  }\r\n\r\n  downloadFile(content: string, name: string) {\r\n    var file = new Blob([this.base64ToUint8Array(content)]);\r\n    var fileURL = window.URL.createObjectURL(file);\r\n\r\n    var element = document.createElement('a');\r\n    document.body.appendChild(element);\r\n    element.style.display = 'none';\r\n    element.href = fileURL;\r\n    element.download = name;\r\n    element.click();\r\n    element.remove();\r\n  }\r\n \r\n\r\n  GenerateXMlByType(exchangeType: ExchangeReason) {\r\n    this.informationExchangeService\r\n      .getXMLFilesFilterByExchangeTypeByReasonAndYear(\r\n        exchangeType,\r\n        this.selectedYear\r\n      )\r\n      .subscribe((result) => {\r\n        this.onLazyLoadEvent(undefined);\r\n        if (result.fileName != '') {\r\n          this.downloadFile(result.content.toString(), result.fileName);\r\n        } else\r\n          Swal.fire({\r\n            icon: 'info',\r\n            title: 'XML Import',\r\n            text: 'No data to export.',\r\n            allowOutsideClick: false,\r\n          });\r\n      });\r\n  }\r\n\r\n  tabChanged(event: any) {\r\n    if (event.index === 1) {\r\n      if (this.showDataPacketDashboard) {\r\n        this.onCtsUploadLazyLoadEvent(undefined);\r\n        this.onCtsDashboardLazyLoadEvent(undefined);\r\n      }\r\n    }\r\n  }\r\n\r\n  setTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.TableId;\r\n    tableData.totalRecords = this.totalRecords;\r\n    tableData.data = this.exchangeInformationResultRecords.map((x) => {\r\n      return {\r\n        id: x.id,\r\n        rawData: x,\r\n        cells: [\r\n          {\r\n            columnId: InformationExchangeTableColumns.EXCHANGE_REASON,\r\n            value: this.getExchangeReasonDescription(x.exchangeReason),\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.RA_CODE,\r\n            value: x.raCode,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.ENTITY_NAME,\r\n            value: x.entityName,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.INCROP_NUMBER,\r\n            value: x.companyFormationNumber,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.FINANCIAL_PERIOD,\r\n            value: x.fiscalEndDate,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.DUE_DATE,\r\n            value: x.dueDate,\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.INFORMATIONEXCH_STATUS,\r\n            value: this.getInformationExchangeStatusDescription(\r\n              x.informationExchangeStatus\r\n            ),\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.VIEW_DECLARATION,\r\n            /** If the underneath data \"IsMigrated\" flag is true, then disable the link, otherwise enable the link to view declaration page */\r\n            value: x.isMigrated === false ? 'view' : '',\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.XML_DATA,\r\n            value: 'XML Data',\r\n          },\r\n          {\r\n            columnId: InformationExchangeTableColumns.VIEW_HISTORY,\r\n            value: x.hasHistoryRecord ? 'View History' : '',\r\n          },\r\n        ],\r\n      };\r\n    });\r\n    setTimeout(() => {\r\n      this.tableService.setGridData(tableData);\r\n    }, 100);\r\n  }\r\n\r\n  onYearChange(ob) {\r\n    this.selectedYear = ob.value;\r\n    this.IsShowOtherCase(this.selectedYear);\r\n    // Keep the selected Year in local storage.\r\n    localStorage.setItem('selectedYear', ob.value);\r\n\r\n    this.input.informationExchangeStatus = this.selectReportStatus;\r\n    this.input.year = this.selectedYear;\r\n    this.input.entityName = this.searchEntityName;\r\n    this.input.skipCount = 0;\r\n    this.input.maxResultCount = this.PageSize;\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        this.totalRecords = response.totalCount;\r\n        this.exchangeInformationResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n\r\n    this.onLazyLoadEventS(undefined);\r\n  }\r\n\r\n  onSearch() {\r\n    this.input.informationExchangeStatus = this.selectReportStatus;\r\n    this.input.year = this.selectedYear;\r\n    this.input.entityName = this.searchEntityName;\r\n    this.input.skipCount = 0;\r\n    this.input.maxResultCount = this.PageSize;\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        this.totalRecords = response.totalCount;\r\n        this.exchangeInformationResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n\r\n    this.onLazyLoadEventS(undefined);\r\n  }\r\n\r\n  onReportChange(ob) {\r\n    this.selectReportStatus = Number(ob.value);\r\n    this.input.informationExchangeStatus = this.selectReportStatus;\r\n    this.input.year = this.selectedYear;\r\n    this.input.entityName = this.searchEntityName;\r\n    this.input.skipCount = 0;\r\n    this.input.maxResultCount = this.PageSize;\r\n\r\n    // Keep the selected Report Status in local storage.\r\n    localStorage.setItem('selectReportStatus', ob.value);\r\n\r\n    this.informationExchangeService\r\n      .getALLInformationExchangeByInput(this.input)\r\n      .subscribe((response) => {\r\n        this.totalRecords = response.totalCount;\r\n        this.exchangeInformationResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onLinkClick(event: BdoTableCellLinkClickEvent) {\r\n    const data = event.rawData as InformationExchangeDto;\r\n    if (event.columnId === InformationExchangeTableColumns.XML_DATA) {\r\n      //\r\n      // Note: /es-info-exchange/exchangedetail page is shared with \"XML Data\" view which parameter \"id\" = \"Id\" of table dbo.InformationExchanges,\r\n      // and \"Information Exchange History Page\" view, which paramter \"id\" = \"InformationExchangeDetailId\" of table dbo.InformationExchangeHistories.\r\n      //\r\n      this.router.navigate(['/es-info-exchange/exchangedetail'], {\r\n        //\r\n        // Passed \"Id\" of table dbo.InformationExchanges.\r\n        //\r\n        queryParams: { id: data.id, ishistory: false },\r\n      });\r\n    } else if (\r\n      event.columnId === InformationExchangeTableColumns.VIEW_DECLARATION\r\n    ) {\r\n      //\r\n      // When click the \"view\" link button in the Information Exchange records grid.\r\n      // Route to CaActionPageComponent.ts component\r\n      //\r\n      this.router.navigate(['/action-page'], {\r\n        queryParams: {\r\n          declarationid: data.declarationId,\r\n          entityid: data.corporateEntityId,\r\n          from: 'info-exchange',\r\n        },\r\n      });\r\n    } else if (\r\n      event.columnId === InformationExchangeTableColumns.VIEW_HISTORY\r\n    ) {\r\n      //\r\n      // Open dialog to show history records. informantion-exchange-history.component.ts\r\n      //\r\n      this.openInformationExchangeHistoryDialog(data.id);\r\n    }\r\n  }\r\n\r\n  openInformationExchangeHistoryDialog(informationExchangeId: string) {\r\n    const dialogRef = this.dialog.open(InformationExchangeHistoryComponent, {\r\n      height: '750px',\r\n      width: '1200px',\r\n      data: { informationExchangeId: informationExchangeId },\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      console.log('The dialog was closed', result);\r\n    });\r\n  }\r\n\r\n  /** Check if logon user has permission \"Generate XML\".\r\n   *  Work for show/hide three xml buttons.\r\n   */\r\n  checkUserPermission() {\r\n    let result = false;\r\n    // Get current logon user object.\r\n    const currentUser = this.configState.getOne(\r\n      'currentUser'\r\n    ) as CurrentUserDto;\r\n\r\n    if (currentUser) {\r\n      result = this.permissionService.getGrantedPolicy(\r\n        Permissions.DASHBOARD_GENERATE_XML\r\n      );\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */\r\n  getFiscalYears() {\r\n    return this.dashboardService.getFiscalYears().pipe();\r\n  }\r\n\r\n\r\n  // CTS Upload & Transmission Dashboard Methods\r\n  getCTSUploadStatusDescription(input: any) {\r\n    const foundStatus = CTSUploadStatusDic.find(\r\n      (status) => status.value === input\r\n    );\r\n    if (foundStatus) return foundStatus.description;\r\n    return '';\r\n  }\r\n\r\n  getReceivingCountryName(input: any) {\r\n    const foundCountry = this.countries.find(\r\n      (status) => status.code2 === input\r\n    );\r\n    if (foundCountry) return foundCountry.name;\r\n    return '';\r\n  }\r\n\r\n  getCountries() {\r\n    this.countryService.getList({ sorting: \"name asc\", maxResultCount: 1000 }).subscribe(response => {\r\n      this.countries = response.items;\r\n      this.uploadHistoricalCountries = response.items;\r\n      // Remove code2 with empty string and null values in countries      \r\n      this.countries = this.countries.filter(country => country.code2 && country.code2.trim() !== '');\r\n      this.uploadHistoricalCountries = this.uploadHistoricalCountries.filter(country => country.code2 && country.code2.trim() !== '');\r\n      // add new country ALL in countries \r\n      this.countries.unshift({ name: 'All', code2: '' });\r\n    });\r\n  }\r\n\r\n  getBahamasCertificateInfo() {\r\n    return this.certificateService.getBahamasCertificateInfo().subscribe({\r\n      next: (info) => {\r\n        this.certificateExpirationDate = info?.expiredAt || null;\r\n      },\r\n      error: () => {\r\n        this.certificateExpirationDate = null;\r\n      }\r\n    });\r\n  }\r\n\r\n  getBahamasCtsSettingInfo() {\r\n    return this.bahamasCtsSettingService.getCurrentSettings().subscribe({\r\n      next: (info) => {\r\n        this.bahamasCtsSetting = info || null;\r\n      },\r\n      error: () => {\r\n        this.bahamasCtsSetting = null;\r\n      }\r\n    });\r\n  }\r\n\r\n  openUpdateCtsSettingDialog() {\r\n    const dialogRef = this.dialog.open(UpdateCtsSettingDialogComponent, {\r\n      width: '1200px',\r\n      data: this.bahamasCtsSetting || null\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (!result) return;\r\n\r\n      const formData: FormData = new FormData();\r\n      if (result.file) {\r\n        formData.append('fileName', result.file.name);\r\n        formData.append('file', result.file);\r\n        formData.append('fileType', result.file.type);\r\n      }\r\n\r\n      const certificateFileFormData: FormData = new FormData();\r\n      if (result.certificateFile) {\r\n        certificateFileFormData.append('fileName', result.certificateFile.name);\r\n        certificateFileFormData.append('file', result.certificateFile);\r\n        certificateFileFormData.append('fileType', result.certificateFile.type);\r\n      }\r\n\r\n      const isCreating = result.id == null;\r\n      const serviceCall = isCreating\r\n        ? this.fileUploadService.createBahamasCtsSettings(result, formData, certificateFileFormData)\r\n        : this.fileUploadService.updateBahamasCtsSettings(result, formData, certificateFileFormData);\r\n\r\n      const action = isCreating ? 'create' : 'update';\r\n\r\n      serviceCall.subscribe({\r\n        next: (response) => {\r\n          if (response) {\r\n            this.getBahamasCtsSettingInfo();\r\n            this.toasterService.success(`CTS Settings successfully ${action}d.`, '', { life: 5000 });\r\n          } else {\r\n            this.toasterService.warn(`CTS Settings could not be ${action}d. Please try again later.`, null, { life: 7000 });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.toasterService.error(`An error occurred while trying to ${action} CTS Settings.`, null, { life: 5000 });\r\n          console.error(`Error ${action}ing CTS Settings:`, error);\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  openUpdateCaCertificateDialog() {\r\n    const dialogRef = this.dialog.open(UpdateCaCertificateDialogComponent, {\r\n      width: '500px',\r\n      data: {},\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (result && result.file) {\r\n        const formData: FormData = new FormData();\r\n        formData.append('fileName', result.file.name);\r\n        formData.append('file', result.file);\r\n        formData.append('fileType', result.file.type);\r\n        // Only call upload if file is present\r\n        this.fileUploadService\r\n          .uploadBahamasCertificate(formData, result.password).subscribe({\r\n            next: (response) => {\r\n              if (response) {\r\n                // Fetch certificate expiration date\r\n                this.getBahamasCertificateInfo();\r\n                this.toasterService.success('Bahamas Certificate successfully uploaded', '', { life: 5000 });\r\n              }\r\n              else {\r\n                this.toasterService.warn('Bahamas Certificate couldn’t be uploaded. Please try again later', null, { life: 7000 });\r\n              }\r\n            },\r\n            error: (error) => {\r\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n              console.error('Error uploading Bahamas certificate:', error);\r\n            }\r\n          })\r\n      }\r\n    });\r\n  }\r\n\r\n  // CTS Upload & Transmission Dashboard Methods\r\n  setCtsDashboardTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.ctsDashboardTableId;\r\n    tableData.totalRecords = 1;\r\n    tableData.data = this.ctsDashboardList.map(x => ({\r\n      id: x.id,\r\n      rawData: x,\r\n      cells: [\r\n        { columnId: 'totalNotUploaded', value: x.totalNotUploaded },\r\n        { columnId: 'totalReadyForUpload', value: x.totalReadyForUpload },\r\n        { columnId: 'totalFailedUpload', value: x.totalFailedUpload },\r\n        { columnId: 'totalUploadedToCTS', value: x.totalUploadedToCTS },\r\n        { columnId: 'totalNotEnrolled', value: x.totalNotEnrolled },\r\n      ],\r\n    }));\r\n    setTimeout(() => this.tableService.setGridData(tableData), 10);\r\n  }\r\n\r\n  setCtsUploadTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.ctsUploadTableId;\r\n    tableData.totalRecords = this.ctsUploadTotalRecords;\r\n    tableData.data = this.ctsUploadResultRecords.map(x => ({\r\n      id: x.id,\r\n      rawData: x,\r\n      cells: [\r\n        { columnId: 'exchangeReason', value: this.getExchangeReasonDescription(x.exchangeReason) },\r\n        { columnId: 'dataPacket', value: x.dataPacket },\r\n        { columnId: 'fileCreationDate', value: DateHelper.convertUtcToLocalDate(x.fileCreationDate) },\r\n        { columnId: 'receivingCountry', value: this.getReceivingCountryName(x.receivingCountry) },\r\n        { columnId: 'ctsUploadStatus', value: this.getCTSUploadStatusDescription(x.ctsUploadStatus) },\r\n        { columnId: 'uploadedAt', value: DateHelper.convertUtcToLocalDate(x.uploadedAt) },\r\n        { columnId: 'ctsTransmissionStatus', value: x.ctsTransmissionStatus },\r\n        { columnId: 'viewExchangeRecords', value: x.viewExchangeRecords === true ? 'View' : null },\r\n        { columnId: 'viewComments', value: x.viewComments.length > 0 ? 'View Comments' : null },\r\n        {\r\n          columnId: 'regeneratePacket',\r\n          value: this.showRegenerateDataPacket && x.allowedActions?.includes(0)\r\n            ? { actionType: 'RegenerateDataPacket', icon: 'refresh', tooltip: \"Regenerate Packet\" }\r\n            : null\r\n        },\r\n        {\r\n          columnId: 'ctsUpload',\r\n          value: this.showCTSUpload && x.allowedActions?.includes(1)\r\n            ? { actionType: 'ctsUpload', icon: 'cloud_upload', tooltip: \"CTS Upload\" }\r\n            : null\r\n        },\r\n        {\r\n          columnId: 'excludeFromCtsUpload',\r\n          hide: x.allowedActions?.includes(2) || x.allowedActions?.includes(3) ? false : true,\r\n          value: x.excludeFromCtsUpload\r\n        }\r\n      ],\r\n    }));\r\n\r\n    setTimeout(() => this.tableService.setGridData(tableData), 100);\r\n  }\r\n\r\n  onCtsDashboardLazyLoadEvent(event): void {\r\n    this.currentPageIndexS = 0;\r\n    this.ctsPackageRequestService\r\n      .getSummaryByYearByYear(this.ctsUploadSelectedYear)\r\n      .subscribe((response) => {\r\n        this.ctsDashboardList = response;\r\n        setTimeout(() => {\r\n          this.setCtsDashboardTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onCtsUploadLazyLoadEvent(event): void {\r\n    if (event) {\r\n      if (this.ctsUploadPageSize === (event.pageSize ?? 10)) {\r\n        this.ctsUploadCurrentPage = event.pageNumber ?? 0;\r\n      } else {\r\n        //\r\n        // if Page size got changed through pagination control,\r\n        // need to reset current page index to 0.\r\n        //\r\n        this.ctsUploadPageSize = event.pageSize ?? 10;\r\n        this.ctsUploadCurrentPage = 0;\r\n      }\r\n\r\n      this.ctsUploadInput.skipCount =\r\n        (this.ctsUploadCurrentPage ?? 0) * (this.ctsUploadPageSize ?? 10);\r\n\r\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize ?? 10;\r\n\r\n      if (event.isAscending === false) {\r\n        this.ctsUploadInput.sorting = `${event.sortField} desc`;\r\n      } else {\r\n        this.ctsUploadInput.sorting = `${event.sortField} asc`;\r\n      }\r\n    } else {\r\n      this.ctsUploadCurrentPage = 0;\r\n      this.ctsUploadPageSize = 10;\r\n      this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\r\n      this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\r\n      this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\r\n      this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\r\n      this.ctsUploadInput.skipCount = 0;\r\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\r\n    }\r\n\r\n    this.ctsPackageRequestService\r\n      .getAllCtsPackageRequestByInput(this.ctsUploadInput)\r\n      .subscribe((response) => {\r\n        if (response) {\r\n          this.ctsUploadTotalRecords = response.totalCount;\r\n          this.ctsUploadResultRecords = response.items;\r\n        } else {\r\n          this.ctsUploadTotalRecords = 0;\r\n          this.ctsUploadResultRecords = [];\r\n        }\r\n\r\n        setTimeout(() => {\r\n          this.setCtsUploadTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onCtsUploadYearChange(ob) {\r\n    this.ctsUploadSelectedYear = ob.value;\r\n    localStorage.setItem('ctsUploadSelectedYear', ob.value);\r\n    this.onCtsUploadSearch();\r\n    this.onCtsDashboardLazyLoadEvent(undefined);\r\n  }\r\n\r\n  onCtsUploadSearch() {\r\n    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\r\n    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\r\n    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\r\n    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\r\n    this.ctsUploadInput.skipCount = 0;\r\n    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\r\n    this.ctsPackageRequestService\r\n      .getAllCtsPackageRequestByInput(this.ctsUploadInput)\r\n      .subscribe((response) => {\r\n        this.ctsUploadTotalRecords = response.totalCount;\r\n        this.ctsUploadResultRecords = response.items;\r\n\r\n        setTimeout(() => {\r\n          this.setCtsUploadTableData();\r\n        }, 200);\r\n      });\r\n  }\r\n\r\n  onCtsUploadExchangeReasonChange(ob) {\r\n    this.selectExchangeReason = Number(ob.value);\r\n    // Keep the selected Exchange Reason in local storage.\r\n    localStorage.setItem('selectExchangeReason', ob.value);\r\n    this.onCtsUploadSearch();\r\n  }\r\n\r\n  onCtsUploadStatusChange(ob) {\r\n    this.selectCtsUploadStatus = Number(ob.value);\r\n    // Keep the selected Upload Status in local storage.\r\n    localStorage.setItem('selectCtsUploadStatus', ob.value);\r\n    this.onCtsUploadSearch();\r\n  }\r\n\r\n  onCtsUploadReceivingCountryChange(ob) {\r\n    this.selectReceivingCountry = ob.value;\r\n    // Keep the selected Receiving Country in local storage.\r\n    localStorage.setItem('selectReceivingCountry', ob.value);\r\n    this.onCtsUploadSearch();\r\n  }\r\n\r\n  onCtsUploadLinkClick(event: BdoTableCellLinkClickEvent) {\r\n    if (event.columnId === 'viewExchangeRecords') {\r\n      this.dialog.open(ViewAssociatedExchangeRecordsComponent, {\r\n        width: '1200px',\r\n        data: {\r\n          row: event.rawData\r\n        }\r\n      });\r\n    }\r\n    else if (event.columnId === 'viewComments') {\r\n      this.dialog.open(ViewCommentDialogComponent, {\r\n        width: '1200px',\r\n        data: {\r\n          row: event.rawData\r\n        }\r\n      });\r\n    }\r\n    else if (event.columnId === 'dataPacket') {\r\n      this.downloadCtsDataPacket(event.rawData);\r\n    }\r\n  }\r\n\r\n  onCtsUploadActionClick(event: BdoTableRowActionClickEvent) {\r\n    if (event.action === 'RegenerateDataPacket') {\r\n      const dialogRef = this.dialog.open(RegeneratePacketDialogComponent, {\r\n        width: '500px',\r\n        data: {\r\n          row: event?.data?.rawData\r\n        }\r\n      });\r\n\r\n      dialogRef.afterClosed().subscribe((result) => {\r\n        if (result && result.comment) {\r\n          this.ctsPackageRequestService\r\n            .regeneratePackage(event.data?.rawData?.ctsPackageId, result.comment).subscribe({\r\n              next: (response) => {\r\n                if (response.success) {\r\n                  this.toasterService.success(response.message || 'Regenerate Package successfully requested', '', { life: 5000 });\r\n                }\r\n                else {\r\n                  this.toasterService.warn(response.message || 'Regenerate Package couldn’t be requested. Please try again later', null, { life: 7000 });\r\n                }\r\n                this.onCtsUploadLazyLoadEvent(undefined);\r\n                this.onCtsDashboardLazyLoadEvent(undefined);\r\n              },\r\n              error: (error) => {\r\n                //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n                console.error('Error requesting Regenerate Package:', error);\r\n              }\r\n            })\r\n        }\r\n      });\r\n    }\r\n    else if (event.action === 'ctsUpload') {\r\n      this.sweetAlert.fireDialog({\r\n        action: \"submit\",\r\n        title: \"CTS Upload\",\r\n        text: \"Are you sure you would like to proceed?\",\r\n        type: \"confirm\"\r\n      }, (confirm) => {\r\n        if (confirm) {\r\n          this.ctsPackageRequestService.uploadToCts(event.data?.rawData?.ctsPackageId).subscribe({\r\n            next: (response) => {\r\n              if (response.success) {\r\n                this.toasterService.success(response.message || 'CTS Upload successfully completed', '', { life: 5000 });\r\n              }\r\n              else {\r\n                this.toasterService.warn(response.message || 'CTS Upload couldn’t be completed. Please try again later', null, { life: 7000 });\r\n              }\r\n              this.onCtsUploadLazyLoadEvent(undefined);\r\n              this.onCtsDashboardLazyLoadEvent(undefined);\r\n            },\r\n            error: (error) => {\r\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n              console.error('Error requesting CTS Upload:', error);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  onCheckboxClick(event: BdoTableCheckboxClickEvent) {\r\n    // Commented out: console.log('Checkbox clicked:', event.selectedRows[0]?.rawData?.ctsPackageId);\r\n    // Commented out: console.log('Checkbox clicked:', event.rowId);\r\n    if (event.isChecked) {\r\n      this.ctsPackageRequestService.markAsDoNotUpload(event.rowId).subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.toasterService.success(response.message || 'Exclude packet from uploading successfully completed', '', { life: 5000 });\r\n          }\r\n          else {\r\n            this.toasterService.warn(response.message || 'Exclude packet from uploading  couldn’t be completed. Please try again later', null, { life: 7000 });\r\n          }\r\n          this.onCtsUploadLazyLoadEvent(undefined);\r\n          this.onCtsDashboardLazyLoadEvent(undefined);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error marking as Do Not Upload:', error);\r\n        }\r\n      });\r\n    } else {\r\n      this.ctsPackageRequestService.unMarkAsDoNotUpload(event.rowId).subscribe({\r\n        next: (response) => {\r\n          if (response.success) {\r\n            this.toasterService.success(response.message || 'Include packet for uploading successfully completed', '', { life: 5000 });\r\n          }\r\n          else {\r\n            this.toasterService.warn(response.message || 'Include packet for uploading couldn’t be completed. Please try again later', null, { life: 7000 });\r\n          }\r\n          this.onCtsUploadLazyLoadEvent(undefined);\r\n          this.onCtsDashboardLazyLoadEvent(undefined);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error unmarking as Do Not Upload:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Add this method to open the Upload Historical XML dialog\r\n  openUploadHistoricalXmlDialog() {\r\n    const dialogRef = this.dialog.open(UploadHistoricalXmlDialogComponent, {\r\n      width: '500px',\r\n      data: {\r\n        receivingCountries: this.uploadHistoricalCountries || [],\r\n        fiscalYears: this.year || []\r\n      }\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (result && result.success) {\r\n        this.onCtsUploadLazyLoadEvent(undefined);\r\n        this.onCtsDashboardLazyLoadEvent(undefined);\r\n      }\r\n    });\r\n  }\r\n\r\n  openCtsUploadDialog() {\r\n    this.sweetAlert.fireDialog({\r\n      action: \"submit\",\r\n      title: \"CTS Upload\",\r\n      text: `Are you sure you want to upload data packets with a Financial Period End in ${this.ctsUploadSelectedYear}? Once confirmed, all data packets with a status of Not Started for the year will be uploaded to the CTS platform.`,\r\n      type: \"confirm\"\r\n    }, (confirm) => {\r\n      if (confirm) {\r\n        this.ctsPackageRequestService\r\n          .batchUploadToCts(Number(this.ctsUploadSelectedYear)).subscribe({\r\n            next: (response) => {\r\n              if (response.success) {\r\n                this.toasterService.success(response.message || 'CTS Upload successfully requested', '', { life: 5000 });\r\n              }\r\n              else {\r\n                this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, { life: 7000 });\r\n              }\r\n              this.onCtsUploadLazyLoadEvent(undefined);\r\n              this.onCtsDashboardLazyLoadEvent(undefined);\r\n            },\r\n            error: (error) => {\r\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n              console.error('Error requesting CTS Upload:', error);\r\n            }\r\n          })\r\n      }\r\n    });\r\n\r\n    // const dialogRef = this.dialog.open(UploadCtsDialogComponent, {\r\n    //   width: '500px',\r\n    //   data: {\r\n    //     fiscalYears: this.year || []\r\n    //   }\r\n    // });\r\n\r\n    // dialogRef.afterClosed().subscribe((result) => {\r\n    //   if (result && result.fiscalYear) {\r\n    //     this.ctsPackageRequestService\r\n    //       .batchUploadToCts(result.fiscalYear).subscribe({\r\n    //         next: (response) => {\r\n    //           if (response.success) {\r\n    //             this.toasterService.success(response.message || 'CTS Upload successfully requested', '', { life: 5000 });\r\n    //           }\r\n    //           else {\r\n    //             this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, { life: 7000 });\r\n    //           }\r\n    //           this.onCtsUploadLazyLoadEvent(undefined);\r\n    //           this.onCtsDashboardLazyLoadEvent(undefined);\r\n    //         },\r\n    //         error: (error) => {\r\n    //           //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n    //           console.error('Error requesting CTS Upload:', error);\r\n    //         }\r\n    //       })\r\n    //   }\r\n    // });\r\n  }\r\n\r\n  openDecryptDialog() {\r\n    const dialogRef = this.dialog.open(DecryptDataPacketDialogComponent, {\r\n      width: '500px'\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (result && result.file) {\r\n        const formData: FormData = new FormData();\r\n        formData.append('fileName', result.file.name);\r\n        formData.append('file', result.file);\r\n        formData.append('fileType', result.file.type);\r\n        // Only call upload if file is present        \r\n        this.fileUploadService\r\n          .unpackCtsPackage(formData).subscribe({\r\n            next: (response) => {\r\n              if (response.success) {\r\n                this.toasterService.success(response.message || 'Decrypt Data Packet successfully completed', '', { life: 5000 });\r\n              }\r\n              else {\r\n                this.toasterService.warn(response.message || 'Decrypt Data Packet couldn’t be completed. Please try again later', null, { life: 7000 });\r\n              }\r\n              this.onCtsUploadLazyLoadEvent(undefined);\r\n              this.onCtsDashboardLazyLoadEvent(undefined);\r\n            },\r\n            error: (error) => {\r\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n              console.error('Error decrypting data packet:', error);\r\n            }\r\n          })\r\n      }\r\n    });\r\n  }\r\n\r\n  refreshAllTransmissionStatus() {\r\n    this.sweetAlert.fireDialog({\r\n      action: \"submit\",\r\n      title: \"Refresh All Transmission Status\",\r\n      text: \"Are you sure you would like to proceed?\",\r\n      type: \"confirm\"\r\n    }, (confirm) => {\r\n      if (confirm) {\r\n        this.ctsPackageRequestService.refreshTransmissionStatus().subscribe({\r\n          next: (response) => {\r\n            if (response.success) {\r\n              this.toasterService.success(response.message || 'Refresh All Transmission Status successfully requested', '', { life: 5000 });\r\n            }\r\n            else {\r\n              this.toasterService.warn(response.message || 'Refresh All Transmission Status couldn’t be requested. Please try again later', null, { life: 7000 });\r\n            }\r\n            this.onCtsUploadLazyLoadEvent(undefined);\r\n            this.onCtsDashboardLazyLoadEvent(undefined);\r\n          },\r\n          error: (error) => {\r\n            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\r\n            console.error('Error requesting Refresh All Transmission Status:', error);\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  downloadCtsDataPacket(row: any) {\r\n    this.sweetAlert.fireDialog({\r\n      action: \"submit\",\r\n      title: \"Download CTS Data Packet\",\r\n      text: \"Are you sure you would like to download?\",\r\n      type: \"confirm\"\r\n    }, (confirm) => {\r\n      if (confirm) {\r\n        this.ctsPackageRequestService.downloadDataPacketFile(row?.ctsPackageId).subscribe({\r\n          next: (response) => {\r\n            if (response) {\r\n              this.downloadFile(response, row?.dataPacket);\r\n              this.toasterService.success('Download CTS Data Packet successfully completed', '', { life: 5000 });\r\n            }\r\n            else {\r\n              this.toasterService.warn('Download CTS Data Packet couldn’t be completed. Please try again later', null, { life: 7000 });\r\n            }\r\n          },\r\n          error: (error) => {\r\n            // this.toasterService.error('Error Download CTS Data Packet', null, { life: 200000 });            \r\n            console.error('Error Download CTS Data Packet:', error);\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<mat-tab-group (selectedTabChange)=\"tabChanged($event)\">\r\n  <mat-tab label=\"Info Exchange Readiness\">\r\n    <div class=\"top-action-row-exchange row\">\r\n      <div class=\"table-container\">\r\n        <bdo-table [id]=\"TableIdS\" [columns]=\"summaryExchangeColumns\" scrollHeight=\"100%\"\r\n          defaultSortColumnId=\"uploadedDateTime\" [defaultSortOrder]=\"'desc'\" [pageIndex]=\"currentPageIndexS\"\r\n          [pageSize]=\"PageSizeS\" [isVirtualScroll]=\"false\" [hidePagination]=\"true\" [rowSelectable]=\"true\"\r\n          [lazyLoad]=\"true\" (onLazyLoad)=\"onLazyLoadEventS($event)\" [pageSizeOptions]=\"[10, 20, 50, 100]\">\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n    <div class=\"top-action-column-exchange row justify-content-end\">\r\n      <div class=\"col-md-auto\">\r\n        <span class=\"certificate-text\" *ngIf=\"certificateExpirationDate\">CA Certificate expires at {{\r\n          certificateExpirationDate | date:'dd/MM/yyyy'}}</span>\r\n        <button *ngIf=\"showBahamasSettings\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"openUpdateCtsSettingDialog()\">\r\n          Update CTS Settings\r\n        </button>\r\n        <button *ngIf=\"showUpdateCACertificate\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"openUpdateCaCertificateDialog()\">\r\n          Update CA Certificate\r\n        </button>\r\n        <button *ngIf=\"showButton\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(0)\">\r\n          Non-compliance XML\r\n        </button>\r\n        <button *ngIf=\"showButton\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(1)\">\r\n          High Risk IP XML\r\n        </button>\r\n        <button *ngIf=\"showButton\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(2)\">\r\n          Non-resident XML\r\n        </button>\r\n        <button *ngIf=\"showButton && showOtherCase\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"GenerateXMlByType(3)\">\r\n          Other Cases XML\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"top-action-column-exchange row\">\r\n      <div class=\"col-md-4 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Financial Period End</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Financial Period End\" [(value)]=\"selectedYear\"\r\n            (selectionChange)=\"onYearChange($event)\">\r\n            <mat-option *ngFor=\"let element of year\" [value]=\"element\">\r\n              {{ element }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Report Status</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Report Status\" [(value)]=\"selectReportStatus\"\r\n            (selectionChange)=\"onReportChange($event)\">\r\n            <mat-option *ngFor=\"let element of informationExchangedDic\" [value]=\"element.value\">\r\n              {{ element.description }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-mat-label\">Entity Name</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <input matInput placeholder=\"File Name\" [(ngModel)]=\"searchEntityName\" />\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-2 col-sm-12 margin-top search-button-column\">\r\n        <button type=\"button\" mat-raised-button (click)=\"onSearch()\" class=\"ui-button search-button\">\r\n          Search\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"top-action-row-exchange row\">\r\n      <div class=\"table-container\">\r\n        <bdo-table [id]=\"TableId\" scrollHeight=\"36vh\" [columns]=\"exchangeResultColumns\"\r\n          defaultSortColumnId=\"ExchangeReason\" [defaultSortOrder]=\"'desc'\" [pageIndex]=\"currentPageIndex\"\r\n          [pageSize]=\"PageSize\" [pageSizeOptions]=\"[10, 20, 50, 100]\" [isVirtualScroll]=\"false\" [hidePagination]=\"false\"\r\n          [rowSelectable]=\"true\" [lazyLoad]=\"true\" (onLazyLoad)=\"onLazyLoadEvent($event)\"\r\n          (onLinkClick)=\"onLinkClick($event)\">\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n  </mat-tab>\r\n\r\n  <mat-tab *ngIf=\"showDataPacketDashboard\" label=\"CTS Upload & Transmission\">\r\n    <!-- 1. Dashboard Section -->\r\n    <div class=\"row top-action-row-exchange\">\r\n      <div class=\"table-container\">\r\n        <bdo-table [id]=\"ctsDashboardTableId\" [columns]=\"ctsDashboardColumns\" scrollHeight=\"auto\"\r\n          [defaultSortOrder]=\"'desc'\" [pageIndex]=\"currentPageIndexS\" [pageSize]=\"PageSizeS\" [isVirtualScroll]=\"false\"\r\n          [hidePagination]=\"true\" [rowSelectable]=\"false\" [lazyLoad]=\"false\">\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 2. Button List Section -->\r\n    <div class=\"top-action-column-exchange row justify-content-end\">\r\n      <div class=\"col-md-auto\">\r\n        <button *ngIf=\"showDecryptReceivedDataPacket\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"openDecryptDialog()\"><mat-icon>lock_open</mat-icon> Decrypt Received Data Packet</button>\r\n        <button *ngIf=\"showUploadHistoricalXml\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"openUploadHistoricalXmlDialog()\"><mat-icon>cloud_upload</mat-icon> Upload Historical XML</button>\r\n        <button *ngIf=\"showCTSUpload\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"openCtsUploadDialog()\"><mat-icon>cloud_upload</mat-icon> CTS Upload</button>\r\n        <button *ngIf=\"showRefreshStatus\" type=\"button\" mat-raised-button class=\"ui-button margin-l-5\"\r\n          (click)=\"refreshAllTransmissionStatus()\"><mat-icon>refresh</mat-icon> Refresh CTS Transmission Status</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 3. Filters and Search Section -->\r\n    <div class=\"top-action-column-exchange row\">\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-label outside-mat-label\">Financial Period End</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Financial Period End\" [(value)]=\"ctsUploadSelectedYear\"\r\n            (selectionChange)=\"onCtsUploadYearChange($event)\">\r\n            <mat-option *ngFor=\"let element of year\" [value]=\"element\">\r\n              {{ element }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-label outside-mat-label\">Exchange Reason</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Exchange Reason\" [(value)]=\"selectExchangeReason\"\r\n            (selectionChange)=\"onCtsUploadExchangeReasonChange($event)\">\r\n            <mat-option *ngFor=\"let element of ctsUploadExchangeReasonDic\" [value]=\"element.value\">\r\n              {{ element.description }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-label outside-mat-label\">Receiving Country</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"Receiving Country\" [(value)]=\"selectReceivingCountry\"\r\n            (selectionChange)=\"onCtsUploadReceivingCountryChange($event)\">\r\n            <mat-option *ngFor=\"let element of countries\" [value]=\"element.code2\">\r\n              {{element.name}}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <div class=\"col-md-3 col-sm-12 margin-top\">\r\n        <mat-label class=\"outside-label outside-mat-label\">CTS Upload Status</mat-label>\r\n        <mat-form-field class=\"form-field-reduce-length\">\r\n          <mat-select placeholder=\"CTS Upload Status\" [(value)]=\"selectCtsUploadStatus\"\r\n            (selectionChange)=\"onCtsUploadStatusChange($event)\">\r\n            <mat-option *ngFor=\"let element of ctsUploadStatusDic\" [value]=\"element.value\">\r\n              {{ element.description }}\r\n            </mat-option>\r\n          </mat-select>\r\n        </mat-form-field>\r\n      </div>\r\n      <!-- <div class=\"col-md-1 col-sm-12 margin-top search-button-column\">\r\n        <button type=\"button\" mat-raised-button class=\"ui-button search-button search-button-m-top\"\r\n          (click)=\"onCtsUploadSearch()\">Search</button>\r\n      </div> -->\r\n    </div>\r\n\r\n    <!-- 4. Grid Section -->\r\n    <div class=\"top-action-row-exchange row\">\r\n      <div class=\"table-container\">\r\n        <bdo-table [id]=\"ctsUploadTableId\" [columns]=\"ctsUploadColumns\" scrollHeight=\"36vh\"\r\n          defaultSortColumnId=\"ExchangeReason\" [defaultSortOrder]=\"'desc'\" [pageIndex]=\"ctsUploadCurrentPage\"\r\n          [pageSize]=\"ctsUploadPageSize\" [pageSizeOptions]=\"[10, 20, 50, 100]\" [isVirtualScroll]=\"false\"\r\n          [hidePagination]=\"false\" [rowSelectable]=\"true\" [lazyLoad]=\"true\"\r\n          (onLazyLoad)=\"onCtsUploadLazyLoadEvent($event)\" (onLinkClick)=\"onCtsUploadLinkClick($event)\"\r\n          (onActionClick)=\"onCtsUploadActionClick($event)\" (onCheckboxClick)=\"onCheckboxClick($event)\">\r\n        </bdo-table>\r\n      </div>\r\n    </div>\r\n\r\n  </mat-tab>\r\n</mat-tab-group>"], "mappings": "AAOA,SAEEA,yBAAyB,QACpB,mHAAmH;AAC1H,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAIEC,kBAAkB,EAClBC,YAAY,QAEP,yDAAyD;AAChE,SACEC,iBAAiB,EAEjBC,4BAA4B,EAG5BC,kBAAkB,EAClBC,0BAA0B,QACrB,8BAA8B;AACrC,OAAOC,IAAI,MAAM,aAAa;AAI9B,SAASC,mCAAmC,QAAQ,wEAAwE;AAE5H,SAASC,kCAAkC,QAAQ,wEAAwE;AAM3H,SAASC,sCAAsC,QAAQ,gFAAgF;AACvI,SAASC,kCAAkC,QAAQ,wEAAwE;AAC3H,SAASC,gCAAgC,QAAQ,oEAAoE;AACrH,SAASC,+BAA+B,QAAQ,gEAAgE;AAChH,SAASC,0BAA0B,QAAQ,sDAAsD;AACjG,SAASC,eAAe,QAAQ,sGAAsG;AAItI,SAASC,+BAA+B,QAAQ,kEAAkE;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICtC1GC,EAAA,CAAAC,cAAA,eAAiE;IAAAD,EAAA,CAAAE,MAAA,GAChB;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADSH,EAAA,CAAAI,SAAA,EAChB;IADgBJ,EAAA,CAAAK,kBAAA,+BAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,yBAAA,oBAChB;;;;;;IACjDR,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAAS,UAAA,mBAAAC,2EAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,0BAAA,EAA4B;IAAA,EAAC;IACtCf,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAC4C;IAA1CD,EAAA,CAAAS,UAAA,mBAAAO,2EAAA;MAAAhB,EAAA,CAAAW,aAAA,CAAAM,GAAA;MAAA,MAAAV,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAW,6BAAA,EAA+B;IAAA,EAAC;IACzClB,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAS,UAAA,mBAAAU,4EAAA;MAAAnB,EAAA,CAAAW,aAAA,CAAAS,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAc,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAC9BrB,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAS,UAAA,mBAAAa,4EAAA;MAAAtB,EAAA,CAAAW,aAAA,CAAAY,GAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAc,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAC9BrB,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAS,UAAA,mBAAAe,4EAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAc,GAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAc,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAC9BrB,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAS,UAAA,mBAAAiB,4EAAA;MAAA1B,EAAA,CAAAW,aAAA,CAAAgB,GAAA;MAAA,MAAApB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAc,iBAAA,CAAkB,CAAC,CAAC;IAAA,EAAC;IAC9BrB,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IASLH,EAAA,CAAAC,cAAA,qBAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4BH,EAAA,CAAA4B,UAAA,UAAAC,UAAA,CAAiB;IACxD7B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAwB,UAAA,MACF;;;;;IASA7B,EAAA,CAAAC,cAAA,qBAAoF;IAClFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+CH,EAAA,CAAA4B,UAAA,UAAAE,UAAA,CAAAC,KAAA,CAAuB;IACjF/B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAyB,UAAA,CAAAE,WAAA,MACF;;;;;;IA0CJhC,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAS,UAAA,mBAAAwB,sFAAA;MAAAjC,EAAA,CAAAW,aAAA,CAAAuB,IAAA;MAAA,MAAA3B,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA4B,iBAAA,EAAmB;IAAA,EAAC;IAACnC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACpGH,EAAA,CAAAC,cAAA,iBAC4C;IAA1CD,EAAA,CAAAS,UAAA,mBAAA2B,sFAAA;MAAApC,EAAA,CAAAW,aAAA,CAAA0B,IAAA;MAAA,MAAA9B,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAA+B,6BAAA,EAA+B;IAAA,EAAC;IAACtC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5GH,EAAA,CAAAC,cAAA,iBACkC;IAAhCD,EAAA,CAAAS,UAAA,mBAAA8B,sFAAA;MAAAvC,EAAA,CAAAW,aAAA,CAAA6B,IAAA;MAAA,MAAAjC,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAkC,mBAAA,EAAqB;IAAA,EAAC;IAACzC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACvFH,EAAA,CAAAC,cAAA,iBAC2C;IAAzCD,EAAA,CAAAS,UAAA,mBAAAiC,sFAAA;MAAA1C,EAAA,CAAAW,aAAA,CAAAgC,IAAA;MAAA,MAAApC,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAqC,4BAAA,EAA8B;IAAA,EAAC;IAAC5C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAW5GH,EAAA,CAAAC,cAAA,qBAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4BH,EAAA,CAAA4B,UAAA,UAAAiB,WAAA,CAAiB;IACxD7C,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAwC,WAAA,MACF;;;;;IASA7C,EAAA,CAAAC,cAAA,qBAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkDH,EAAA,CAAA4B,UAAA,UAAAkB,WAAA,CAAAf,KAAA,CAAuB;IACpF/B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAyC,WAAA,CAAAd,WAAA,MACF;;;;;IASAhC,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFiCH,EAAA,CAAA4B,UAAA,UAAAmB,WAAA,CAAAC,KAAA,CAAuB;IACnEhD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0C,WAAA,CAAAE,IAAA,MACF;;;;;IASAjD,EAAA,CAAAC,cAAA,qBAA+E;IAC7ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF0CH,EAAA,CAAA4B,UAAA,UAAAsB,WAAA,CAAAnB,KAAA,CAAuB;IAC5E/B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6C,WAAA,CAAAlB,WAAA,MACF;;;;;;IAhENhC,EAHJ,CAAAC,cAAA,kBAA2E,cAEhC,aACV;IAC3BD,EAAA,CAAAmD,SAAA,oBAGY;IAEhBnD,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,aAAgE,aACrC;IAOvBD,EANA,CAAAoD,UAAA,IAAAC,6DAAA,oBACgC,IAAAC,6DAAA,oBAEY,IAAAC,6DAAA,oBAEV,IAAAC,6DAAA,oBAES;IAE/CxD,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAA4C,eACC,qBACU;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAEjFH,EADF,CAAAC,cAAA,0BAAiD,sBAEK;IADLD,EAAA,CAAAyD,gBAAA,yBAAAC,wFAAAC,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAA6D,kBAAA,CAAAtD,MAAA,CAAAuD,qBAAA,EAAAH,MAAA,MAAApD,MAAA,CAAAuD,qBAAA,GAAAH,MAAA;MAAA,OAAA3D,EAAA,CAAAc,WAAA,CAAA6C,MAAA;IAAA,EAAiC;IAC9E3D,EAAA,CAAAS,UAAA,6BAAAsD,4FAAAJ,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAmBP,MAAA,CAAAyD,qBAAA,CAAAL,MAAA,CAA6B;IAAA,EAAC;IACjD3D,EAAA,CAAAoD,UAAA,KAAAa,kEAAA,yBAA2D;IAKjEjE,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;IAEJH,EADF,CAAAC,cAAA,eAA2C,qBACU;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAE5EH,EADF,CAAAC,cAAA,0BAAiD,sBAEe;IADpBD,EAAA,CAAAyD,gBAAA,yBAAAS,wFAAAP,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAA6D,kBAAA,CAAAtD,MAAA,CAAA4D,oBAAA,EAAAR,MAAA,MAAApD,MAAA,CAAA4D,oBAAA,GAAAR,MAAA;MAAA,OAAA3D,EAAA,CAAAc,WAAA,CAAA6C,MAAA;IAAA,EAAgC;IACxE3D,EAAA,CAAAS,UAAA,6BAAA2D,4FAAAT,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAmBP,MAAA,CAAA8D,+BAAA,CAAAV,MAAA,CAAuC;IAAA,EAAC;IAC3D3D,EAAA,CAAAoD,UAAA,KAAAkB,kEAAA,yBAAuF;IAK7FtE,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;IAEJH,EADF,CAAAC,cAAA,eAA2C,qBACU;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAE9EH,EADF,CAAAC,cAAA,0BAAiD,sBAEiB;IADpBD,EAAA,CAAAyD,gBAAA,yBAAAc,wFAAAZ,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAA6D,kBAAA,CAAAtD,MAAA,CAAAiE,sBAAA,EAAAb,MAAA,MAAApD,MAAA,CAAAiE,sBAAA,GAAAb,MAAA;MAAA,OAAA3D,EAAA,CAAAc,WAAA,CAAA6C,MAAA;IAAA,EAAkC;IAC5E3D,EAAA,CAAAS,UAAA,6BAAAgE,4FAAAd,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAmBP,MAAA,CAAAmE,iCAAA,CAAAf,MAAA,CAAyC;IAAA,EAAC;IAC7D3D,EAAA,CAAAoD,UAAA,KAAAuB,kEAAA,yBAAsE;IAK5E3E,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;IAEJH,EADF,CAAAC,cAAA,eAA2C,qBACU;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAE9EH,EADF,CAAAC,cAAA,0BAAiD,sBAEO;IADVD,EAAA,CAAAyD,gBAAA,yBAAAmB,wFAAAjB,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAA6D,kBAAA,CAAAtD,MAAA,CAAAsE,qBAAA,EAAAlB,MAAA,MAAApD,MAAA,CAAAsE,qBAAA,GAAAlB,MAAA;MAAA,OAAA3D,EAAA,CAAAc,WAAA,CAAA6C,MAAA;IAAA,EAAiC;IAC3E3D,EAAA,CAAAS,UAAA,6BAAAqE,4FAAAnB,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAmBP,MAAA,CAAAwE,uBAAA,CAAApB,MAAA,CAA+B;IAAA,EAAC;IACnD3D,EAAA,CAAAoD,UAAA,KAAA4B,kEAAA,yBAA+E;IAUvFhF,EAPM,CAAAG,YAAA,EAAa,EACE,EACb,EAKF;IAKFH,EAFJ,CAAAC,cAAA,cAAyC,cACV,qBAMoE;IAA5CD,EADjD,CAAAS,UAAA,wBAAAwE,sFAAAtB,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAcP,MAAA,CAAA2E,wBAAA,CAAAvB,MAAA,CAAgC;IAAA,EAAC,yBAAAwB,uFAAAxB,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAgBP,MAAA,CAAA6E,oBAAA,CAAAzB,MAAA,CAA4B;IAAA,EAAC,2BAAA0B,yFAAA1B,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAC3EP,MAAA,CAAA+E,sBAAA,CAAA3B,MAAA,CAA8B;IAAA,EAAC,6BAAA4B,2FAAA5B,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAoBP,MAAA,CAAAiF,eAAA,CAAA7B,MAAA,CAAuB;IAAA,EAAC;IAKpG3D,EAJM,CAAAG,YAAA,EAAY,EACR,EACF,EAEE;;;;IAtFOH,EAAA,CAAAI,SAAA,GAA0B;IAEaJ,EAFvC,CAAA4B,UAAA,OAAArB,MAAA,CAAAkF,mBAAA,CAA0B,YAAAlF,MAAA,CAAAmF,mBAAA,CAAgC,4BACxC,cAAAnF,MAAA,CAAAoF,iBAAA,CAAgC,aAAApF,MAAA,CAAAqF,SAAA,CAAuB,0BAA0B,wBACrF,wBAAwB,mBAAmB;IAQ3D5F,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAA4B,UAAA,SAAArB,MAAA,CAAAsF,6BAAA,CAAmC;IAEnC7F,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAA4B,UAAA,SAAArB,MAAA,CAAAuF,uBAAA,CAA6B;IAE7B9F,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAA4B,UAAA,SAAArB,MAAA,CAAAwF,aAAA,CAAmB;IAEnB/F,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,SAAArB,MAAA,CAAAyF,iBAAA,CAAuB;IAUiBhG,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAiG,gBAAA,UAAA1F,MAAA,CAAAuD,qBAAA,CAAiC;IAE9C9D,EAAA,CAAAI,SAAA,EAAO;IAAPJ,EAAA,CAAA4B,UAAA,YAAArB,MAAA,CAAA2F,IAAA,CAAO;IASClG,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAiG,gBAAA,UAAA1F,MAAA,CAAA4D,oBAAA,CAAgC;IAExCnE,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAA4B,UAAA,YAAArB,MAAA,CAAA4F,0BAAA,CAA6B;IASnBnG,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAiG,gBAAA,UAAA1F,MAAA,CAAAiE,sBAAA,CAAkC;IAE5CxE,EAAA,CAAAI,SAAA,EAAY;IAAZJ,EAAA,CAAA4B,UAAA,YAAArB,MAAA,CAAA6F,SAAA,CAAY;IASFpG,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAiG,gBAAA,UAAA1F,MAAA,CAAAsE,qBAAA,CAAiC;IAE3C7E,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAA4B,UAAA,YAAArB,MAAA,CAAA8F,kBAAA,CAAqB;IAe9CrG,EAAA,CAAAI,SAAA,GAAuB;IAGgBJ,EAHvC,CAAA4B,UAAA,OAAArB,MAAA,CAAA+F,gBAAA,CAAuB,YAAA/F,MAAA,CAAAgG,gBAAA,CAA6B,4BACG,cAAAhG,MAAA,CAAAiG,oBAAA,CAAmC,aAAAjG,MAAA,CAAAkG,iBAAA,CACrE,oBAAAzG,EAAA,CAAA0G,eAAA,KAAAC,GAAA,EAAsC,0BAA0B,yBACtE,uBAAuB,kBAAkB;;;ADhH3E,OAAM,MAAOC,gCACX,SAAQ9H,gBAAgB;EA8UxB+H,YACEC,QAAkB,EACVC,MAAc,EACdC,0BAAsD,EACtDC,gCAAmE,EACnEC,iBAAoC,EACpCC,gBAA8C,EAC/CC,MAAiB,EAChBC,kBAAsC,EACtCC,iBAAoC,EACpCC,cAA8B,EAC9BC,cAA8B,EAC9BC,wBAAkD,EAClDC,UAA6B,EAC7BC,wBAAkD;IAE1D,KAAK,CAACb,QAAQ,CAAC;IAdP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,0BAA0B,GAA1BA,0BAA0B;IAC1B,KAAAC,gCAAgC,GAAhCA,gCAAgC;IAChC,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,wBAAwB,GAAxBA,wBAAwB;IA1VlC,KAAAC,KAAK,GAA8B;MACjCC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,oBAAoB;MAC7BC,yBAAyB,EAAEnJ,yBAAyB,CAACoJ,IAAI;MACzDC,UAAU,EAAE,EAAE;MACdhC,IAAI,EAAE;KACP;IACD,KAAAiC,OAAO,GAAG,wBAAwB;IAClC;IACA,KAAAC,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAAlC,IAAI,GAAG,EAAE;IACT;IACA,KAAAmC,YAAY,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE,CAAC,CAAC;IACpD,KAAAC,qBAAqB,GAA+B,CAClD;MACEC,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAAC4J,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAAC4J,MAAM;MAC/BC,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAAC4J,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAAC4J,MAAM;MAC/BC,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACsJ,IAAI;MAC7BO,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACsJ,IAAI;MAC7BO,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAAC4J,MAAM;MAC/BC,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACiK,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACiK,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACiK,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE;KACb,CACF;IACD;IACA,KAAAE,QAAQ,GAAG,EAAE;IACb,KAAAC,gCAAgC,GAAG,EAAE;IACrC,KAAAC,kBAAkB,GAAWvK,yBAAyB,CAACoJ,IAAI;IAE3D,KAAAoB,uBAAuB,GAAQlK,4BAA4B;IAC3D,KAAAmK,QAAQ,GAAG,wBAAwB;IACnC,KAAA3D,iBAAiB,GAAG,CAAC;IACrB,KAAA4D,sBAAsB,GAA+B,CACnD;MACEb,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EACR;KACH,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,CACF;IACD;IACA,KAAApD,SAAS,GAAG,EAAE;IACd,KAAA6D,YAAY,GAAG,CAAC;IAChB;;IAEA,KAAAC,UAAU,GAAG,IAAI;IACjB,KAAAC,aAAa,GAAG,IAAI;IAEpB;IACA,KAAAC,WAAW,GAAG,IAAItB,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE;IAEjD,KAAAhI,yBAAyB,GAAkB,IAAI;IAC/C,KAAAqJ,iBAAiB,GAAgC,IAAI;IACrD,KAAAC,eAAe,GAAY,KAAK;IAEhC;IACA,KAAApE,mBAAmB,GAA+B,CAChD;MACEgD,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,mBAAmB;MAC7BC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,oBAAoB;MAC9BC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE3J,kBAAkB,CAACwK,MAAM;MAC/BX,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,CACF;IAED;IACA,KAAAe,gBAAgB,GAAU,CACxB;MACEC,EAAE,EAAE,CAAC;MACLC,gBAAgB,EAAE,CAAC;MACnBC,mBAAmB,EAAE,CAAC;MACtBC,iBAAiB,EAAE,CAAC;MACpBC,kBAAkB,EAAE,CAAC;MACrBC,gBAAgB,EAAE;KACnB,CACF;IAED;IACA,KAAA9D,gBAAgB,GAA+B,CAC7C;MACEmC,QAAQ,EAAE,gBAAgB;MAC1BC,IAAI,EAAE3J,kBAAkB,CAAC4J,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE3J,kBAAkB,CAACiK,IAAI;MAC7BJ,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE3J,kBAAkB,CAACsJ,IAAI;MAC7BO,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE3J,kBAAkB,CAAC4J,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE3J,kBAAkB,CAAC4J,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE3J,kBAAkB,CAACsL,QAAQ;MACjCzB,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,uBAAuB;MACjCC,IAAI,EAAE3J,kBAAkB,CAAC4J,MAAM;MAC/BC,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE3J,kBAAkB,CAACiK,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,cAAc;MACxBC,IAAI,EAAE3J,kBAAkB,CAACiK,IAAI;MAC7BJ,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE3J,kBAAkB,CAACuL,kBAAkB;MAC3C1B,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,WAAW;MACrBC,IAAI,EAAE3J,kBAAkB,CAACuL,kBAAkB;MAC3C1B,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE3J,kBAAkB,CAACwL,QAAQ;MACjC3B,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;KACb,CACF;IAED;IACA,KAAA7C,0BAA0B,GAAQ9G,0BAA0B;IAC5D,KAAAgH,kBAAkB,GAAQjH,kBAAkB;IAG5C,KAAA0E,qBAAqB,GAAG,IAAIwE,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE,CAAC,CAAC;IAC7D,KAAAiC,sBAAsB,GAAG,EAAE;IAC3B,KAAAtG,oBAAoB,GAAW,CAAC,CAAC;IACjC,KAAAU,qBAAqB,GAAW/E,eAAe,CAAC4K,UAAU;IAC1D,KAAAlG,sBAAsB,GAAW,EAAE;IAEnC;IACA,KAAAiB,mBAAmB,GAAG,eAAe;IACrC,KAAAa,gBAAgB,GAAG,iBAAiB;IACpC,KAAAG,iBAAiB,GAAG,EAAE;IACtB,KAAAD,oBAAoB,GAAG,CAAC;IACxB,KAAAmE,qBAAqB,GAAG,CAAC;IACzB,KAAAC,cAAc,GAA4B;MACxC/C,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,oBAAoB;MAC7B8C,eAAe,EAAE,IAAI;MACrBC,cAAc,EAAE,IAAI;MACpBC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE;KACnB;IACD,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,uBAAuB,GAAY,KAAK;IACxC,KAAAC,uBAAuB,GAAY,KAAK;IACxC,KAAApF,aAAa,GAAY,KAAK;IAC9B,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAoF,wBAAwB,GAAY,KAAK;IACzC,KAAAvF,6BAA6B,GAAY,KAAK;IAC9C,KAAAC,uBAAuB,GAAY,KAAK;IA+KxC,KAAAuF,mBAAmB,GAAU,CAC3B;MACErB,EAAE,EAAE,CAAC;MACLsB,aAAa,EAAE,GAAG;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,sBAAsB,EAAE,CAAC;MACzBC,oBAAoB,EAAE,CAAC;MACvBC,oBAAoB,EAAE;KACvB,CACF;EArKD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC3C,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAAC7F,IAAI,GAAG,EAAE;QACd4F,QAAQ,CAACE,OAAO,CAAEC,OAAO,IAAI;UAC3B,IAAI,CAAC/F,IAAI,CAACgG,IAAI,CAACD,OAAO,CAACzD,QAAQ,EAAE,CAAC;QACpC,CAAC,CAAC;MACJ;MAAC;IACH,CAAC,CAAC;IAEF,IAAI2D,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,EAAE;MACxC,IAAI,CAAC/D,YAAY,GACf8D,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAACxC,WAAW;IAC5D;IAEA,IAAIuC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE;MAC9C,IAAI,CAAChD,kBAAkB,GAAGI,MAAM,CAC9B2C,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAC3C;IACH;IACA,IAAI,CAACnF,gCAAgC,CAClCoF,0BAA0B,EAAE,CAC5BR,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACQ,sBAAsB,GAAGR,QAAQ;MAEtC,IAAI,CAACS,eAAe,CAAC,IAAI,CAAClE,YAAY,CAAC;IACzC,CAAC,CAAC;IAEJ,IAAI,CAACmE,eAAe,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACC,gBAAgB,CAACD,SAAS,CAAC;IAEhC,IAAI,CAAC/C,UAAU,GAAG,IAAI,CAACiD,mBAAmB,EAAE;IAE5C;IACA;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC,aAAa,CAAmB;IAC5E,IAAI,CAAChD,eAAe,GAAG,CAAC,CAAC8C,WAAW,EAAEG,KAAK,EAAEC,QAAQ,CAAC,iBAAiB,CAAC;IAExE,IAAIb,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;MACjD,IAAI,CAACtI,qBAAqB,GACxBqI,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAACxC,WAAW;IACrE;IAEA,IAAIuC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAChD,IAAI,CAACjI,oBAAoB,GAAGqF,MAAM,CAChC2C,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAC7C;IACH;IAEA,IAAID,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;MACjD,IAAI,CAACvH,qBAAqB,GAAG2E,MAAM,CACjC2C,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAC9C;IACH;IAEA,IAAID,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;MAClD,IAAI,CAAC5H,sBAAsB,GAAG2H,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAC9E;IAEA,IAAI,CAACnB,mBAAmB,GAAG,IAAI,CAAC/D,iBAAiB,CAAC+F,gBAAgB,iFAAiC;IACnG,IAAI,CAAC/B,uBAAuB,GAAG,IAAI,CAAChE,iBAAiB,CAAC+F,gBAAgB,qFAAmC;IACzG,IAAI,CAAC9B,uBAAuB,GAAG,IAAI,CAACjE,iBAAiB,CAAC+F,gBAAgB,qFAAmC;IACzG,IAAI,CAAClH,aAAa,GAAG,IAAI,CAACmB,iBAAiB,CAAC+F,gBAAgB,gEAAwB;IACpF,IAAI,CAACjH,iBAAiB,GAAG,IAAI,CAACkB,iBAAiB,CAAC+F,gBAAgB,wEAA4B;IAC5F,IAAI,CAAC7B,wBAAwB,GAAG,IAAI,CAAClE,iBAAiB,CAAC+F,gBAAgB,uFAAoC;IAC3G,IAAI,CAACpH,6BAA6B,GAAG,IAAI,CAACqB,iBAAiB,CAAC+F,gBAAgB,kGAA0C;IACtH,IAAI,CAACnH,uBAAuB,GAAG,IAAI,CAACoB,iBAAiB,CAAC+F,gBAAgB,8EAAmC;IAEzG,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,IAAI,CAAC/B,uBAAuB,EAAE;MAChC,IAAI,CAACgC,yBAAyB,EAAE;IAClC;IACA,IAAI,IAAI,CAAClC,mBAAmB,EAAE;MAC5B,IAAI,CAACmC,wBAAwB,EAAE;IACjC;IACA,IAAI,IAAI,CAAClC,uBAAuB,EAAE;MAChC,IAAI,CAAChG,wBAAwB,CAACuH,SAAS,CAAC;MACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;IAC7C;EACF;EAEAF,eAAeA,CAACrG,IAAY;IAC1B,MAAMoH,iBAAiB,GAAWC,QAAQ,CAACrH,IAAI,EAAE,EAAE,CAAC;IACpD,MAAMsH,2BAA2B,GAAWD,QAAQ,CAClD,IAAI,CAACjB,sBAAsB,EAC3B,EAAE,CACH;IACD,IAAI,CAAC3C,aAAa,GAChB2D,iBAAiB,IAAIE,2BAA2B,GAAG,IAAI,GAAG,KAAK;EACnE;EAEA;EACAd,gBAAgBA,CAACe,KAAK;IACpB,IAAI,CAAC9H,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACqB,0BAA0B,CAC5B0G,sBAAsB,CAAC,IAAI,CAACrF,YAAY,CAAC,CACzCwD,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACT,mBAAmB,GAAGS,QAAQ;MACnC6B,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,aAAa,EAAE;MACtB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEA;EACApB,eAAeA,CAACiB,KAAK;IACnB,IAAIA,KAAK,EAAE;MACT,IAAI,IAAI,CAACvE,QAAQ,MAAMuE,KAAK,CAACI,QAAQ,IAAI,EAAE,CAAC,EAAE;QAC5C,IAAI,CAACzF,gBAAgB,GAAGqF,KAAK,CAACK,UAAU,IAAI,CAAC;MAC/C,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA,IAAI,CAAC5E,QAAQ,GAAGuE,KAAK,CAACI,QAAQ,IAAI,EAAE;QACpC,IAAI,CAACzF,gBAAgB,GAAG,CAAC;MAC3B;MAEA,IAAI,CAACR,KAAK,CAACE,SAAS,GAClB,CAAC,IAAI,CAACM,gBAAgB,IAAI,CAAC,KAAK,IAAI,CAACc,QAAQ,IAAI,EAAE,CAAC;MAEtD,IAAI,CAACtB,KAAK,CAACC,cAAc,GAAG,IAAI,CAACqB,QAAQ,IAAI,EAAE;MAE/C,IAAIuE,KAAK,CAACM,WAAW,KAAK,KAAK,EAAE;QAC/B,IAAI,CAACnG,KAAK,CAACG,OAAO,GAAG,GAAG0F,KAAK,CAACO,SAAS,OAAO;MAChD,CAAC,MAAM;QACL,IAAI,CAACpG,KAAK,CAACG,OAAO,GAAG,GAAG0F,KAAK,CAACO,SAAS,MAAM;MAC/C;IACF,CAAC,MAAM;MACL,IAAI,CAAC5F,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACc,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACtB,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACoB,kBAAkB;MAC9D,IAAI,CAACxB,KAAK,CAAC1B,IAAI,GAAG,IAAI,CAACmC,YAAY;MACnC,IAAI,CAACT,KAAK,CAACE,SAAS,GAAG,CAAC;MACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACqB,QAAQ;IAC3C;IAEA,IAAI,CAAClC,0BAA0B,CAC5BiH,gCAAgC,CAAC,IAAI,CAACrG,KAAK,CAAC,CAC5CiE,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACrC,YAAY,GAAGqC,QAAQ,CAACoC,UAAU;QACvC,IAAI,CAAC/E,gCAAgC,GAAG2C,QAAQ,CAACqC,KAAK;MACxD,CAAC,MAAM;QACL,IAAI,CAAC1E,YAAY,GAAG,CAAC;QACrB,IAAI,CAACN,gCAAgC,GAAG,EAAE;MAC5C;MAEAwE,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAaAR,aAAaA,CAAA;IACX,MAAMS,SAAS,GAAG,IAAIpP,YAAY,EAAE;IACpCoP,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAACjF,QAAQ;IACjC+E,SAAS,CAAC5E,YAAY,GAAG,CAAC;IAC1B4E,SAAS,CAACG,IAAI,GAAG,IAAI,CAACnD,mBAAmB,CAACoD,GAAG,CAAEC,CAAC,IAAI;MAClD,OAAO;QACL1E,EAAE,EAAE0E,CAAC,CAAC1E,EAAE;QACR2E,OAAO,EAAED,CAAC;QACVE,KAAK,EAAE,CACL;UACElG,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACG;SACV,EACD;UACEnG,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACI;SACV,EACD;UACEpG,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACK;SACV,EACD;UACErG,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACM;SACV,EACD;UACEtG,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACO;SACV;OAEJ;IACH,CAAC,CAAC;IACFtB,UAAU,CAAC,MAAK;MACd,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC;IAC1C,CAAC,EAAE,EAAE,CAAC;EACR;EAEAe,4BAA4BA,CAACxH,KAAU;IACrC,MAAMyH,WAAW,GAAGnQ,iBAAiB,CAACoQ,IAAI,CACvCC,MAAM,IAAKA,MAAM,CAACxN,KAAK,KAAK6F,KAAK,CACnC;IACD,IAAIyH,WAAW,EAAE,OAAOA,WAAW,CAACrN,WAAW;IAC/C,OAAO,EAAE;EACX;EAEAwN,uCAAuCA,CAAC5H,KAAU;IAChD,MAAMyH,WAAW,GAAGlQ,4BAA4B,CAACmQ,IAAI,CAClDC,MAAM,IAAKA,MAAM,CAACxN,KAAK,KAAK6F,KAAK,CACnC;IACD,IAAIyH,WAAW,EAAE,OAAOA,WAAW,CAACrN,WAAW;IAC/C,OAAO,EAAE;EACX;EAEAyN,kBAAkBA,CAACf,CAAS;IAC1B,MAAMgB,GAAG,GAAGC,IAAI,CAACjB,CAAC,CAAC;IACnB,IAAIkB,SAAS,GAAGF,GAAG,CAAC3D,MAAM;IAC1B,IAAI8D,KAAK,GAAG,IAAIC,UAAU,CAAC,IAAIC,WAAW,CAACH,SAAS,CAAC,CAAC;IAEtD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAEI,CAAC,EAAE,EAAE;MAClCH,KAAK,CAACG,CAAC,CAAC,GAAGN,GAAG,CAACO,UAAU,CAACD,CAAC,CAAC;IAC9B;IACA,OAAOH,KAAK;EACd;EAEAK,YAAYA,CAACC,OAAe,EAAElN,IAAY;IACxC,IAAImN,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,IAAI,CAACZ,kBAAkB,CAACU,OAAO,CAAC,CAAC,CAAC;IACvD,IAAIG,OAAO,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAE9C,IAAInE,OAAO,GAAGyE,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACzCD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAAC5E,OAAO,CAAC;IAClCA,OAAO,CAAC6E,KAAK,CAACC,OAAO,GAAG,MAAM;IAC9B9E,OAAO,CAAC+E,IAAI,GAAGV,OAAO;IACtBrE,OAAO,CAACgF,QAAQ,GAAGhO,IAAI;IACvBgJ,OAAO,CAACiF,KAAK,EAAE;IACfjF,OAAO,CAACkF,MAAM,EAAE;EAClB;EAGA9P,iBAAiBA,CAAC+P,YAA4B;IAC5C,IAAI,CAACpK,0BAA0B,CAC5BqK,8CAA8C,CAC7CD,YAAY,EACZ,IAAI,CAAC/I,YAAY,CAClB,CACAwD,SAAS,CAAEyF,MAAM,IAAI;MACpB,IAAI,CAAC9E,eAAe,CAACC,SAAS,CAAC;MAC/B,IAAI6E,MAAM,CAACC,QAAQ,IAAI,EAAE,EAAE;QACzB,IAAI,CAACrB,YAAY,CAACoB,MAAM,CAACnB,OAAO,CAAC3H,QAAQ,EAAE,EAAE8I,MAAM,CAACC,QAAQ,CAAC;MAC/D,CAAC,MACCjS,IAAI,CAACkS,IAAI,CAAC;QACRC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,oBAAoB;QAC1BC,iBAAiB,EAAE;OACpB,CAAC;IACN,CAAC,CAAC;EACN;EAEAC,UAAUA,CAACpE,KAAU;IACnB,IAAIA,KAAK,CAACqE,KAAK,KAAK,CAAC,EAAE;MACrB,IAAI,IAAI,CAAC5G,uBAAuB,EAAE;QAChC,IAAI,CAAChG,wBAAwB,CAACuH,SAAS,CAAC;QACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;MAC7C;IACF;EACF;EAEA2B,YAAYA,CAAA;IACV,MAAMC,SAAS,GAAG,IAAIpP,YAAY,EAAE;IACpCoP,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAACpG,OAAO;IAChCkG,SAAS,CAAC5E,YAAY,GAAG,IAAI,CAACA,YAAY;IAC1C4E,SAAS,CAACG,IAAI,GAAG,IAAI,CAACrF,gCAAgC,CAACsF,GAAG,CAAEC,CAAC,IAAI;MAC/D,OAAO;QACL1E,EAAE,EAAE0E,CAAC,CAAC1E,EAAE;QACR2E,OAAO,EAAED,CAAC;QACVE,KAAK,EAAE,CACL;UACElG,QAAQ;UACR3G,KAAK,EAAE,IAAI,CAACqN,4BAA4B,CAACV,CAAC,CAAC5D,cAAc;SAC1D,EACD;UACEpC,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACqD;SACV,EACD;UACErJ,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACxG;SACV,EACD;UACEQ,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACsD;SACV,EACD;UACEtJ,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACuD;SACV,EACD;UACEvJ,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAACwD;SACV,EACD;UACExJ,QAAQ;UACR3G,KAAK,EAAE,IAAI,CAACyN,uCAAuC,CACjDd,CAAC,CAAC1G,yBAAyB;SAE9B,EACD;UACEU,QAAQ;;UACR;UACA3G,KAAK,EAAE2M,CAAC,CAACyD,UAAU,KAAK,KAAK,GAAG,MAAM,GAAG;SAC1C,EACD;UACEzJ,QAAQ;UACR3G,KAAK,EAAE;SACR,EACD;UACE2G,QAAQ;UACR3G,KAAK,EAAE2M,CAAC,CAAC0D,gBAAgB,GAAG,cAAc,GAAG;SAC9C;OAEJ;IACH,CAAC,CAAC;IACFzE,UAAU,CAAC,MAAK;MACd,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC;IAC1C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAgE,YAAYA,CAACC,EAAE;IACb,IAAI,CAACjK,YAAY,GAAGiK,EAAE,CAACvQ,KAAK;IAC5B,IAAI,CAACwK,eAAe,CAAC,IAAI,CAAClE,YAAY,CAAC;IACvC;IACA8D,YAAY,CAACoG,OAAO,CAAC,cAAc,EAAED,EAAE,CAACvQ,KAAK,CAAC;IAE9C,IAAI,CAAC6F,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACoB,kBAAkB;IAC9D,IAAI,CAACxB,KAAK,CAAC1B,IAAI,GAAG,IAAI,CAACmC,YAAY;IACnC,IAAI,CAACT,KAAK,CAACM,UAAU,GAAG,IAAI,CAACsK,gBAAgB;IAC7C,IAAI,CAAC5K,KAAK,CAACE,SAAS,GAAG,CAAC;IACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACqB,QAAQ;IACzC,IAAI,CAAClC,0BAA0B,CAC5BiH,gCAAgC,CAAC,IAAI,CAACrG,KAAK,CAAC,CAC5CiE,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACrC,YAAY,GAAGqC,QAAQ,CAACoC,UAAU;MACvC,IAAI,CAAC/E,gCAAgC,GAAG2C,QAAQ,CAACqC,KAAK;MAEtDR,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEJ,IAAI,CAAC1B,gBAAgB,CAACD,SAAS,CAAC;EAClC;EAEAgG,QAAQA,CAAA;IACN,IAAI,CAAC7K,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACoB,kBAAkB;IAC9D,IAAI,CAACxB,KAAK,CAAC1B,IAAI,GAAG,IAAI,CAACmC,YAAY;IACnC,IAAI,CAACT,KAAK,CAACM,UAAU,GAAG,IAAI,CAACsK,gBAAgB;IAC7C,IAAI,CAAC5K,KAAK,CAACE,SAAS,GAAG,CAAC;IACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACqB,QAAQ;IACzC,IAAI,CAAClC,0BAA0B,CAC5BiH,gCAAgC,CAAC,IAAI,CAACrG,KAAK,CAAC,CAC5CiE,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACrC,YAAY,GAAGqC,QAAQ,CAACoC,UAAU;MACvC,IAAI,CAAC/E,gCAAgC,GAAG2C,QAAQ,CAACqC,KAAK;MAEtDR,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEJ,IAAI,CAAC1B,gBAAgB,CAACD,SAAS,CAAC;EAClC;EAEAiG,cAAcA,CAACJ,EAAE;IACf,IAAI,CAAClJ,kBAAkB,GAAGI,MAAM,CAAC8I,EAAE,CAACvQ,KAAK,CAAC;IAC1C,IAAI,CAAC6F,KAAK,CAACI,yBAAyB,GAAG,IAAI,CAACoB,kBAAkB;IAC9D,IAAI,CAACxB,KAAK,CAAC1B,IAAI,GAAG,IAAI,CAACmC,YAAY;IACnC,IAAI,CAACT,KAAK,CAACM,UAAU,GAAG,IAAI,CAACsK,gBAAgB;IAC7C,IAAI,CAAC5K,KAAK,CAACE,SAAS,GAAG,CAAC;IACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACqB,QAAQ;IAEzC;IACAiD,YAAY,CAACoG,OAAO,CAAC,oBAAoB,EAAED,EAAE,CAACvQ,KAAK,CAAC;IAEpD,IAAI,CAACiF,0BAA0B,CAC5BiH,gCAAgC,CAAC,IAAI,CAACrG,KAAK,CAAC,CAC5CiE,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACrC,YAAY,GAAGqC,QAAQ,CAACoC,UAAU;MACvC,IAAI,CAAC/E,gCAAgC,GAAG2C,QAAQ,CAACqC,KAAK;MAEtDR,UAAU,CAAC,MAAK;QACd,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAuE,WAAWA,CAAClF,KAAiC;IAC3C,MAAMe,IAAI,GAAGf,KAAK,CAACkB,OAAiC;IACpD,IAAIlB,KAAK,CAAC/E,QAAQ,+DAA+C;MAC/D;MACA;MACA;MACA;MACA,IAAI,CAAC3B,MAAM,CAAC6L,QAAQ,CAAC,CAAC,kCAAkC,CAAC,EAAE;QACzD;QACA;QACA;QACAC,WAAW,EAAE;UAAE7I,EAAE,EAAEwE,IAAI,CAACxE,EAAE;UAAE8I,SAAS,EAAE;QAAK;OAC7C,CAAC;IACJ,CAAC,MAAM,IACLrF,KAAK,CAAC/E,QAAQ,gFACd;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC3B,MAAM,CAAC6L,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;QACrCC,WAAW,EAAE;UACXE,aAAa,EAAEvE,IAAI,CAACwE,aAAa;UACjCC,QAAQ,EAAEzE,IAAI,CAAC0E,iBAAiB;UAChCC,IAAI,EAAE;;OAET,CAAC;IACJ,CAAC,MAAM,IACL1F,KAAK,CAAC/E,QAAQ,uEACd;MACA;MACA;MACA;MACA,IAAI,CAAC0K,oCAAoC,CAAC5E,IAAI,CAACxE,EAAE,CAAC;IACpD;EACF;EAEAoJ,oCAAoCA,CAACC,qBAA6B;IAChE,MAAMC,SAAS,GAAG,IAAI,CAAClM,MAAM,CAACmM,IAAI,CAAChU,mCAAmC,EAAE;MACtEiU,MAAM,EAAE,OAAO;MACfC,KAAK,EAAE,QAAQ;MACfjF,IAAI,EAAE;QAAE6E,qBAAqB,EAAEA;MAAqB;KACrD,CAAC;IAEFC,SAAS,CAACI,WAAW,EAAE,CAAC7H,SAAS,CAAEyF,MAAM,IAAI;MAC3CqC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEtC,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA;;;EAGA3E,mBAAmBA,CAAA;IACjB,IAAI2E,MAAM,GAAG,KAAK;IAClB;IACA,MAAM1E,WAAW,GAAG,IAAI,CAACC,WAAW,CAACC,MAAM,CACzC,aAAa,CACI;IAEnB,IAAIF,WAAW,EAAE;MACf0E,MAAM,GAAG,IAAI,CAACpK,iBAAiB,CAAC+F,gBAAgB,mFAE/C;IACH;IAEA,OAAOqE,MAAM;EACf;EAEA;EACA1F,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACzE,gBAAgB,CAACyE,cAAc,EAAE,CAACiI,IAAI,EAAE;EACtD;EAGA;EACAC,6BAA6BA,CAAClM,KAAU;IACtC,MAAMyH,WAAW,GAAGjQ,kBAAkB,CAACkQ,IAAI,CACxCC,MAAM,IAAKA,MAAM,CAACxN,KAAK,KAAK6F,KAAK,CACnC;IACD,IAAIyH,WAAW,EAAE,OAAOA,WAAW,CAACrN,WAAW;IAC/C,OAAO,EAAE;EACX;EAEA+R,uBAAuBA,CAACnM,KAAU;IAChC,MAAMoM,YAAY,GAAG,IAAI,CAAC5N,SAAS,CAACkJ,IAAI,CACrCC,MAAM,IAAKA,MAAM,CAACvM,KAAK,KAAK4E,KAAK,CACnC;IACD,IAAIoM,YAAY,EAAE,OAAOA,YAAY,CAAC/Q,IAAI;IAC1C,OAAO,EAAE;EACX;EAEAiK,YAAYA,CAAA;IACV,IAAI,CAAC1F,cAAc,CAACyM,OAAO,CAAC;MAAElM,OAAO,EAAE,UAAU;MAAEF,cAAc,EAAE;IAAI,CAAE,CAAC,CAACgE,SAAS,CAACC,QAAQ,IAAG;MAC9F,IAAI,CAAC1F,SAAS,GAAG0F,QAAQ,CAACqC,KAAK;MAC/B,IAAI,CAAC+F,yBAAyB,GAAGpI,QAAQ,CAACqC,KAAK;MAC/C;MACA,IAAI,CAAC/H,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC+N,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACpR,KAAK,IAAIoR,OAAO,CAACpR,KAAK,CAACqR,IAAI,EAAE,KAAK,EAAE,CAAC;MAC/F,IAAI,CAACH,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACpR,KAAK,IAAIoR,OAAO,CAACpR,KAAK,CAACqR,IAAI,EAAE,KAAK,EAAE,CAAC;MAC/H;MACA,IAAI,CAACjO,SAAS,CAACkO,OAAO,CAAC;QAAErR,IAAI,EAAE,KAAK;QAAED,KAAK,EAAE;MAAE,CAAE,CAAC;IACpD,CAAC,CAAC;EACJ;EAEAmK,yBAAyBA,CAAA;IACvB,OAAO,IAAI,CAAC9F,kBAAkB,CAAC8F,yBAAyB,EAAE,CAACtB,SAAS,CAAC;MACnE0I,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAAChU,yBAAyB,GAAGgU,IAAI,EAAEC,SAAS,IAAI,IAAI;MAC1D,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAClU,yBAAyB,GAAG,IAAI;MACvC;KACD,CAAC;EACJ;EAEA4M,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACzF,wBAAwB,CAACgN,kBAAkB,EAAE,CAAC9I,SAAS,CAAC;MAClE0I,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAAC3K,iBAAiB,GAAG2K,IAAI,IAAI,IAAI;MACvC,CAAC;MACDE,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC7K,iBAAiB,GAAG,IAAI;MAC/B;KACD,CAAC;EACJ;EAEA9I,0BAA0BA,CAAA;IACxB,MAAMuS,SAAS,GAAG,IAAI,CAAClM,MAAM,CAACmM,IAAI,CAACxT,+BAA+B,EAAE;MAClE0T,KAAK,EAAE,QAAQ;MACfjF,IAAI,EAAE,IAAI,CAAC3E,iBAAiB,IAAI;KACjC,CAAC;IAEFyJ,SAAS,CAACI,WAAW,EAAE,CAAC7H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAI,CAACA,MAAM,EAAE;MAEb,MAAMsD,QAAQ,GAAa,IAAIC,QAAQ,EAAE;MACzC,IAAIvD,MAAM,CAAClB,IAAI,EAAE;QACfwE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAACnN,IAAI,CAAC;QAC7C2R,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExD,MAAM,CAAClB,IAAI,CAAC;QACpCwE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAACzH,IAAI,CAAC;MAC/C;MAEA,MAAMoM,uBAAuB,GAAa,IAAIF,QAAQ,EAAE;MACxD,IAAIvD,MAAM,CAAC0D,eAAe,EAAE;QAC1BD,uBAAuB,CAACD,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAC0D,eAAe,CAAC/R,IAAI,CAAC;QACvE8R,uBAAuB,CAACD,MAAM,CAAC,MAAM,EAAExD,MAAM,CAAC0D,eAAe,CAAC;QAC9DD,uBAAuB,CAACD,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAC0D,eAAe,CAACrM,IAAI,CAAC;MACzE;MAEA,MAAMsM,UAAU,GAAG3D,MAAM,CAACtH,EAAE,IAAI,IAAI;MACpC,MAAMkL,WAAW,GAAGD,UAAU,GAC1B,IAAI,CAAC3N,iBAAiB,CAAC6N,wBAAwB,CAAC7D,MAAM,EAAEsD,QAAQ,EAAEG,uBAAuB,CAAC,GAC1F,IAAI,CAACzN,iBAAiB,CAAC8N,wBAAwB,CAAC9D,MAAM,EAAEsD,QAAQ,EAAEG,uBAAuB,CAAC;MAE9F,MAAMM,MAAM,GAAGJ,UAAU,GAAG,QAAQ,GAAG,QAAQ;MAE/CC,WAAW,CAACrJ,SAAS,CAAC;QACpB0I,IAAI,EAAGzI,QAAQ,IAAI;UACjB,IAAIA,QAAQ,EAAE;YACZ,IAAI,CAACsB,wBAAwB,EAAE;YAC/B,IAAI,CAAC7F,cAAc,CAAC+N,OAAO,CAAC,6BAA6BD,MAAM,IAAI,EAAE,EAAE,EAAE;cAAEE,IAAI,EAAE;YAAI,CAAE,CAAC;UAC1F,CAAC,MAAM;YACL,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC,6BAA6BH,MAAM,4BAA4B,EAAE,IAAI,EAAE;cAAEE,IAAI,EAAE;YAAI,CAAE,CAAC;UACjH;QACF,CAAC;QACDb,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnN,cAAc,CAACmN,KAAK,CAAC,qCAAqCW,MAAM,gBAAgB,EAAE,IAAI,EAAE;YAAEE,IAAI,EAAE;UAAI,CAAE,CAAC;UAC5G5B,OAAO,CAACe,KAAK,CAAC,SAASW,MAAM,mBAAmB,EAAEX,KAAK,CAAC;QAC1D;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAxT,6BAA6BA,CAAA;IAC3B,MAAMoS,SAAS,GAAG,IAAI,CAAClM,MAAM,CAACmM,IAAI,CAAC/T,kCAAkC,EAAE;MACrEiU,KAAK,EAAE,OAAO;MACdjF,IAAI,EAAE;KACP,CAAC;IAEF8E,SAAS,CAACI,WAAW,EAAE,CAAC7H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAIA,MAAM,IAAIA,MAAM,CAAClB,IAAI,EAAE;QACzB,MAAMwE,QAAQ,GAAa,IAAIC,QAAQ,EAAE;QACzCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAACnN,IAAI,CAAC;QAC7C2R,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExD,MAAM,CAAClB,IAAI,CAAC;QACpCwE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAACzH,IAAI,CAAC;QAC7C;QACA,IAAI,CAACrB,iBAAiB,CACnBmO,wBAAwB,CAACb,QAAQ,EAAEtD,MAAM,CAACoE,QAAQ,CAAC,CAAC7J,SAAS,CAAC;UAC7D0I,IAAI,EAAGzI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,EAAE;cACZ;cACA,IAAI,CAACqB,yBAAyB,EAAE;cAChC,IAAI,CAAC5F,cAAc,CAAC+N,OAAO,CAAC,2CAA2C,EAAE,EAAE,EAAE;gBAAEC,IAAI,EAAE;cAAI,CAAE,CAAC;YAC9F,CAAC,MACI;cACH,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC,kEAAkE,EAAE,IAAI,EAAE;gBAAED,IAAI,EAAE;cAAI,CAAE,CAAC;YACpH;UACF,CAAC;UACDb,KAAK,EAAGA,KAAK,IAAI;YACf;YACAf,OAAO,CAACe,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC9D;SACD,CAAC;MACN;IACF,CAAC,CAAC;EACJ;EAEA;EACAiB,wBAAwBA,CAAA;IACtB,MAAMtH,SAAS,GAAG,IAAIpP,YAAY,EAAE;IACpCoP,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAAC9I,mBAAmB;IAC5C4I,SAAS,CAAC5E,YAAY,GAAG,CAAC;IAC1B4E,SAAS,CAACG,IAAI,GAAG,IAAI,CAACzE,gBAAgB,CAAC0E,GAAG,CAACC,CAAC,KAAK;MAC/C1E,EAAE,EAAE0E,CAAC,CAAC1E,EAAE;MACR2E,OAAO,EAAED,CAAC;MACVE,KAAK,EAAE,CACL;QAAElG,QAAQ,EAAE,kBAAkB;QAAE3G,KAAK,EAAE2M,CAAC,CAACzE;MAAgB,CAAE,EAC3D;QAAEvB,QAAQ,EAAE,qBAAqB;QAAE3G,KAAK,EAAE2M,CAAC,CAACxE;MAAmB,CAAE,EACjE;QAAExB,QAAQ,EAAE,mBAAmB;QAAE3G,KAAK,EAAE2M,CAAC,CAACvE;MAAiB,CAAE,EAC7D;QAAEzB,QAAQ,EAAE,oBAAoB;QAAE3G,KAAK,EAAE2M,CAAC,CAACtE;MAAkB,CAAE,EAC/D;QAAE1B,QAAQ,EAAE,kBAAkB;QAAE3G,KAAK,EAAE2M,CAAC,CAACrE;MAAgB,CAAE;KAE9D,CAAC,CAAC;IACHsD,UAAU,CAAC,MAAM,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC,EAAE,EAAE,CAAC;EAChE;EAEAuH,qBAAqBA,CAAA;IACnB,MAAMvH,SAAS,GAAG,IAAIpP,YAAY,EAAE;IACpCoP,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAACjI,gBAAgB;IACzC+H,SAAS,CAAC5E,YAAY,GAAG,IAAI,CAACkB,qBAAqB;IACnD0D,SAAS,CAACG,IAAI,GAAG,IAAI,CAAC/D,sBAAsB,CAACgE,GAAG,CAACC,CAAC,KAAK;MACrD1E,EAAE,EAAE0E,CAAC,CAAC1E,EAAE;MACR2E,OAAO,EAAED,CAAC;MACVE,KAAK,EAAE,CACL;QAAElG,QAAQ,EAAE,gBAAgB;QAAE3G,KAAK,EAAE,IAAI,CAACqN,4BAA4B,CAACV,CAAC,CAAC5D,cAAc;MAAC,CAAE,EAC1F;QAAEpC,QAAQ,EAAE,YAAY;QAAE3G,KAAK,EAAE2M,CAAC,CAACmH;MAAU,CAAE,EAC/C;QAAEnN,QAAQ,EAAE,kBAAkB;QAAE3G,KAAK,EAAEhD,UAAU,CAAC+W,qBAAqB,CAACpH,CAAC,CAACqH,gBAAgB;MAAC,CAAE,EAC7F;QAAErN,QAAQ,EAAE,kBAAkB;QAAE3G,KAAK,EAAE,IAAI,CAACgS,uBAAuB,CAACrF,CAAC,CAAC1D,gBAAgB;MAAC,CAAE,EACzF;QAAEtC,QAAQ,EAAE,iBAAiB;QAAE3G,KAAK,EAAE,IAAI,CAAC+R,6BAA6B,CAACpF,CAAC,CAAC7D,eAAe;MAAC,CAAE,EAC7F;QAAEnC,QAAQ,EAAE,YAAY;QAAE3G,KAAK,EAAEhD,UAAU,CAAC+W,qBAAqB,CAACpH,CAAC,CAACsH,UAAU;MAAC,CAAE,EACjF;QAAEtN,QAAQ,EAAE,uBAAuB;QAAE3G,KAAK,EAAE2M,CAAC,CAACuH;MAAqB,CAAE,EACrE;QAAEvN,QAAQ,EAAE,qBAAqB;QAAE3G,KAAK,EAAE2M,CAAC,CAACwH,mBAAmB,KAAK,IAAI,GAAG,MAAM,GAAG;MAAI,CAAE,EAC1F;QAAExN,QAAQ,EAAE,cAAc;QAAE3G,KAAK,EAAE2M,CAAC,CAACyH,YAAY,CAACpK,MAAM,GAAG,CAAC,GAAG,eAAe,GAAG;MAAI,CAAE,EACvF;QACErD,QAAQ,EAAE,kBAAkB;QAC5B3G,KAAK,EAAE,IAAI,CAACqJ,wBAAwB,IAAIsD,CAAC,CAAC0H,cAAc,EAAEpJ,QAAQ,CAAC,CAAC,CAAC,GACjE;UAAEqJ,UAAU,EAAE,sBAAsB;UAAE5E,IAAI,EAAE,SAAS;UAAE6E,OAAO,EAAE;QAAmB,CAAE,GACrF;OACL,EACD;QACE5N,QAAQ,EAAE,WAAW;QACrB3G,KAAK,EAAE,IAAI,CAACgE,aAAa,IAAI2I,CAAC,CAAC0H,cAAc,EAAEpJ,QAAQ,CAAC,CAAC,CAAC,GACtD;UAAEqJ,UAAU,EAAE,WAAW;UAAE5E,IAAI,EAAE,cAAc;UAAE6E,OAAO,EAAE;QAAY,CAAE,GACxE;OACL,EACD;QACE5N,QAAQ,EAAE,sBAAsB;QAChC6N,IAAI,EAAE7H,CAAC,CAAC0H,cAAc,EAAEpJ,QAAQ,CAAC,CAAC,CAAC,IAAI0B,CAAC,CAAC0H,cAAc,EAAEpJ,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI;QACnFjL,KAAK,EAAE2M,CAAC,CAAC8H;OACV;KAEJ,CAAC,CAAC;IAEH7I,UAAU,CAAC,MAAM,IAAI,CAACuB,YAAY,CAACC,WAAW,CAACd,SAAS,CAAC,EAAE,GAAG,CAAC;EACjE;EAEAhB,2BAA2BA,CAACI,KAAK;IAC/B,IAAI,CAAC9H,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAAC8B,wBAAwB,CAC1BiG,sBAAsB,CAAC,IAAI,CAAC5J,qBAAqB,CAAC,CAClD+H,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAAC/B,gBAAgB,GAAG+B,QAAQ;MAChC6B,UAAU,CAAC,MAAK;QACd,IAAI,CAACgI,wBAAwB,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAzQ,wBAAwBA,CAACuI,KAAK;IAC5B,IAAIA,KAAK,EAAE;MACT,IAAI,IAAI,CAAChH,iBAAiB,MAAMgH,KAAK,CAACI,QAAQ,IAAI,EAAE,CAAC,EAAE;QACrD,IAAI,CAACrH,oBAAoB,GAAGiH,KAAK,CAACK,UAAU,IAAI,CAAC;MACnD,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA,IAAI,CAACrH,iBAAiB,GAAGgH,KAAK,CAACI,QAAQ,IAAI,EAAE;QAC7C,IAAI,CAACrH,oBAAoB,GAAG,CAAC;MAC/B;MAEA,IAAI,CAACoE,cAAc,CAAC9C,SAAS,GAC3B,CAAC,IAAI,CAACtB,oBAAoB,IAAI,CAAC,KAAK,IAAI,CAACC,iBAAiB,IAAI,EAAE,CAAC;MAEnE,IAAI,CAACmE,cAAc,CAAC/C,cAAc,GAAG,IAAI,CAACpB,iBAAiB,IAAI,EAAE;MAEjE,IAAIgH,KAAK,CAACM,WAAW,KAAK,KAAK,EAAE;QAC/B,IAAI,CAACnD,cAAc,CAAC7C,OAAO,GAAG,GAAG0F,KAAK,CAACO,SAAS,OAAO;MACzD,CAAC,MAAM;QACL,IAAI,CAACpD,cAAc,CAAC7C,OAAO,GAAG,GAAG0F,KAAK,CAACO,SAAS,MAAM;MACxD;IACF,CAAC,MAAM;MACL,IAAI,CAACxH,oBAAoB,GAAG,CAAC;MAC7B,IAAI,CAACC,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACmE,cAAc,CAACG,gBAAgB,GAAG,IAAI,CAACjH,qBAAqB;MACjE,IAAI,CAAC8G,cAAc,CAACE,cAAc,GAAG,IAAI,CAAC3G,oBAAoB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,oBAAoB,GAAG,IAAI;MACxG,IAAI,CAACyG,cAAc,CAACC,eAAe,GAAG,IAAI,CAAChG,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,qBAAqB,GAAG,IAAI;MAC3G,IAAI,CAAC+F,cAAc,CAACI,gBAAgB,GAAG,IAAI,CAACxG,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,GAAG,IAAI;MACvG,IAAI,CAACoG,cAAc,CAAC9C,SAAS,GAAG,CAAC;MACjC,IAAI,CAAC8C,cAAc,CAAC/C,cAAc,GAAG,IAAI,CAACpB,iBAAiB;IAC7D;IAEA,IAAI,CAACgB,wBAAwB,CAC1BgP,8BAA8B,CAAC,IAAI,CAAC7L,cAAc,CAAC,CACnDiB,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACnB,qBAAqB,GAAGmB,QAAQ,CAACoC,UAAU;QAChD,IAAI,CAACzD,sBAAsB,GAAGqB,QAAQ,CAACqC,KAAK;MAC9C,CAAC,MAAM;QACL,IAAI,CAACxD,qBAAqB,GAAG,CAAC;QAC9B,IAAI,CAACF,sBAAsB,GAAG,EAAE;MAClC;MAEAkD,UAAU,CAAC,MAAK;QACd,IAAI,CAACiI,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEA5R,qBAAqBA,CAACsO,EAAE;IACtB,IAAI,CAACxO,qBAAqB,GAAGwO,EAAE,CAACvQ,KAAK;IACrCoK,YAAY,CAACoG,OAAO,CAAC,uBAAuB,EAAED,EAAE,CAACvQ,KAAK,CAAC;IACvD,IAAI,CAAC2U,iBAAiB,EAAE;IACxB,IAAI,CAACrJ,2BAA2B,CAACZ,SAAS,CAAC;EAC7C;EAEAiK,iBAAiBA,CAAA;IACf,IAAI,CAAC9L,cAAc,CAACG,gBAAgB,GAAG,IAAI,CAACjH,qBAAqB;IACjE,IAAI,CAAC8G,cAAc,CAACE,cAAc,GAAG,IAAI,CAAC3G,oBAAoB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACxG,IAAI,CAACyG,cAAc,CAACC,eAAe,GAAG,IAAI,CAAChG,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,qBAAqB,GAAG,IAAI;IAC3G,IAAI,CAAC+F,cAAc,CAACI,gBAAgB,GAAG,IAAI,CAACxG,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACvG,IAAI,CAACoG,cAAc,CAAC9C,SAAS,GAAG,CAAC;IACjC,IAAI,CAAC8C,cAAc,CAAC/C,cAAc,GAAG,IAAI,CAACpB,iBAAiB;IAC3D,IAAI,CAACgB,wBAAwB,CAC1BgP,8BAA8B,CAAC,IAAI,CAAC7L,cAAc,CAAC,CACnDiB,SAAS,CAAEC,QAAQ,IAAI;MACtB,IAAI,CAACnB,qBAAqB,GAAGmB,QAAQ,CAACoC,UAAU;MAChD,IAAI,CAACzD,sBAAsB,GAAGqB,QAAQ,CAACqC,KAAK;MAE5CR,UAAU,CAAC,MAAK;QACd,IAAI,CAACiI,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACN;EAEAvR,+BAA+BA,CAACiO,EAAE;IAChC,IAAI,CAACnO,oBAAoB,GAAGqF,MAAM,CAAC8I,EAAE,CAACvQ,KAAK,CAAC;IAC5C;IACAoK,YAAY,CAACoG,OAAO,CAAC,sBAAsB,EAAED,EAAE,CAACvQ,KAAK,CAAC;IACtD,IAAI,CAAC2U,iBAAiB,EAAE;EAC1B;EAEA3R,uBAAuBA,CAACuN,EAAE;IACxB,IAAI,CAACzN,qBAAqB,GAAG2E,MAAM,CAAC8I,EAAE,CAACvQ,KAAK,CAAC;IAC7C;IACAoK,YAAY,CAACoG,OAAO,CAAC,uBAAuB,EAAED,EAAE,CAACvQ,KAAK,CAAC;IACvD,IAAI,CAAC2U,iBAAiB,EAAE;EAC1B;EAEAhS,iCAAiCA,CAAC4N,EAAE;IAClC,IAAI,CAAC9N,sBAAsB,GAAG8N,EAAE,CAACvQ,KAAK;IACtC;IACAoK,YAAY,CAACoG,OAAO,CAAC,wBAAwB,EAAED,EAAE,CAACvQ,KAAK,CAAC;IACxD,IAAI,CAAC2U,iBAAiB,EAAE;EAC1B;EAEAtR,oBAAoBA,CAACqI,KAAiC;IACpD,IAAIA,KAAK,CAAC/E,QAAQ,KAAK,qBAAqB,EAAE;MAC5C,IAAI,CAACtB,MAAM,CAACmM,IAAI,CAAC9T,sCAAsC,EAAE;QACvDgU,KAAK,EAAE,QAAQ;QACfjF,IAAI,EAAE;UACJmI,GAAG,EAAElJ,KAAK,CAACkB;;OAEd,CAAC;IACJ,CAAC,MACI,IAAIlB,KAAK,CAAC/E,QAAQ,KAAK,cAAc,EAAE;MAC1C,IAAI,CAACtB,MAAM,CAACmM,IAAI,CAAC1T,0BAA0B,EAAE;QAC3C4T,KAAK,EAAE,QAAQ;QACfjF,IAAI,EAAE;UACJmI,GAAG,EAAElJ,KAAK,CAACkB;;OAEd,CAAC;IACJ,CAAC,MACI,IAAIlB,KAAK,CAAC/E,QAAQ,KAAK,YAAY,EAAE;MACxC,IAAI,CAACkO,qBAAqB,CAACnJ,KAAK,CAACkB,OAAO,CAAC;IAC3C;EACF;EAEArJ,sBAAsBA,CAACmI,KAAkC;IACvD,IAAIA,KAAK,CAAC4H,MAAM,KAAK,sBAAsB,EAAE;MAC3C,MAAM/B,SAAS,GAAG,IAAI,CAAClM,MAAM,CAACmM,IAAI,CAAC3T,+BAA+B,EAAE;QAClE6T,KAAK,EAAE,OAAO;QACdjF,IAAI,EAAE;UACJmI,GAAG,EAAElJ,KAAK,EAAEe,IAAI,EAAEG;;OAErB,CAAC;MAEF2E,SAAS,CAACI,WAAW,EAAE,CAAC7H,SAAS,CAAEyF,MAAM,IAAI;QAC3C,IAAIA,MAAM,IAAIA,MAAM,CAACuF,OAAO,EAAE;UAC5B,IAAI,CAACpP,wBAAwB,CAC1BqP,iBAAiB,CAACrJ,KAAK,CAACe,IAAI,EAAEG,OAAO,EAAEoI,YAAY,EAAEzF,MAAM,CAACuF,OAAO,CAAC,CAAChL,SAAS,CAAC;YAC9E0I,IAAI,EAAGzI,QAAQ,IAAI;cACjB,IAAIA,QAAQ,CAACwJ,OAAO,EAAE;gBACpB,IAAI,CAAC/N,cAAc,CAAC+N,OAAO,CAACxJ,QAAQ,CAACkL,OAAO,IAAI,2CAA2C,EAAE,EAAE,EAAE;kBAAEzB,IAAI,EAAE;gBAAI,CAAE,CAAC;cAClH,CAAC,MACI;gBACH,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC1J,QAAQ,CAACkL,OAAO,IAAI,kEAAkE,EAAE,IAAI,EAAE;kBAAEzB,IAAI,EAAE;gBAAI,CAAE,CAAC;cACxI;cACA,IAAI,CAACrQ,wBAAwB,CAACuH,SAAS,CAAC;cACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;YAC7C,CAAC;YACDiI,KAAK,EAAGA,KAAK,IAAI;cACf;cACAf,OAAO,CAACe,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;YAC9D;WACD,CAAC;QACN;MACF,CAAC,CAAC;IACJ,CAAC,MACI,IAAIjH,KAAK,CAAC4H,MAAM,KAAK,WAAW,EAAE;MACrC,IAAI,CAAC3N,UAAU,CAACuP,UAAU,CAAC;QACzB5B,MAAM,EAAE,QAAQ;QAChB3D,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,yCAAyC;QAC/ChJ,IAAI,EAAE;OACP,EAAGuO,OAAO,IAAI;QACb,IAAIA,OAAO,EAAE;UACX,IAAI,CAACzP,wBAAwB,CAAC0P,WAAW,CAAC1J,KAAK,CAACe,IAAI,EAAEG,OAAO,EAAEoI,YAAY,CAAC,CAAClL,SAAS,CAAC;YACrF0I,IAAI,EAAGzI,QAAQ,IAAI;cACjB,IAAIA,QAAQ,CAACwJ,OAAO,EAAE;gBACpB,IAAI,CAAC/N,cAAc,CAAC+N,OAAO,CAACxJ,QAAQ,CAACkL,OAAO,IAAI,mCAAmC,EAAE,EAAE,EAAE;kBAAEzB,IAAI,EAAE;gBAAI,CAAE,CAAC;cAC1G,CAAC,MACI;gBACH,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC1J,QAAQ,CAACkL,OAAO,IAAI,0DAA0D,EAAE,IAAI,EAAE;kBAAEzB,IAAI,EAAE;gBAAI,CAAE,CAAC;cAChI;cACA,IAAI,CAACrQ,wBAAwB,CAACuH,SAAS,CAAC;cACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;YAC7C,CAAC;YACDiI,KAAK,EAAGA,KAAK,IAAI;cACf;cACAf,OAAO,CAACe,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;YACtD;WACD,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF;EAEAlP,eAAeA,CAACiI,KAAiC;IAC/C;IACA;IACA,IAAIA,KAAK,CAAC2J,SAAS,EAAE;MACnB,IAAI,CAAC3P,wBAAwB,CAAC4P,iBAAiB,CAAC5J,KAAK,CAAC6J,KAAK,CAAC,CAACzL,SAAS,CAAC;QACrE0I,IAAI,EAAGzI,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACwJ,OAAO,EAAE;YACpB,IAAI,CAAC/N,cAAc,CAAC+N,OAAO,CAACxJ,QAAQ,CAACkL,OAAO,IAAI,sDAAsD,EAAE,EAAE,EAAE;cAAEzB,IAAI,EAAE;YAAI,CAAE,CAAC;UAC7H,CAAC,MACI;YACH,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC1J,QAAQ,CAACkL,OAAO,IAAI,8EAA8E,EAAE,IAAI,EAAE;cAAEzB,IAAI,EAAE;YAAI,CAAE,CAAC;UACpJ;UACA,IAAI,CAACrQ,wBAAwB,CAACuH,SAAS,CAAC;UACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;QAC7C,CAAC;QACDiI,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACzD;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACjN,wBAAwB,CAAC8P,mBAAmB,CAAC9J,KAAK,CAAC6J,KAAK,CAAC,CAACzL,SAAS,CAAC;QACvE0I,IAAI,EAAGzI,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACwJ,OAAO,EAAE;YACpB,IAAI,CAAC/N,cAAc,CAAC+N,OAAO,CAACxJ,QAAQ,CAACkL,OAAO,IAAI,qDAAqD,EAAE,EAAE,EAAE;cAAEzB,IAAI,EAAE;YAAI,CAAE,CAAC;UAC5H,CAAC,MACI;YACH,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC1J,QAAQ,CAACkL,OAAO,IAAI,4EAA4E,EAAE,IAAI,EAAE;cAAEzB,IAAI,EAAE;YAAI,CAAE,CAAC;UAClJ;UACA,IAAI,CAACrQ,wBAAwB,CAACuH,SAAS,CAAC;UACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;QAC7C,CAAC;QACDiI,KAAK,EAAGA,KAAK,IAAI;UACff,OAAO,CAACe,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAC3D;OACD,CAAC;IACJ;EACF;EAEA;EACApS,6BAA6BA,CAAA;IAC3B,MAAMgR,SAAS,GAAG,IAAI,CAAClM,MAAM,CAACmM,IAAI,CAAC7T,kCAAkC,EAAE;MACrE+T,KAAK,EAAE,OAAO;MACdjF,IAAI,EAAE;QACJgJ,kBAAkB,EAAE,IAAI,CAACtD,yBAAyB,IAAI,EAAE;QACxDuD,WAAW,EAAE,IAAI,CAACvR,IAAI,IAAI;;KAE7B,CAAC;IAEFoN,SAAS,CAACI,WAAW,EAAE,CAAC7H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAIA,MAAM,IAAIA,MAAM,CAACgE,OAAO,EAAE;QAC5B,IAAI,CAACpQ,wBAAwB,CAACuH,SAAS,CAAC;QACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ;EAEAhK,mBAAmBA,CAAA;IACjB,IAAI,CAACiF,UAAU,CAACuP,UAAU,CAAC;MACzB5B,MAAM,EAAE,QAAQ;MAChB3D,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE,+EAA+E,IAAI,CAAC7N,qBAAqB,oHAAoH;MACnO6E,IAAI,EAAE;KACP,EAAGuO,OAAO,IAAI;MACb,IAAIA,OAAO,EAAE;QACX,IAAI,CAACzP,wBAAwB,CAC1BiQ,gBAAgB,CAAClO,MAAM,CAAC,IAAI,CAAC1F,qBAAqB,CAAC,CAAC,CAAC+H,SAAS,CAAC;UAC9D0I,IAAI,EAAGzI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAACwJ,OAAO,EAAE;cACpB,IAAI,CAAC/N,cAAc,CAAC+N,OAAO,CAACxJ,QAAQ,CAACkL,OAAO,IAAI,mCAAmC,EAAE,EAAE,EAAE;gBAAEzB,IAAI,EAAE;cAAI,CAAE,CAAC;YAC1G,CAAC,MACI;cACH,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC1J,QAAQ,CAACkL,OAAO,IAAI,0DAA0D,EAAE,IAAI,EAAE;gBAAEzB,IAAI,EAAE;cAAI,CAAE,CAAC;YAChI;YACA,IAAI,CAACrQ,wBAAwB,CAACuH,SAAS,CAAC;YACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;UAC7C,CAAC;UACDiI,KAAK,EAAGA,KAAK,IAAI;YACf;YACAf,OAAO,CAACe,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACtD;SACD,CAAC;MACN;IACF,CAAC,CAAC;IAEF;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEAvS,iBAAiBA,CAAA;IACf,MAAMmR,SAAS,GAAG,IAAI,CAAClM,MAAM,CAACmM,IAAI,CAAC5T,gCAAgC,EAAE;MACnE8T,KAAK,EAAE;KACR,CAAC;IAEFH,SAAS,CAACI,WAAW,EAAE,CAAC7H,SAAS,CAAEyF,MAAM,IAAI;MAC3C,IAAIA,MAAM,IAAIA,MAAM,CAAClB,IAAI,EAAE;QACzB,MAAMwE,QAAQ,GAAa,IAAIC,QAAQ,EAAE;QACzCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAACnN,IAAI,CAAC;QAC7C2R,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExD,MAAM,CAAClB,IAAI,CAAC;QACpCwE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAExD,MAAM,CAAClB,IAAI,CAACzH,IAAI,CAAC;QAC7C;QACA,IAAI,CAACrB,iBAAiB,CACnBqQ,gBAAgB,CAAC/C,QAAQ,CAAC,CAAC/I,SAAS,CAAC;UACpC0I,IAAI,EAAGzI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAACwJ,OAAO,EAAE;cACpB,IAAI,CAAC/N,cAAc,CAAC+N,OAAO,CAACxJ,QAAQ,CAACkL,OAAO,IAAI,4CAA4C,EAAE,EAAE,EAAE;gBAAEzB,IAAI,EAAE;cAAI,CAAE,CAAC;YACnH,CAAC,MACI;cACH,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC1J,QAAQ,CAACkL,OAAO,IAAI,mEAAmE,EAAE,IAAI,EAAE;gBAAEzB,IAAI,EAAE;cAAI,CAAE,CAAC;YACzI;YACA,IAAI,CAACrQ,wBAAwB,CAACuH,SAAS,CAAC;YACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;UAC7C,CAAC;UACDiI,KAAK,EAAGA,KAAK,IAAI;YACf;YACAf,OAAO,CAACe,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACvD;SACD,CAAC;MACN;IACF,CAAC,CAAC;EACJ;EAEA9R,4BAA4BA,CAAA;IAC1B,IAAI,CAAC8E,UAAU,CAACuP,UAAU,CAAC;MACzB5B,MAAM,EAAE,QAAQ;MAChB3D,KAAK,EAAE,iCAAiC;MACxCC,IAAI,EAAE,yCAAyC;MAC/ChJ,IAAI,EAAE;KACP,EAAGuO,OAAO,IAAI;MACb,IAAIA,OAAO,EAAE;QACX,IAAI,CAACzP,wBAAwB,CAACmQ,yBAAyB,EAAE,CAAC/L,SAAS,CAAC;UAClE0I,IAAI,EAAGzI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAACwJ,OAAO,EAAE;cACpB,IAAI,CAAC/N,cAAc,CAAC+N,OAAO,CAACxJ,QAAQ,CAACkL,OAAO,IAAI,wDAAwD,EAAE,EAAE,EAAE;gBAAEzB,IAAI,EAAE;cAAI,CAAE,CAAC;YAC/H,CAAC,MACI;cACH,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC1J,QAAQ,CAACkL,OAAO,IAAI,+EAA+E,EAAE,IAAI,EAAE;gBAAEzB,IAAI,EAAE;cAAI,CAAE,CAAC;YACrJ;YACA,IAAI,CAACrQ,wBAAwB,CAACuH,SAAS,CAAC;YACxC,IAAI,CAACY,2BAA2B,CAACZ,SAAS,CAAC;UAC7C,CAAC;UACDiI,KAAK,EAAGA,KAAK,IAAI;YACf;YACAf,OAAO,CAACe,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;UAC3E;SACD,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAkC,qBAAqBA,CAACD,GAAQ;IAC5B,IAAI,CAACjP,UAAU,CAACuP,UAAU,CAAC;MACzB5B,MAAM,EAAE,QAAQ;MAChB3D,KAAK,EAAE,0BAA0B;MACjCC,IAAI,EAAE,0CAA0C;MAChDhJ,IAAI,EAAE;KACP,EAAGuO,OAAO,IAAI;MACb,IAAIA,OAAO,EAAE;QACX,IAAI,CAACzP,wBAAwB,CAACoQ,sBAAsB,CAAClB,GAAG,EAAEI,YAAY,CAAC,CAAClL,SAAS,CAAC;UAChF0I,IAAI,EAAGzI,QAAQ,IAAI;YACjB,IAAIA,QAAQ,EAAE;cACZ,IAAI,CAACoE,YAAY,CAACpE,QAAQ,EAAE6K,GAAG,EAAEd,UAAU,CAAC;cAC5C,IAAI,CAACtO,cAAc,CAAC+N,OAAO,CAAC,iDAAiD,EAAE,EAAE,EAAE;gBAAEC,IAAI,EAAE;cAAI,CAAE,CAAC;YACpG,CAAC,MACI;cACH,IAAI,CAAChO,cAAc,CAACiO,IAAI,CAAC,wEAAwE,EAAE,IAAI,EAAE;gBAAED,IAAI,EAAE;cAAI,CAAE,CAAC;YAC1H;UACF,CAAC;UACDb,KAAK,EAAGA,KAAK,IAAI;YACf;YACAf,OAAO,CAACe,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACzD;SACD,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;;;uBAv4CW9N,gCAAgC,EAAA5G,EAAA,CAAA8X,iBAAA,CAAA9X,EAAA,CAAA+X,QAAA,GAAA/X,EAAA,CAAA8X,iBAAA,CAAAE,EAAA,CAAAC,MAAA,GAAAjY,EAAA,CAAA8X,iBAAA,CAAAI,EAAA,CAAAC,0BAAA,GAAAnY,EAAA,CAAA8X,iBAAA,CAAAM,EAAA,CAAAC,iCAAA,GAAArY,EAAA,CAAA8X,iBAAA,CAAAQ,EAAA,CAAAC,iBAAA,GAAAvY,EAAA,CAAA8X,iBAAA,CAAAU,EAAA,CAAAC,4BAAA,GAAAzY,EAAA,CAAA8X,iBAAA,CAAAY,EAAA,CAAAC,SAAA,GAAA3Y,EAAA,CAAA8X,iBAAA,CAAAc,EAAA,CAAAC,kBAAA,GAAA7Y,EAAA,CAAA8X,iBAAA,CAAAgB,EAAA,CAAAC,iBAAA,GAAA/Y,EAAA,CAAA8X,iBAAA,CAAAkB,EAAA,CAAAC,cAAA,GAAAjZ,EAAA,CAAA8X,iBAAA,CAAAoB,GAAA,CAAAC,cAAA,GAAAnZ,EAAA,CAAA8X,iBAAA,CAAAsB,GAAA,CAAAC,wBAAA,GAAArZ,EAAA,CAAA8X,iBAAA,CAAAwB,GAAA,CAAAC,iBAAA,GAAAvZ,EAAA,CAAA8X,iBAAA,CAAA0B,GAAA,CAAAC,wBAAA;IAAA;EAAA;;;YAAhC7S,gCAAgC;MAAA8S,SAAA;MAAAC,QAAA,GAAA3Z,EAAA,CAAA4Z,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3D7Cla,EAAA,CAAAC,cAAA,uBAAwD;UAAzCD,EAAA,CAAAS,UAAA,+BAAA2Z,qFAAAzW,MAAA;YAAA,OAAqBwW,GAAA,CAAAtI,UAAA,CAAAlO,MAAA,CAAkB;UAAA,EAAC;UAI/C3D,EAHN,CAAAC,cAAA,iBAAyC,aACE,aACV,mBAIuE;UAA9ED,EAAA,CAAAS,UAAA,wBAAA4Z,0EAAA1W,MAAA;YAAA,OAAcwW,GAAA,CAAAzN,gBAAA,CAAA/I,MAAA,CAAwB;UAAA,EAAC;UAG/D3D,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,aAAgE,aACrC;UAuBvBD,EAtBA,CAAAoD,UAAA,IAAAkX,gDAAA,kBAAiE,IAAAC,kDAAA,oBAGxB,IAAAC,kDAAA,oBAIG,KAAAC,mDAAA,oBAIX,KAAAC,mDAAA,oBAIA,KAAAC,mDAAA,oBAIA,KAAAC,mDAAA,oBAIA;UAIrC5a,EADE,CAAAG,YAAA,EAAM,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAA4C,eACC,qBACJ;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAEnEH,EADF,CAAAC,cAAA,0BAAiD,sBAEJ;UADID,EAAA,CAAAyD,gBAAA,yBAAAoX,6EAAAlX,MAAA;YAAA3D,EAAA,CAAA6D,kBAAA,CAAAsW,GAAA,CAAA9R,YAAA,EAAA1E,MAAA,MAAAwW,GAAA,CAAA9R,YAAA,GAAA1E,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACrE3D,EAAA,CAAAS,UAAA,6BAAAqa,iFAAAnX,MAAA;YAAA,OAAmBwW,GAAA,CAAA9H,YAAA,CAAA1O,MAAA,CAAoB;UAAA,EAAC;UACxC3D,EAAA,CAAAoD,UAAA,KAAA2X,uDAAA,yBAA2D;UAKjE/a,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,eAA2C,qBACJ;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE5DH,EADF,CAAAC,cAAA,0BAAiD,sBAEF;UADLD,EAAA,CAAAyD,gBAAA,yBAAAuX,6EAAArX,MAAA;YAAA3D,EAAA,CAAA6D,kBAAA,CAAAsW,GAAA,CAAA/Q,kBAAA,EAAAzF,MAAA,MAAAwW,GAAA,CAAA/Q,kBAAA,GAAAzF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UACpE3D,EAAA,CAAAS,UAAA,6BAAAwa,iFAAAtX,MAAA;YAAA,OAAmBwW,GAAA,CAAAzH,cAAA,CAAA/O,MAAA,CAAsB;UAAA,EAAC;UAC1C3D,EAAA,CAAAoD,UAAA,KAAA8X,uDAAA,yBAAoF;UAK1Flb,EAFI,CAAAG,YAAA,EAAa,EACE,EACb;UAEJH,EADF,CAAAC,cAAA,eAA2C,qBACJ;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE1DH,EADF,CAAAC,cAAA,0BAAiD,iBAC0B;UAAjCD,EAAA,CAAAyD,gBAAA,2BAAA0X,0EAAAxX,MAAA;YAAA3D,EAAA,CAAA6D,kBAAA,CAAAsW,GAAA,CAAA3H,gBAAA,EAAA7O,MAAA,MAAAwW,GAAA,CAAA3H,gBAAA,GAAA7O,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAE1E3D,EAFI,CAAAG,YAAA,EAAyE,EAC1D,EACb;UAEJH,EADF,CAAAC,cAAA,eAAgE,kBAC+B;UAArDD,EAAA,CAAAS,UAAA,mBAAA2a,mEAAA;YAAA,OAASjB,GAAA,CAAA1H,QAAA,EAAU;UAAA,EAAC;UAC1DzS,EAAA,CAAAE,MAAA,gBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAyC,cACV,qBAKW;UAApCD,EADyC,CAAAS,UAAA,wBAAA4a,2EAAA1X,MAAA;YAAA,OAAcwW,GAAA,CAAA3N,eAAA,CAAA7I,MAAA,CAAuB;UAAA,EAAC,yBAAA2X,4EAAA3X,MAAA;YAAA,OAChEwW,GAAA,CAAAxH,WAAA,CAAAhP,MAAA,CAAmB;UAAA,EAAC;UAI3C3D,EAHM,CAAAG,YAAA,EAAY,EACR,EACF,EACE;UAEVH,EAAA,CAAAoD,UAAA,KAAAmY,oDAAA,wBAA2E;UA2F7Evb,EAAA,CAAAG,YAAA,EAAgB;;;UA/KGH,EAAA,CAAAI,SAAA,GAAe;UAGkCJ,EAHjD,CAAA4B,UAAA,OAAAuY,GAAA,CAAA7Q,QAAA,CAAe,YAAA6Q,GAAA,CAAA5Q,sBAAA,CAAmC,4BACO,cAAA4Q,GAAA,CAAAxU,iBAAA,CAAgC,aAAAwU,GAAA,CAAAvU,SAAA,CAC5E,0BAA0B,wBAAwB,uBAAuB,kBAC9E,oBAAA5F,EAAA,CAAA0G,eAAA,KAAAC,GAAA,EAA8E;UAMjE3G,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAA4B,UAAA,SAAAuY,GAAA,CAAA3Z,yBAAA,CAA+B;UAEtDR,EAAA,CAAAI,SAAA,EAAyB;UAAzBJ,EAAA,CAAA4B,UAAA,SAAAuY,GAAA,CAAAlP,mBAAA,CAAyB;UAIzBjL,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAA4B,UAAA,SAAAuY,GAAA,CAAAhP,uBAAA,CAA6B;UAI7BnL,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAA4B,UAAA,SAAAuY,GAAA,CAAAzQ,UAAA,CAAgB;UAIhB1J,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAA4B,UAAA,SAAAuY,GAAA,CAAAzQ,UAAA,CAAgB;UAIhB1J,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAA4B,UAAA,SAAAuY,GAAA,CAAAzQ,UAAA,CAAgB;UAIhB1J,EAAA,CAAAI,SAAA,EAAiC;UAAjCJ,EAAA,CAAA4B,UAAA,SAAAuY,GAAA,CAAAzQ,UAAA,IAAAyQ,GAAA,CAAAxQ,aAAA,CAAiC;UAUO3J,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAiG,gBAAA,UAAAkU,GAAA,CAAA9R,YAAA,CAAwB;UAErCrI,EAAA,CAAAI,SAAA,EAAO;UAAPJ,EAAA,CAAA4B,UAAA,YAAAuY,GAAA,CAAAjU,IAAA,CAAO;UASDlG,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAiG,gBAAA,UAAAkU,GAAA,CAAA/Q,kBAAA,CAA8B;UAEpCpJ,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAA4B,UAAA,YAAAuY,GAAA,CAAA9Q,uBAAA,CAA0B;UASpBrJ,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAiG,gBAAA,YAAAkU,GAAA,CAAA3H,gBAAA,CAA8B;UAW7DxS,EAAA,CAAAI,SAAA,GAAc;UAGAJ,EAHd,CAAA4B,UAAA,OAAAuY,GAAA,CAAAhS,OAAA,CAAc,YAAAgS,GAAA,CAAA1R,qBAAA,CAAsD,4BACb,cAAA0R,GAAA,CAAA/R,gBAAA,CAA+B,aAAA+R,GAAA,CAAAjR,QAAA,CAC1E,oBAAAlJ,EAAA,CAAA0G,eAAA,KAAAC,GAAA,EAAsC,0BAA0B,yBAAyB,uBACxF,kBAAkB;UAOtC3G,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAA4B,UAAA,SAAAuY,GAAA,CAAAjP,uBAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}