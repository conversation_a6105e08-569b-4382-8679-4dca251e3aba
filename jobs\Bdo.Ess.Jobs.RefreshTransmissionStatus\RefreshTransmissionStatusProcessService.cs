﻿using IdentityModel;
using IdentityModel.Client;

using Microsoft.Extensions.Configuration;

using Volo.Abp.DependencyInjection;
using Volo.Abp.IdentityModel;

namespace Bdo.Ess.Jobs.RefreshTransmissionStatus;

public class RefreshTransmissionStatusProcessService : ITransientDependency
{
	private readonly IIdentityModelAuthenticationService _authenticationService;
	private readonly IConfiguration _configuration;

	public RefreshTransmissionStatusProcessService(
		IIdentityModelAuthenticationService authenticationService,
		IConfiguration configuration)
	{
		_authenticationService = authenticationService;
		_configuration = configuration;
	}

	public async Task RunAsync()
	{
		try
		{
			Console.WriteLine("Start of RefreshTransmissionStatus Job");
			await RefreshTransmissionStatuses();
			Console.WriteLine("End of RefreshTransmissionStatus Job");
		}
		catch (Exception ex)
		{
			Console.WriteLine(ex.ToString());
			throw;
		}
	}

	private async Task RefreshTransmissionStatuses()
	{
		try
		{
			var apiPath = "api/CtsIntegrationService/CtsPackageRequest/refresh-transmission-status";
			var accessToken = await GetAccessToken();

			using var httpClient = new HttpClient { Timeout = TimeSpan.FromHours(24) };
			httpClient.SetBearerToken(accessToken);

			var baseUrl = _configuration["RemoteServices:CtsIntegrationService:BaseUrl"] ?? string.Empty;
			var url = $"{baseUrl}{apiPath}";

			var responseMessage = await httpClient.PostAsync(url, null);

			if (!responseMessage.IsSuccessStatusCode)
			{
				Console.WriteLine($"RefreshTransmissionStatus Job failed: {responseMessage.StatusCode}");
			}
		}
		catch (Exception ex)
		{
			Console.WriteLine("Error during RefreshTransmissionStatuses execution:");
			Console.WriteLine(ex.ToString());
		}
	}

	private async Task<string> GetAccessToken()
	{
		var accessToken = await _authenticationService.GetAccessTokenAsync(
			new IdentityClientConfiguration(
				_configuration["IdentityClients:Default:Authority"]!,
				_configuration["IdentityClients:Default:Scope"]!,
				_configuration["IdentityClients:Default:ClientId"]!,
				_configuration["IdentityClients:Default:ClientSecret"]!,
				grantType: OidcConstants.GrantTypes.ClientCredentials,
				requireHttps: false
			)
		);
		return accessToken;
	}
}
