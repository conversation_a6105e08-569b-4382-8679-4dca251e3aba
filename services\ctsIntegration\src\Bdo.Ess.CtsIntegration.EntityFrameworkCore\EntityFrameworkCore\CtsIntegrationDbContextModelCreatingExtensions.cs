using Bdo.Ess.CtsIntegration.Entities;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Bdo.Ess.CtsIntegration.EntityFrameworkCore;

public static class CtsIntegrationDbContextModelCreatingExtensions
{
    public static void ConfigureCtsIntegration(this ModelBuilder builder)
    {
        Check.NotNull(builder, nameof(builder));

        builder.Entity<BahamasCertificate>(entity =>
        {
            entity.Property(x => x.Id)
            .ValueGeneratedOnAdd()
            .HasDefaultValueSql("newsequentialid()");

            entity.Property(p => p.PublicKey).IsRequired().IsUnicode(false);
            entity.Property(p => p.CertificateContent).IsRequired().HasColumnType("VARBINARY(MAX)");           
            entity.Property(p => p.CertificateContentType).IsRequired().HasMaxLength(100).IsUnicode(true);
            entity.Property(p => p.CertificateFileName).IsRequired().HasMaxLength(256).IsUnicode(true);
            entity.Property(p => p.CertificatePassword).HasMaxLength(256).IsUnicode(true);
            entity.Property(p => p.ExpiredAt).IsRequired();
            entity.Property(p => p.IsActive).IsRequired().HasDefaultValue(false);
        });

        builder.Entity<CtsPackageRequest>(entity =>
        {
            entity.Property(x => x.Id)
            .ValueGeneratedOnAdd()
            .HasDefaultValueSql("newsequentialid()");

            entity.Property(p => p.CtsPackageFileName).HasMaxLength(256).IsUnicode(false);
            entity.Property(p => p.ReceiverCountryCode).IsRequired().HasMaxLength(2).IsUnicode(false);
            entity.Property(p => p.MetaCountryCode).HasMaxLength(6).IsUnicode(false);
            entity.Property(p => p.FiscalYear).IsRequired();
            entity.Property(p => p.ExchangeReason);           
            entity.Property(p => p.XmlPayload).IsUnicode(true);
            entity.Property(p => p.XmlPayloadUrl).HasMaxLength(256).IsUnicode(false);
            entity.Property(p => p.PackageZipUrl).HasMaxLength(256).IsUnicode(false);            
            entity.Property(p => p.TransmissionStatus).HasMaxLength(10).IsUnicode(false);
            entity.Property(p => p.IsExcludeCtsUpload).IsRequired().HasDefaultValue(false);
            entity.Property(p => p.StatusUpdatedAt);
            entity.Property(p => p.HasExchangeRecords).IsRequired();
            entity.Property(p => p.ProcessInfo).IsUnicode(false);
            entity.Property(p => p.MessageRefId).HasMaxLength(50).IsUnicode(false);
        });

        builder.Entity<CtsPackageComment>(entity =>
        {
            entity.Property(x => x.Id)
            .ValueGeneratedOnAdd()
            .HasDefaultValueSql("newsequentialid()");

            entity.Property(p => p.CtsPackageId).IsRequired();
            entity.Property(p => p.Comment).HasMaxLength(512).IsUnicode(true);
            entity.Property(p => p.CreatorName).HasMaxLength(50).IsUnicode(true).IsRequired();
            entity.Property(p => p.TransmissionStatus).HasMaxLength(10).IsUnicode(false).IsRequired(false);
        });

		builder.Entity<CountryCertificate>(b =>
		{
			b.ToTable("CountryCertificates");
			b.ConfigureByConvention();
            b.Property(x => x.Id)
                .ValueGeneratedOnAdd()
                .HasDefaultValueSql("newsequentialid()");
            b.HasIndex(x => x.CountryCode);
		});

		builder.Entity<BahamasCtsSetting>(entity =>
		{
			entity.Property(x => x.Id)
				.ValueGeneratedOnAdd()
				.HasDefaultValueSql("newsequentialid()");

			entity.Property(p => p.SystemUserName).IsRequired().HasMaxLength(50).IsUnicode(false);
			entity.Property(p => p.SystemUserPassword).IsRequired().HasMaxLength(512).IsUnicode(false);
			entity.Property(p => p.SftpUserName).IsRequired().HasMaxLength(50).IsUnicode(false);
			entity.Property(p => p.SftpSSHKey).IsRequired().IsUnicode(false);
			//entity.Property(p => p.SftpSSHKeyPassphrase).HasMaxLength(50).IsUnicode(false);
			entity.Property(p => p.CtsPublicCertificate).IsUnicode(false);
		});

	}
}
