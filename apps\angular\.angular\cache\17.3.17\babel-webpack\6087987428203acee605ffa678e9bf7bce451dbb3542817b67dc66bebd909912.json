{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@app/shared/services/sweetalert.service\";\nimport * as i4 from \"@angular/material/input\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/cdk/text-field\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@ngx-validate/core\";\nexport class RegeneratePacketDialogComponent {\n  constructor(dialogRef, data, fb, sweetAlert) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.fb = fb;\n    this.sweetAlert = sweetAlert;\n    this.form = this.fb.group({\n      comment: [null, [Validators.required, Validators.maxLength(512)]]\n    });\n  }\n  onSubmit() {\n    if (this.form.invalid) {\n      this.form.get('comment')?.markAsTouched();\n      return;\n    }\n    this.dialogRef.close({\n      comment: this.form.value.comment\n    });\n  }\n  onCancel() {\n    if (this.form.dirty) {\n      this.sweetAlert.fireDialog({\n        action: \"delete\",\n        title: \"Are you sure you want to close?\",\n        text: \"Any unsaved changes may be lost\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.dialogRef.close();\n        }\n      });\n    } else {\n      this.dialogRef.close();\n    }\n  }\n  static {\n    this.ɵfac = function RegeneratePacketDialogComponent_Factory(t) {\n      return new (t || RegeneratePacketDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.SweetAlertService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegeneratePacketDialogComponent,\n      selectors: [[\"app-regenerate-packet-dialog\"]],\n      decls: 18,\n      vars: 2,\n      consts: [[\"mat-dialog-title\", \"\"], [1, \"row\"], [1, \"col-8\", \"title\"], [1, \"col-4\", \"text-end\", \"modal-action-button\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"fill\", 1, \"w-100\"], [\"matInput\", \"\", \"formControlName\", \"comment\", \"rows\", \"3\", \"placeholder\", \"Your Comment\", \"cdkTextareaAutosize\", \"\", \"cdkAutosizeMinRows\", \"3\", \"maxlength\", \"500\"], [\"align\", \"end\", 1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"type\", \"submit\", 1, \"ui-button\", 3, \"disabled\"]],\n      template: function RegeneratePacketDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"Regenerate Packet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function RegeneratePacketDialogComponent_Template_button_click_5_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function RegeneratePacketDialogComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(8, \"mat-dialog-content\")(9, \"mat-form-field\", 7)(10, \"mat-label\");\n          i0.ɵɵtext(11, \"Comment (Maximum Length Allowed: 500 characters)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"textarea\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"mat-dialog-actions\", 9)(14, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function RegeneratePacketDialogComponent_Template_button_click_14_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(15, \"Cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 11);\n          i0.ɵɵtext(17, \"Regenerate\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"disabled\", ctx.form.invalid);\n        }\n      },\n      dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MaxLengthValidator, i2.FormGroupDirective, i2.FormControlName, i4.MatInput, i5.MatFormField, i5.MatLabel, i6.CdkTextareaAutosize, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i7.MatButton, i8.ValidationGroupDirective, i8.ValidationDirective],\n      styles: [\".title[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n  color: #00779b;\\n  display: block;\\n}\\n\\n.modal-action-button[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n\\n.w-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  display: flex;\\n}\\n\\n.required[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInJlZ2VuZXJhdGUtcGFja2V0LWRpYWxvZy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLGNBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0VBQ0EsYUFBQTtBQUNGOztBQUVBO0VBQ0UsVUFBQTtBQUNGIiwiZmlsZSI6InJlZ2VuZXJhdGUtcGFja2V0LWRpYWxvZy5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi50aXRsZSB7XHJcbiAgZm9udC1zaXplOiAxLjNlbTtcclxuICBjb2xvcjogIzAwNzc5YjtcclxuICBkaXNwbGF5OiBibG9jaztcclxufVxyXG5cclxuLm1vZGFsLWFjdGlvbi1idXR0b24ge1xyXG4gIGZvbnQtc2l6ZTogMWVtO1xyXG59XHJcblxyXG4udy0xMDAge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4uYWN0aW9uLWJ1dHRvbnMge1xyXG4gIG1hcmdpbi10b3A6IDE2cHg7XHJcbiAgZGlzcGxheTogZmxleDsgXHJcbn1cclxuXHJcbi5yZXF1aXJlZCB7XHJcbiAgY29sb3I6IHJlZDtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaW5mb3JtYXRpb24tZXhjaGFuZ2UvY29udGFpbmVycy9yZWdlbmVyYXRlLXBhY2tldC1kaWFsb2cvcmVnZW5lcmF0ZS1wYWNrZXQtZGlhbG9nLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsY0FBQTtBQUNGOztBQUVBO0VBQ0UsY0FBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtBQUNGOztBQUVBO0VBQ0UsZ0JBQUE7RUFDQSxhQUFBO0FBQ0Y7O0FBRUE7RUFDRSxVQUFBO0FBQ0Y7QUFDQSxvMEJBQW8wQiIsInNvdXJjZXNDb250ZW50IjpbIi50aXRsZSB7XHJcbiAgZm9udC1zaXplOiAxLjNlbTtcclxuICBjb2xvcjogIzAwNzc5YjtcclxuICBkaXNwbGF5OiBibG9jaztcclxufVxyXG5cclxuLm1vZGFsLWFjdGlvbi1idXR0b24ge1xyXG4gIGZvbnQtc2l6ZTogMWVtO1xyXG59XHJcblxyXG4udy0xMDAge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4uYWN0aW9uLWJ1dHRvbnMge1xyXG4gIG1hcmdpbi10b3A6IDE2cHg7XHJcbiAgZGlzcGxheTogZmxleDsgXHJcbn1cclxuXHJcbi5yZXF1aXJlZCB7XHJcbiAgY29sb3I6IHJlZDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "MAT_DIALOG_DATA", "RegeneratePacketDialogComponent", "constructor", "dialogRef", "data", "fb", "<PERSON><PERSON><PERSON><PERSON>", "form", "group", "comment", "required", "max<PERSON><PERSON><PERSON>", "onSubmit", "invalid", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "close", "value", "onCancel", "dirty", "fireDialog", "action", "title", "text", "type", "confirm", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "FormBuilder", "i3", "SweetAlertService", "selectors", "decls", "vars", "consts", "template", "RegeneratePacketDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "RegeneratePacketDialogComponent_Template_button_click_5_listener", "ɵɵelement", "RegeneratePacketDialogComponent_Template_form_ngSubmit_7_listener", "RegeneratePacketDialogComponent_Template_button_click_14_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\regenerate-packet-dialog\\regenerate-packet-dialog.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\regenerate-packet-dialog\\regenerate-packet-dialog.component.html"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\r\nimport { SweetAlertService } from '@app/shared/services/sweetalert.service';\r\n\r\n@Component({\r\n  selector: 'app-regenerate-packet-dialog',\r\n  templateUrl: './regenerate-packet-dialog.component.html',\r\n  styleUrls: ['./regenerate-packet-dialog.component.scss']\r\n})\r\nexport class RegeneratePacketDialogComponent {\r\n  form: FormGroup;\r\n\r\n  constructor(\r\n    public dialogRef: MatDialogRef<RegeneratePacketDialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: any,\r\n    private fb: FormBuilder,\r\n    private sweetAlert: SweetAlertService,\r\n  ) {\r\n    this.form = this.fb.group({      \r\n      comment: [null, [Validators.required, Validators.maxLength(512)]]\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.form.invalid) {\r\n      this.form.get('comment')?.markAsTouched();\r\n      return;\r\n    }\r\n    this.dialogRef.close({ comment: this.form.value.comment });\r\n  }\r\n\r\n  onCancel() {\r\n    if (this.form.dirty) {\r\n      this.sweetAlert.fireDialog({\r\n        action: \"delete\", title: \"Are you sure you want to close?\",\r\n        text: \"Any unsaved changes may be lost\", type: \"confirm\"\r\n      }, (confirm) => {\r\n        if (confirm) {\r\n          this.dialogRef.close();\r\n        }\r\n      });\r\n    } else {\r\n      this.dialogRef.close();\r\n    }\r\n  }\r\n}\r\n", "<div mat-dialog-title>\r\n  <div class=\"row\">\r\n    <div class=\"col-8 title\">Regenerate Packet</div>\r\n    <div class=\"col-4 text-end modal-action-button\">\r\n      <button type=\"button\" mat-raised-button class=\"ui-button\" (click)=\"onCancel()\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n<form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\">\r\n  <mat-dialog-content>\r\n    <mat-form-field appearance=\"fill\" class=\"w-100\">\r\n      <mat-label>Comment (Maximum Length Allowed: 500 characters)</mat-label>\r\n      <textarea matInput formControlName=\"comment\" rows=\"3\" placeholder=\"Your Comment\" cdkTextareaAutosize\r\n        cdkAutosizeMinRows=\"3\" maxlength=\"500\"></textarea>\r\n    </mat-form-field>\r\n  </mat-dialog-content>\r\n  <mat-dialog-actions align=\"end\" class=\"action-buttons\">\r\n    <button mat-raised-button type=\"button\" (click)=\"onCancel()\">Cancel</button>\r\n    <button mat-raised-button class=\"ui-button\" type=\"submit\" [disabled]=\"form.invalid\">Regenerate</button>\r\n  </mat-dialog-actions>\r\n</form>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAAuBC,eAAe,QAAQ,0BAA0B;;;;;;;;;;AAQxE,OAAM,MAAOC,+BAA+B;EAG1CC,YACSC,SAAwD,EAC/BC,IAAS,EACjCC,EAAe,EACfC,UAA6B;IAH9B,KAAAH,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC5B,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IAElB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACF,EAAE,CAACG,KAAK,CAAC;MACxBC,OAAO,EAAE,CAAC,IAAI,EAAE,CAACV,UAAU,CAACW,QAAQ,EAAEX,UAAU,CAACY,SAAS,CAAC,GAAG,CAAC,CAAC;KACjE,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,IAAI,CAACM,OAAO,EAAE;MACrB,IAAI,CAACN,IAAI,CAACO,GAAG,CAAC,SAAS,CAAC,EAAEC,aAAa,EAAE;MACzC;IACF;IACA,IAAI,CAACZ,SAAS,CAACa,KAAK,CAAC;MAAEP,OAAO,EAAE,IAAI,CAACF,IAAI,CAACU,KAAK,CAACR;IAAO,CAAE,CAAC;EAC5D;EAEAS,QAAQA,CAAA;IACN,IAAI,IAAI,CAACX,IAAI,CAACY,KAAK,EAAE;MACnB,IAAI,CAACb,UAAU,CAACc,UAAU,CAAC;QACzBC,MAAM,EAAE,QAAQ;QAAEC,KAAK,EAAE,iCAAiC;QAC1DC,IAAI,EAAE,iCAAiC;QAAEC,IAAI,EAAE;OAChD,EAAGC,OAAO,IAAI;QACb,IAAIA,OAAO,EAAE;UACX,IAAI,CAACtB,SAAS,CAACa,KAAK,EAAE;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACb,SAAS,CAACa,KAAK,EAAE;IACxB;EACF;;;uBAnCWf,+BAA+B,EAAAyB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAKhC3B,eAAe,GAAA0B,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YALdhC,+BAA+B;MAAAiC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRxCd,EAFJ,CAAAgB,cAAA,aAAsB,aACH,aACU;UAAAhB,EAAA,CAAAiB,MAAA,wBAAiB;UAAAjB,EAAA,CAAAkB,YAAA,EAAM;UAE9ClB,EADF,CAAAgB,cAAA,aAAgD,gBACiC;UAArBhB,EAAA,CAAAmB,UAAA,mBAAAC,iEAAA;YAAA,OAASL,GAAA,CAAAvB,QAAA,EAAU;UAAA,EAAC;UAC5EQ,EAAA,CAAAqB,SAAA,WAA4B;UAIpCrB,EAHM,CAAAkB,YAAA,EAAS,EACL,EACF,EACF;UACNlB,EAAA,CAAAgB,cAAA,cAAiD;UAAxBhB,EAAA,CAAAmB,UAAA,sBAAAG,kEAAA;YAAA,OAAYP,GAAA,CAAA7B,QAAA,EAAU;UAAA,EAAC;UAG1Cc,EAFJ,CAAAgB,cAAA,yBAAoB,wBAC8B,iBACnC;UAAAhB,EAAA,CAAAiB,MAAA,wDAAgD;UAAAjB,EAAA,CAAAkB,YAAA,EAAY;UACvElB,EAAA,CAAAqB,SAAA,mBACoD;UAExDrB,EADE,CAAAkB,YAAA,EAAiB,EACE;UAEnBlB,EADF,CAAAgB,cAAA,6BAAuD,kBACQ;UAArBhB,EAAA,CAAAmB,UAAA,mBAAAI,kEAAA;YAAA,OAASR,GAAA,CAAAvB,QAAA,EAAU;UAAA,EAAC;UAACQ,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAkB,YAAA,EAAS;UAC5ElB,EAAA,CAAAgB,cAAA,kBAAoF;UAAAhB,EAAA,CAAAiB,MAAA,kBAAU;UAElGjB,EAFkG,CAAAkB,YAAA,EAAS,EACpF,EAChB;;;UAZDlB,EAAA,CAAAwB,SAAA,GAAkB;UAAlBxB,EAAA,CAAAyB,UAAA,cAAAV,GAAA,CAAAlC,IAAA,CAAkB;UAUsCmB,EAAA,CAAAwB,SAAA,GAAyB;UAAzBxB,EAAA,CAAAyB,UAAA,aAAAV,GAAA,CAAAlC,IAAA,CAAAM,OAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}