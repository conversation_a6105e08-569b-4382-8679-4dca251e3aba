﻿using Bdo.Ess.AuditService.Encryption;
using Bdo.Ess.AuditService.Entities;
using Bdo.Ess.AuditService.Models;
using Bdo.Ess.AuditService.Permissions;
using Bdo.Ess.AuditService.Utility;
using Bdo.Ess.LookupService.Declaration;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Constants.Saas;
using Bdo.Ess.Shared.Hosting.Microservices;
using Bdo.Ess.Shared.Hosting.Microservices.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Audit;
using Bdo.Ess.Shared.Utility.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Caching;
using Volo.Abp.Identity;

namespace Bdo.Ess.AuditService
{
    public class AuditAppService : AuditServiceAppService, IAuditAppService
    {
        private readonly AuditLogManager _manager;
        private readonly ILookupDataProvider _lookupDataProvider;
        private readonly BdoTenantService _tenantService;
        private readonly IAuthorizationService _authorizationService;
        private readonly IDistributedCache<List<AuditSearchUserDto>, string> _auditSearchUsersCache;
        private readonly IIdentityUserAppService _identityUserAppService;
        private readonly IAuditEncryptionManager _auditEncryptionManager;

        /// <summary>
        ///  Note: _auditWebInfo is scoped dependency instance,
        ///  so, it will be shared between HttpApi and AppService
        ///  Work for Auditing purpose to get client IP address.
        /// </summary>
        private readonly IAuditWebInfo _auditWebInfo;

        public AuditAppService(
             AuditLogManager manager,
             ILookupDataProvider lookupDataProvider,
             BdoTenantService tenantService,
             IAuthorizationService authorizationService,
             IDistributedCache<List<AuditSearchUserDto>, string> auditSearchUsersCache,
             IIdentityUserAppService identityUserAppService,
             IAuditEncryptionManager auditEncryptionManager,
             IAuditWebInfo auditWebInfo
         )
        {
            this._manager = manager;
            _lookupDataProvider = lookupDataProvider;
            this._tenantService = tenantService;
            this._authorizationService = authorizationService;
            this._auditSearchUsersCache = auditSearchUsersCache;
            this._identityUserAppService = identityUserAppService;
            _auditEncryptionManager = auditEncryptionManager;
            this._auditWebInfo = auditWebInfo;
        }

        public async Task<bool> CreateAuditLog<T>(AuditBaseEto<T> newAuditLog)
        {
            bool result = false;

            try
            {
                if (newAuditLog != null)
                {
                    if (newAuditLog.Action == AuditActionEnum.FailedLogin)
                    {
                        // TODO.
                        ////
                        //// Count the number of failed login attempts in every 10 minutes.
                        ////
                        //int numberOfFailedAttempts = await this._manager.GetUserLoginFailedConsecutiveAttempts(newAuditLog.UserId);

                        //var entity = newAuditLog.NewValue as AuditUserFailedLoginEto;
                        //if (entity != null)
                        //{
                        //    entity.NewValue.NumberOfFailedAttempts = numberOfFailedAttempts;
                        //}
                    }
                    else if (newAuditLog.Action == AuditActionEnum.UserLockedBySystem)
                    {
                        bool isInitialLockout = await this._manager.CheckIsUserInitialLockout(newAuditLog.UserId);

                        if (!isInitialLockout)
                        {
                            // Skip this event, since this is not the first time user account locked.
                            return true;
                        }
                    }

                    //
                    // Double check if the new AuditLog has correctly assign user id and user name.
                    // Note: For some cases, app service calling another app service could caused the CurrentUser is null,
                    // thus, caused the newAuditLog.UserId is null.
                    // Solution is get user id from client side through http request header called "Ess-Audit-Uid".
                    //
                    try
                    {
                        if (string.IsNullOrEmpty(newAuditLog.UserName))
                        {
                            //
                            // Note: CurrentUser and CurrentTenant are null here, since CreatedAuditLog method called by Event Bus.
                            //
                            if (newAuditLog.UserId.HasValue && newAuditLog.UserId != Guid.Empty)
                            {
                                var user = await this.GetAuditUser(newAuditLog.UserId.Value);
                                if (user != null)
                                {
                                    newAuditLog.UserName = user.UserName;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        this.Logger.LogError(ex, "CreateAuditLog failed in GetAuditUser call. Audit Action: {0}, UserId: {1}", newAuditLog?.Action.GetEnumDescription(), newAuditLog?.UserId);
                    }

                    AuditLog newEntity = new AuditLog();

                    if (!string.IsNullOrEmpty(newAuditLog.UserName) && newAuditLog.UserName.Trim().Length > 0)
                    {
                        newEntity.UserName = newAuditLog.UserName;
                    }

                    newEntity.UserId = newAuditLog.UserId;

                    if (!string.IsNullOrEmpty(newAuditLog.IPAddress) && newAuditLog.IPAddress.Trim().Length > 0)
                    {
                        newEntity.IPAddress = newAuditLog.IPAddress;
                    }

                    newEntity.Action = newAuditLog.Action;
                    newEntity.AuditDateTime = newAuditLog.AuditDateTime;
                    newEntity.TenantId = newAuditLog.TenantId;
                    newEntity.Source = await this.GetSource(newAuditLog.TenantId);

                    if (!string.IsNullOrEmpty(newAuditLog.EntityName) && newAuditLog.EntityName.Trim().Length > 0)
                    {
                        newEntity.EntityName = newAuditLog.EntityName;
                    }

                    newEntity.EntityId = newAuditLog.EntityId;
                    newEntity.DeclarationId = newAuditLog.DeclarationId;

                    if (!string.IsNullOrEmpty(newAuditLog.EntityFormationNumber) && newAuditLog.EntityFormationNumber.Trim().Length > 0)
                    {
                        newEntity.EntityFormationNumber = newAuditLog.EntityFormationNumber;
                    }

                    if (!string.IsNullOrEmpty(newAuditLog.EntityUniqueId) && newAuditLog.EntityUniqueId.Trim().Length > 0)
                    {
                        newEntity.EntityUniqueId = newAuditLog.EntityUniqueId;
                    }

                    newEntity.ESPeriodEnd = newAuditLog.ESPeriodEnd;
                    newEntity.Description = string.Format("{0}: {1}", newAuditLog.Action.GetEnumDescription(), !string.IsNullOrEmpty(newAuditLog.UserName) ? newAuditLog.UserName : "System");

                    if (newAuditLog.OldValue != null)
                    {
                        newEntity.OldValue = JsonConvert.SerializeObject(newAuditLog.OldValue);
                    }

                    if (newAuditLog.NewValue != null)
                    {
                        newEntity.NewValue = JsonConvert.SerializeObject(newAuditLog.NewValue);
                    }

                    result = await this._manager.CreateAuditLog(newEntity);
                }
            }
            catch (Exception ex)
            {
                this.Logger.LogError(ex, "CreateAuditLog failed. Audit Action: {0}", newAuditLog?.Action.GetEnumDescription());
                result = false;
            }

            return result;
        }

        private void DecrytData(AuditActionDto? action, AuditLogDto auditLogDto)
        {
            if (action == null) return;
            if ((AuditActionEnum)action.AuditActionId == AuditActionEnum.UpdateSpontaneousInformationExchangeDetails)
            {
                if (auditLogDto.OldValue != null && auditLogDto.OldValue.Length > 0)
                {
                    var oldValue = JsonConvert.DeserializeObject<AuditSpontaneousInformationExchangeDetailsDto>(auditLogDto.OldValue);
                    oldValue = _auditEncryptionManager.DecryptAuditInformationDto(oldValue ?? new());
                    auditLogDto.OldValue = JsonConvert.SerializeObject(oldValue);
                }
                if (auditLogDto.NewValue != null && auditLogDto.NewValue.Length > 0)
                {
                    var newValue = JsonConvert.DeserializeObject<AuditSpontaneousInformationExchangeDetailsDto>(auditLogDto.NewValue);
                    newValue = _auditEncryptionManager.DecryptAuditInformationDto(newValue ?? new());
                    auditLogDto.NewValue = JsonConvert.SerializeObject(newValue);
                }
            }
            else if ((AuditActionEnum)action.AuditActionId == AuditActionEnum.CreateESDeclaration || (AuditActionEnum)action.AuditActionId == AuditActionEnum.UpdateESDeclaration)
            {
                if (auditLogDto.OldValue != null && auditLogDto.OldValue.Length > 0)
                {
                    var oldValue = JsonConvert.DeserializeObject<AuditCreateUpdateESDeclarationDto>(auditLogDto.OldValue);
                    oldValue = _auditEncryptionManager.DecryptAuditDeclarationDto(oldValue ?? new());
                    auditLogDto.OldValue = JsonConvert.SerializeObject(oldValue);
                }
                if (auditLogDto.NewValue != null && auditLogDto.NewValue.Length > 0)
                {
                    var newValue = JsonConvert.DeserializeObject<AuditCreateUpdateESDeclarationDto>(auditLogDto.NewValue);
                    newValue = _auditEncryptionManager.DecryptAuditDeclarationDto(newValue ?? new());
                    auditLogDto.NewValue = JsonConvert.SerializeObject(newValue);
                }
            }
        }


        /// <summary>
        ///  Apply JSON conversion approach to flatten json object (old value & new value) to specified format for UI display.
        /// </summary>
        /// <param name="id">Primary key "Id" in table dbo.AuditLogs in ESS_Audit database.</param>
        /// <returns></returns>
        public async Task<AuditResultDto?> GetAuditLogDetail(Guid id)
        {
            throw new UserFriendlyException("GetAuditLogDetail API is disabled temporarily.");
            AuditResultDto? result = null;

            bool isAccessAll = false;
            bool isAccessOwn = false;

            if (this.CurrentUser != null)
            {
                //
                // current logon user permission check.
                //
                (isAccessAll, isAccessOwn) = await this.CheckHasPermission();

                var entity = await this._manager.GetAuditLogDetail(id);

                if (entity != null)
                {
                    if (isAccessAll || (isAccessOwn && entity.UserId.HasValue && entity.UserId.Value == this.CurrentUser.Id))
                    {
                        var auditLogDto = this.ObjectMapper.Map<AuditLog, AuditLogDto>(entity);

                        if (auditLogDto != null)
                        {
                            var countryList = await _lookupDataProvider.GetCountryListAsync();
                            var activityList = await _lookupDataProvider.GetRelevantActivitiyListAsync();
                            var cigaList = await _lookupDataProvider.GetCigaListAsync();
                            var action = AuditActionsStaticList.AuditActions.FirstOrDefault(x => (AuditActionEnum)x.AuditActionId == auditLogDto.Action);

                            DecrytData(action, auditLogDto);

                            result = auditLogDto.ToAuditResult(this.Logger, countryList, activityList, cigaList);

                            if (result != null && action != null)
                            {
                                result.FieldColumnWidth = action.FieldColumnWidth;
                                result.OldValueColumnWidth = action.OldValueColumnWidth;
                                result.NewValueColumnWidth = action.NewValueColumnWidth;

                                if (result.UserId.HasValue && result.UserId.Value != Guid.Empty)
                                {
                                    //var query = await this._userRepository.GetDbSetAsync();
                                    //var user = query.FirstOrDefault(x => x.Id == new Guid(result.UserId));

                                    try
                                    {
                                        var user = await this._identityUserAppService.GetAsync(result.UserId.Value);

                                        if (user != null)
                                        {
                                            result.FirstName = user.Name;
                                            result.LastName = user.Surname;
                                            result.Email = user.Email;
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        result.FirstName = "";
                                        result.LastName = "";
                                        result.Email = "";
                                        this.Logger.LogError(ex, "GetAuditLogDetail failed. UserId: {0}", result.UserId);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }

        public async Task<ICollection<AuditActionDto>> GetAuditActions()
        {
            ICollection<AuditActionDto> result = new List<AuditActionDto>();

            //
            // Check current process is CA or RA portal.
            //
            var currentTenantEdition = await _tenantService.GetCurrentEdition();
            bool isRA = currentTenantEdition == EssEditionConsts.RaPortal;
            bool isCA = currentTenantEdition == EssEditionConsts.CaPortal;

            result = AuditActionsStaticList.AuditActions.Where(x => (x.IsCA == isCA || x.IsRA == isRA) && x.IsDisplay).OrderBy(x => x.ActionDisplayName).ToList();

            return result;
        }

        /// <summary>
        ///  Based on current logon user's login portal (tenant edition), return "CA" or "RA".
        ///  "CA": CA portal.
        ///  "RA": RA portal.
        ///   Define the data source of Audit Log.
        /// </summary>
        /// <returns>string value. value = "CA" or "RA"</returns>
        private async Task<string> GetSource(Guid? tenantId)
        {
            if (tenantId.HasValue)
            {
                //
                // Check current process is CA or RA portal.
                //
                var currentTenantEdition = await this._tenantService.GetEditionById(tenantId);

                if (currentTenantEdition != null)
                {
                    if (currentTenantEdition == EssEditionConsts.RaPortal)
                        return "RA";
                    else if (currentTenantEdition == EssEditionConsts.CaPortal)
                        return "CA";
                    else return "Unknown";
                }
                else return "Unknown";
            }
            else return "Unknown";
        }

        public async Task<PagedResultDto<AuditLogDto>> GetAuditLogs(GetAuditLogsDto input)
        {
            PagedResultDto<AuditLogDto> result = new PagedResultDto<AuditLogDto>();

            if (this.CurrentUser != null)
            {
                var (isAccessAll, isAccessOwn) = await this.CheckHasPermission();

                input.Source = await this.GetSource(this.CurrentTenant.Id);

                result = await this._manager.GetAuditLogs(input, isAccessOwn, isAccessAll, this.CurrentUser.Id.Value);
            }

            return result;
        }

        /// <summary>
        ///  From 2019 to current year + "1000, Any"
        /// </summary>
        /// <returns></returns>
        public async Task<ICollection<FiscalYearDto>> GetFiscalYears()
        {
            return await Task.Run(() => this.FiscalYears());
        }

        private ICollection<FiscalYearDto> FiscalYears()
        {
            ICollection<FiscalYearDto> result = new List<FiscalYearDto>();
            var curYear = DateTime.Now.Year;

            result.Add(new FiscalYearDto(10000, "Any"));

            for (int y = 2019; y <= curYear; y++)
            {
                result.Add(new FiscalYearDto(y, y.ToString()));
            }

            return result.OrderByDescending(r => r.Year).ToList();
        }

        /// <summary>
        ///  Work for auditing feature list all users in the system.
        ///  Note: CA portal user should only check users logs in CA portal,
        ///  RA portal user should only check users logs in RA portal.
        ///  Filter users based on current user's assigned Auditing Permission.
        /// </summary>
        /// <returns></returns>
        public async Task<ICollection<AuditSearchUserDto>> GetAuditUsers()
        {
            bool isAccessAll = false;
            bool isAccessOwn = false;
            List<AuditSearchUserDto> result = new List<AuditSearchUserDto>();

            try
            {
                (isAccessAll, isAccessOwn) = await this.CheckHasPermission();

                if (isAccessAll)
                {
                    if (CurrentTenant != null && CurrentTenant.Id != Guid.Empty)
                    {
                        // Check cache list first based on tenant.
                        string cacheKey = string.Format("AuditSearchUsers_{0}", CurrentTenant.Id);
                        result = await this._auditSearchUsersCache.GetAsync(cacheKey, true);

                        if (result == null || result.Count == 0)
                        {
                            ////
                            ////Note: userRepository auto filter based on current logon user's tenantId
                            ////and soft deleted flag (IsDeleted column in table AbpUsers in Ess_Identity)
                            ////
                            //var query = await this._userRepository.GetDbSetAsync();

                            ////
                            //// Get users from database. Note: Auto filter based on current logon user's tenantId
                            ////
                            //result = query.Select(x => new AuditSearchUserDto(x.Id, x.UserName, x.Email, x.Name, x.Surname)).ToList();

                            var usersFilter = new Volo.Abp.Identity.GetIdentityUsersInput();
                            usersFilter.MaxResultCount = 1000;
                            usersFilter.SkipCount = 0;

                            var users = await this._identityUserAppService.GetListAsync(usersFilter);

                            if (users != null && users.TotalCount > 0)
                            {
                                result = users.Items.Select(u => new AuditSearchUserDto(u.Id, u.UserName, u.Email, u.Name, u.Surname)).ToList();
                            }

                            // Save to cache based on current tenant.
                            if (result != null && result.Count > 0)
                            {
                                await this._auditSearchUsersCache.SetAsync(cacheKey, result, new DistributedCacheEntryOptions
                                {
                                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30)
                                }, true);
                            }
                        }
                    }
                }
                else if (isAccessOwn && this.CurrentUser != null && this.CurrentUser.Id != null && this.CurrentUser.Id != Guid.Empty)
                {
                    // For access only own account, always call database.
                    //
                    //Note: userRepository auto filter based on current logon user's tenantId
                    //and soft deleted flag (IsDeleted column in table AbpUsers in Ess_Identity)
                    //
                    //var query = await this._userRepository.GetDbSetAsync();

                    var user = await this._identityUserAppService.GetAsync(this.CurrentUser.Id.Value);

                    if (user != null)
                    {
                        result = new List<AuditSearchUserDto> { new AuditSearchUserDto(user.Id, user.UserName, user.Email, user.Name, user.Surname) };
                    }

                    //result = query.Where(x=> x.Id == this.CurrentUser.Id)
                    //       .Select(x => new AuditSearchUserDto(x.Id, x.UserName, x.Email, x.Name, x.Surname)).ToList();
                }

                if (result != null && result.Count > 0)
                {
                    result = result.OrderBy(x => x.DisplayName).ToList();
                }
            }
            catch (Exception ex)
            {
                this.Logger.LogError(ex, "GetAuditUsers failed.");
            }

            return result;
        }

        private async Task<AuditSearchUserDto> GetAuditUser(Guid userId)
        {
            AuditSearchUserDto result = new AuditSearchUserDto();
            try
            {
                var user = await this._identityUserAppService.GetAsync(userId);

                if (user != null)
                {
                    result = new AuditSearchUserDto(user.Id, user.UserName, user.Email, user.Name, user.Surname);
                }
            }
            catch (Exception ex)
            {
                this.Logger.LogError(ex, "GetAuditUser failed.");
            }
            return result;
        }

        /// <summary>
        ///  Check current logon user's assigned Auditing Permission.
        ///  Work for filter users based on current user's assigned Auditing Permission.
        /// </summary>
        /// <returns>Return "Is Access All" + "Is Access Own"</returns>
        private async Task<(bool, bool)> CheckHasPermission()
        {
            bool isAccessAll = false;
            bool isAccessOwn = false;

            try
            {
                if (this.CurrentUser != null)
                {
                    if (this.CurrentUser.Roles != null && this.CurrentUser.Roles.Length > 0)
                    {
                        foreach (var role in this.CurrentUser.Roles)
                        {
                            var viewOwnPermission = await this._authorizationService.AuthorizeAsync(AuditServicePermissions.AuditPermissions.ViewOwn);
                            var viewAllPermission = await this._authorizationService.AuthorizeAsync(AuditServicePermissions.AuditPermissions.ViewAll);

                            if (viewOwnPermission != null && viewOwnPermission.Succeeded) isAccessOwn = true;
                            if (viewAllPermission != null && viewAllPermission.Succeeded) isAccessAll = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                this.Logger.LogError(ex, "CheckHasPermission failed.");
                isAccessAll = false;
                isAccessOwn = false;
            }

            return (isAccessAll, isAccessOwn);
        }
    }
}