﻿using Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests;
using Bdo.Ess.CtsIntegration.PackageGeneration;
using Bdo.Ess.Shared.Constants.CtsIntegration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Security.Claims;

namespace Bdo.Ess.CtsIntegration.CtsPackageRequests
{
    public class CtsEventHandler : ITransientDependency,
        IDistributedEventHandler<InformationExchangeXmlCreatedEto>
       , IDistributedEventHandler<RegenerateCtsDataPacketEto>
       , IDistributedEventHandler<PackageGenerationRequestCreatedEto>
    {
        private readonly ICtsPackageRequestAppService _exchangeRequestAppService;
        private readonly ICtsPackageGenerationAppService _packageGenerationAppService;
        private readonly ICurrentPrincipalAccessor _currentPrincipalAccessor;
        private readonly ILogger<CtsEventHandler> _logger;
        private readonly IDistributedEventBus _distributedEventBus;

        public CtsEventHandler(
            ICtsPackageRequestAppService exchangeRequestAppService,
            ICtsPackageGenerationAppService packageGenerationAppService,
            ICurrentPrincipalAccessor currentPrincipalAccessor,
            ILogger<CtsEventHandler> logger,
            IDistributedEventBus distributedEventBus
            )
        {
            _exchangeRequestAppService = exchangeRequestAppService;
            _packageGenerationAppService = packageGenerationAppService;
            _currentPrincipalAccessor = currentPrincipalAccessor;
            _logger = logger;
            _distributedEventBus = distributedEventBus;
        }
        public async Task HandleEventAsync(InformationExchangeXmlCreatedEto eventData)
        {
            try
            {
                var userId = eventData.UserId;
                // Create a ClaimsPrincipal with the desired userId
                var principal = new ClaimsPrincipal(
                    new ClaimsIdentity(new[]
                    {
                         new Claim(AbpClaimTypes.UserId, userId.ToString())
                    }, "Impersonation")
                );

                using (_currentPrincipalAccessor.Change(principal))
                {
                    var request = await _exchangeRequestAppService.InitPackageGenerationRequestAsync(eventData);
                    await _distributedEventBus.PublishAsync(new PackageGenerationRequestCreatedEto
                    {
                        TenantId = eventData.TenantId,
                        UserId = eventData.UserId,
                        PackageRequestId = request.Id
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogException(ex);
            }
        }

        public async Task HandleEventAsync(RegenerateCtsDataPacketEto eventData)
        {
            try
            {
                var userId = eventData.UserId;
                // Create a ClaimsPrincipal with the desired userId
                var principal = new ClaimsPrincipal(
                    new ClaimsIdentity(new[]
                    {
                         new Claim(AbpClaimTypes.UserId, userId.ToString())
                    }, "Impersonation")
                );

                using (_currentPrincipalAccessor.Change(principal))
                {
                    await _packageGenerationAppService.GeneratePackageAsync(eventData.PackageRequestId, eventData.TenantId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogException(ex);
            }
        }

        public async Task HandleEventAsync(PackageGenerationRequestCreatedEto eventData)
        {
            try
            {
                var userId = eventData.UserId;
                // Create a ClaimsPrincipal with the desired userId
                var principal = new ClaimsPrincipal(
                    new ClaimsIdentity(new[]
                    {
                         new Claim(AbpClaimTypes.UserId, userId.ToString())
                    }, "Impersonation")
                );

                using (_currentPrincipalAccessor.Change(principal))
                {
                    await _packageGenerationAppService.GeneratePackageAsync(eventData.PackageRequestId, eventData.TenantId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogException(ex);
            }
        }
    }
}
