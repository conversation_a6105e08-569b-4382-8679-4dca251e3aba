{"ast": null, "code": "import { BdoTableColumnType, BdoTableData } from '@app/shared/components/bdo-table/bdo-table.model';\nimport { UploadedFileStatus } from '../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declaration-imports';\nimport { AppComponentBase } from '../../../../app-component-base';\nimport { saveAs } from 'file-saver';\nimport { finalize, interval, switchMap, takeWhile } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/imports\";\nimport * as i3 from \"../../../../shared/services/upload-file.service\";\nimport * as i4 from \"../../../../shared/services/notification-signalr-service\";\nimport * as i5 from \"@abp/ng.core\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/divider\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"../../../../shared/components/bdo-table/bdo-table.component\";\nimport * as i13 from \"@angular/common\";\nconst _c0 = [\"uploadFile\"];\nconst _c1 = () => [10, 20, 50, 100, 200];\nfunction DeclarationImportComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 29)(1, \"a\", 30);\n    i0.ɵɵlistener(\"click\", function DeclarationImportComponent_span_8_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.routeToImportedDataPage());\n    });\n    i0.ɵɵtext(2, \" IMPORTED DATA\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeclarationImportComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\")(2, \"input\", 31, 1);\n    i0.ɵɵlistener(\"change\", function DeclarationImportComponent_div_35_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSelectFile($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\")(5, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function DeclarationImportComponent_div_35_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.uploadFileClick());\n    });\n    i0.ɵɵtext(6, \"Upload\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.selectedFile == null || ctx_r2.hasActiveFile || ctx_r2.uploading);\n  }\n}\nfunction DeclarationImportComponent_div_41_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r5, \" \");\n  }\n}\nfunction DeclarationImportComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"ul\");\n    i0.ɵɵtemplate(2, DeclarationImportComponent_div_41_li_2_Template, 2, 1, \"li\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.items);\n  }\n}\nexport class DeclarationImportComponent extends AppComponentBase {\n  ngOnInit() {\n    this.subscribeToEvents();\n    //this.setTableData();\n    this.onLazyLoadEvent(undefined);\n  }\n  ngOnDestroy() {\n    if (this.notificationSubscription) this.notificationSubscription.unsubscribe();\n    console.log(\"declaration import page closed. close notification connection\");\n    this.notificationSignalRService.closeConnection();\n  }\n  constructor(injector, router, declarationImportFileService, fileUploadService, notificationSignalRService, permissionService) {\n    super(injector);\n    this.router = router;\n    this.declarationImportFileService = declarationImportFileService;\n    this.fileUploadService = fileUploadService;\n    this.notificationSignalRService = notificationSignalRService;\n    this.permissionService = permissionService;\n    this.TableId = 'import-results';\n    this.currentPageIndex = 0;\n    this.stopPollingByFile = false;\n    this.stopPolling = false;\n    this.importResultColumns = [{\n      columnId: \"uploadedDateTime\" /* ESSImportTableColumns.DATE */,\n      type: BdoTableColumnType.Date,\n      minWidth: 100,\n      frozenLeft: true,\n      isSortable: true,\n      columnName: 'Date'\n    }, {\n      columnId: \"fileName\" /* ESSImportTableColumns.FILE_NAME */,\n      type: BdoTableColumnType.Link,\n      minWidth: 150,\n      isSortable: true,\n      columnName: 'Name'\n    }, {\n      columnId: \"submitterName\" /* ESSImportTableColumns.SUBMITTED_BY */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Submitted By'\n    }, {\n      columnId: \"statusName\" /* ESSImportTableColumns.STATUS */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: 'Status'\n    }, {\n      columnId: \"numberOfDeclarations\" /* ESSImportTableColumns.DECLERATIONS_NO */,\n      type: BdoTableColumnType.Number,\n      minWidth: 60,\n      isSortable: true,\n      columnName: '# Declarations'\n    }, {\n      columnId: \"numberOfWithErrorDeclarations\" /* ESSImportTableColumns.DECLERATIONS_WITHERROR */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: '# Declarations with errors'\n    }, {\n      columnId: \"numberOfToTriageDeclarations\" /* ESSImportTableColumns.DECLERATIONS_TO_TRIAGE */,\n      type: BdoTableColumnType.String,\n      minWidth: 60,\n      isSortable: true,\n      columnName: '# Declarations To Triage'\n    }];\n    this.PageSize = 10;\n    this.selectedFile = null;\n    this.templateData = [];\n    this.displayError = false;\n    this.UploadStatus = '';\n    this.input = {\n      maxResultCount: 10,\n      skipCount: 0,\n      sorting: \"UploadedDateTime desc\"\n    };\n    this.uploadedDate = undefined;\n    this.importResultRecords = [];\n    this.totalRecords = 0;\n    this.items = [];\n    this.fileErrorStatus = [UploadedFileStatus.FileErrors, UploadedFileStatus.Rejected];\n    this.hasActiveFile = false;\n    this.actionRequiredMessage = '';\n    this.errorFileId = \"\";\n    this.uploading = false;\n  }\n  canUploadFile() {\n    return this.permissionService.getGrantedPolicy(\"EsService.DeclarationImportFile.Import\" /* Permissions.DECLARATION_IMPORT_FILE */);\n  }\n  onLazyLoadEvent(event) {\n    if (event) {\n      if (this.PageSize === (event.pageSize ?? 10)) {\n        this.currentPageIndex = event.pageNumber ?? 0;\n      } else {\n        this.PageSize = event.pageSize ?? 10;\n        this.currentPageIndex = 0;\n      }\n      this.input.skipCount = (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\n      this.input.maxResultCount = this.PageSize ?? 10;\n      if (event.isAscending === false) {\n        this.input.sorting = `${event.sortField} desc`;\n      } else {\n        this.input.sorting = `${event.sortField} asc`;\n      }\n    } else {\n      this.input.uploadedDateTime = undefined;\n      this.input.sorting = 'UploadedDateTime desc';\n      this.currentPageIndex = 0;\n      this.PageSize = 10;\n      this.input.skipCount = 0;\n      this.input.maxResultCount = this.PageSize;\n    }\n    this.declarationImportFileService.getFileListByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.importResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n    this.declarationImportFileService.hasFileInTriage().subscribe(response => {\n      this.hasActiveFile = response;\n    });\n    this.declarationImportFileService.getFileInTriage().subscribe(response => {\n      if (response != null) {\n        this.activeFileDetail = response;\n        this.actionRequiredMessage = `${this.activeFileDetail.fileName} ready for triage`;\n      } else {\n        this.activeFileDetail = null;\n        this.actionRequiredMessage = '';\n      }\n    });\n  }\n  setTableData() {\n    const tableData = new BdoTableData();\n    tableData.resetToFirstPage = false;\n    tableData.tableId = this.TableId;\n    tableData.totalRecords = this.totalRecords;\n    tableData.data = this.importResultRecords.map(x => {\n      return {\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: \"uploadedDateTime\" /* ESSImportTableColumns.DATE */,\n          value: x.uploadedDateTime\n        }, {\n          columnId: \"fileName\" /* ESSImportTableColumns.FILE_NAME */,\n          value: x.fileName\n        }, {\n          columnId: \"submitterName\" /* ESSImportTableColumns.SUBMITTED_BY */,\n          value: x.submitterName\n        }, {\n          columnId: \"statusName\" /* ESSImportTableColumns.STATUS */,\n          value: x.statusName\n        }, {\n          columnId: \"numberOfDeclarations\" /* ESSImportTableColumns.DECLERATIONS_NO */,\n          value: x.status === UploadedFileStatus.FileErrors ? null : x.numberOfDeclarations\n        }, {\n          columnId: \"numberOfWithErrorDeclarations\" /* ESSImportTableColumns.DECLERATIONS_WITHERROR */,\n          value: x.status === UploadedFileStatus.FileErrors ? null : x.numberOfWithErrorDeclarations\n        }, {\n          columnId: \"numberOfToTriageDeclarations\" /* ESSImportTableColumns.DECLERATIONS_TO_TRIAGE */,\n          value: x.status === UploadedFileStatus.FileErrors ? null : x.numberOfToTriageDeclarations\n        }]\n      };\n    });\n    setTimeout(() => {\n      this.tableService.setGridData(tableData);\n    }, 10);\n  }\n  getFormattedDate(date) {\n    if (!date) return undefined;\n    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();\n  }\n  uploadFileClick() {\n    this.uploading = true;\n    console.log(\"Upload File button clicked. Start connect Notifcation Hub\");\n    this.notificationSignalRService.startConnection().then(() => {\n      //\n      // When signalR connected, start file import process.\n      //\n      console.log(\"Notification connection is connected. Start file import\");\n      const formData = new FormData();\n      formData.append(\"fileName\", this.selectedFile.name);\n      formData.append(\"file\", this.selectedFile);\n      formData.append(\"fileType\", this.selectedFile.type);\n      this.pollingUploadProgress();\n      this.fileUploadService.uploadDeclarationImportExcelByFile(formData).pipe(finalize(() => {\n        this.uploading = false;\n        this.stopPolling = true;\n      })).subscribe({\n        next: response => {\n          console.log(\"File Import is completed succssfully\", response.statusName);\n          this.onLazyLoadEvent(undefined);\n          this.UploadStatus = response.statusName;\n          this.selectedFile = null;\n          this.fileInput.nativeElement.value = '';\n          if (this.fileErrorStatus.includes(response.statusId) || response.statusId === UploadedFileStatus.ReadyForTriage) {\n            if (!response.validationResult.isValid) {\n              this.displayError = true;\n              this.items = response.validationResult.errors.map(x => x.errorMessage);\n            }\n          } else {\n            this.displayError = false;\n          }\n        },\n        error: err => {\n          if (err.status === 504) {\n            console.log(\"Gateway time out error, please refresh screen\");\n            this.pollingUploadProgressByFile();\n          }\n          this.displayError = true;\n          this.items = [];\n          this.items.push(\"System internal error, please try again\");\n        }\n      });\n    }).catch(err => {\n      //This caused by signal R error\n      console.log(\"Notification Connection failed\", err);\n      //If signal R auto-reconnect, line below should be commented out?\n      this.notificationSignalRService.closeConnection();\n    });\n  }\n  onSelectFile(event) {\n    this.selectedFile = event.target.files[0];\n  }\n  onLinkClick(event) {\n    this.items = [];\n    const data = event.rawData;\n    if (data.status === UploadedFileStatus.FileErrors) {\n      this.declarationImportFileService.getFileErrorsByFileId(data.id).subscribe(response => {\n        this.displayError = true;\n        this.items = response.errors.map(x => x.errorMessage);\n      });\n    } else {\n      this.router.navigate(['/es-import/importdetail'], {\n        queryParams: {\n          id: data.id\n        }\n      });\n    }\n  }\n  rowClicked(event) {\n    this.items = [];\n    this.errorFileId = \"\";\n    this.selectedRowStatusId = null;\n    const data = event.rowData.rawData;\n    if (data.status === UploadedFileStatus.FileErrors) {\n      this.declarationImportFileService.getFileErrorsByFileId(data.id).subscribe(response => {\n        this.errorFileId = data.id;\n        this.selectedRowStatusId = data.status;\n        this.displayError = true;\n        this.items = response.errors.map(x => x.errorMessage);\n      });\n    } else if (data.status === UploadedFileStatus.ReadyForTriage && data.numberOfWithErrorDeclarations > 0) {\n      this.declarationImportFileService.getDataErrorsByFileId(data.id).subscribe(response => {\n        this.errorFileId = data.id;\n        this.selectedRowStatusId = data.status;\n        this.displayError = true;\n        if (!response.isValid) {\n          this.items = response.errors.map(x => x.errorMessage);\n        }\n      });\n    } else {\n      this.errorFileId = \"\";\n      this.displayError = false;\n    }\n  }\n  applyClick() {\n    this.input.uploadedDateTime = this.getFormattedDate(this.uploadedDate);\n    this.declarationImportFileService.getFileListByInput(this.input).subscribe(response => {\n      this.totalRecords = response.totalCount;\n      this.importResultRecords = response.items;\n      setTimeout(() => {\n        this.setTableData();\n      }, 200);\n    });\n  }\n  resetSearchClick() {\n    this.uploadedDate = undefined;\n    this.onLazyLoadEvent(undefined);\n  }\n  routeToImportedDataPage() {\n    if (this.activeFileDetail) this.router.navigate(['/es-import/importdetail'], {\n      queryParams: {\n        id: this.activeFileDetail.id\n      }\n    });\n  }\n  downloadFileErrors() {\n    if (this.errorFileId !== '') {\n      if (this.selectedRowStatusId === UploadedFileStatus.FileErrors) {\n        this.declarationImportFileService.downloadFileErrors(this.errorFileId).subscribe(result => {\n          const decodedData = atob(result.toString());\n          const blob = new Blob([decodedData], {\n            type: 'text/csv'\n          });\n          saveAs(blob, `FileErrors_${this.errorFileId}.csv`);\n        });\n      } else if (this.selectedRowStatusId === UploadedFileStatus.ReadyForTriage) {\n        this.declarationImportFileService.downloadDataErrors(this.errorFileId).subscribe(result => {\n          const decodedData = atob(result.toString());\n          const blob = new Blob([decodedData], {\n            type: 'text/csv'\n          });\n          saveAs(blob, `DataErrors_${this.errorFileId}.csv`);\n        });\n      }\n    }\n  }\n  subscribeToEvents() {\n    this.notificationSubscription = this.notificationSignalRService.messageReceived.subscribe(message => {\n      if (message) {\n        this.UploadStatus = message;\n      }\n    });\n  }\n  pollingUploadProgress() {\n    const apiCallInterval$ = interval(2000);\n    apiCallInterval$.pipe(switchMap(() => this.declarationImportFileService.getUploadProgress()), takeWhile(data => !this.stopPolling && data.statusName !== 'Failed to Import' && data.statusName !== 'Ready For Triage' && data.statusName !== 'Has File Errors')).subscribe({\n      next: response => {\n        //console.log(response);\n        this.UploadStatus = response.message;\n      },\n      error: err => {\n        this.stopPolling = true;\n        console.log(\"Error occured when polling file import progress (event bus message)\", err);\n      }\n    });\n  }\n  pollingUploadProgressByFile() {\n    const apiCallInterval$ = interval(2000);\n    // Use switchMap to trigger an API call at each interval\n    apiCallInterval$.pipe(switchMap(() => this.declarationImportFileService.getLastImportedFile()), takeWhile(data => !this.stopPollingByFile && data.statusName !== 'Failed to Import' && data.statusName !== 'Ready For Triage' && data.statusName !== 'Has File Errors')).subscribe({\n      next: response => {\n        this.displayUploadStatusByFile(response);\n      },\n      error: err => {\n        this.stopPollingByFile = true;\n        console.log(\"Error occured when polling file import progress\", err);\n      }\n    });\n  }\n  displayUploadStatusByFile(data) {\n    this.onLazyLoadEvent(undefined);\n    this.UploadStatus = data.statusName;\n    this.selectedFile = null;\n    this.fileInput.nativeElement.value = '';\n    this.items = [];\n    if (data.status === UploadedFileStatus.FileErrors) {\n      this.declarationImportFileService.getFileErrorsByFileId(data.id).subscribe(response => {\n        this.displayError = true;\n        this.items = response.errors.map(x => x.errorMessage);\n      });\n    } else {\n      this.displayError = false;\n    }\n  }\n  static {\n    this.ɵfac = function DeclarationImportComponent_Factory(t) {\n      return new (t || DeclarationImportComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.DeclarationImportFileService), i0.ɵɵdirectiveInject(i3.FileUploadService), i0.ɵɵdirectiveInject(i4.NotificationSignalRService), i0.ɵɵdirectiveInject(i5.PermissionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DeclarationImportComponent,\n      selectors: [[\"app-declaration-import\"]],\n      viewQuery: function DeclarationImportComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 42,\n      vars: 20,\n      consts: [[\"uploadDatepicker\", \"\"], [\"uploadFile\", \"\"], [1, \"display-flex-main\"], [1, \"left-column\"], [1, \"display-flex\"], [1, \"top-action-row-import\"], [1, \"upload-import-container\"], [1, \"upload-file\"], [\"id\", \"importedDataUploadFile\"], [\"class\", \"import-file\", 4, \"ngIf\"], [1, \"error-message\"], [1, \"fill-extra-space\"], [1, \"top-action-row\"], [1, \"custom-datepicker\"], [\"matInput\", \"\", \"title\", \"Choose Date\", 3, \"ngModelChange\", \"matDatepicker\", \"ngModel\"], [\"matIconSuffix\", \"\", 3, \"for\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\", \"disabled\"], [1, \"table-container\"], [\"scrollHeight\", \"68vh\", \"defaultSortColumnId\", \"uploadedDateTime\", 3, \"onLinkClick\", \"onLazyLoad\", \"onRowClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\", \"pageSizeOptions\"], [1, \"right-column\"], [1, \"left-column-titles\"], [1, \"divider-margin\"], [1, \"entity-detail-container\"], [4, \"ngIf\"], [1, \"detail-note_header\"], [1, \"processing-message\"], [1, \"detail-note\"], [\"class\", \"errormessage\", 4, \"ngIf\"], [1, \"import-file\"], [\"id\", \"importedDataUploadFile\", 2, \"cursor\", \"pointer\", \"text-decoration\", \"underline\", 3, \"click\"], [\"title\", \"Select File\", \"accept\", \"*\", \"type\", \"file\", \"id\", \"UploadProfileImportFile\", 1, \"form-control\", 3, \"change\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"button-right\", 3, \"click\", \"disabled\"], [1, \"errormessage\"], [4, \"ngFor\", \"ngForOf\"]],\n      template: function DeclarationImportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"div\", 6)(5, \"span\", 7)(6, \"a\", 8);\n          i0.ɵɵtext(7, \" UPLOAD FILE\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, DeclarationImportComponent_span_8_Template, 3, 0, \"span\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 10)(10, \"span\");\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(12, \"span\", 11);\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"mat-form-field\", 13)(15, \"mat-label\");\n          i0.ɵɵtext(16, \"Search Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function DeclarationImportComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.uploadedDate, $event) || (ctx.uploadedDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"mat-datepicker-toggle\", 15)(19, \"mat-datepicker\", null, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function DeclarationImportComponent_Template_button_click_21_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.applyClick());\n          });\n          i0.ɵɵtext(22, \"Apply\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function DeclarationImportComponent_Template_button_click_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearchClick());\n          });\n          i0.ɵɵtext(24, \"Reset Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DeclarationImportComponent_Template_button_click_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.downloadFileErrors());\n          });\n          i0.ɵɵtext(26, \"Download Errors\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 18)(28, \"bdo-table\", 19);\n          i0.ɵɵlistener(\"onLinkClick\", function DeclarationImportComponent_Template_bdo_table_onLinkClick_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onLinkClick($event));\n          })(\"onLazyLoad\", function DeclarationImportComponent_Template_bdo_table_onLazyLoad_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onLazyLoadEvent($event));\n          })(\"onRowClick\", function DeclarationImportComponent_Template_bdo_table_onRowClick_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.rowClicked($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 20)(30, \"div\")(31, \"span\", 21);\n          i0.ɵɵtext(32, \"SELECT FILE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"mat-divider\", 22);\n          i0.ɵɵelementStart(34, \"div\", 23);\n          i0.ɵɵtemplate(35, DeclarationImportComponent_div_35_Template, 7, 1, \"div\", 24);\n          i0.ɵɵelementStart(36, \"div\", 25);\n          i0.ɵɵtext(37, \" If the number of records in your file upload exceeds 100, you may need to refresh the page after 5 minutes to see the uploaded records displayed. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 26);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 27);\n          i0.ɵɵtemplate(41, DeclarationImportComponent_div_41_Template, 3, 1, \"div\", 28);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const uploadDatepicker_r6 = i0.ɵɵreference(20);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeFileDetail);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.actionRequiredMessage, \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matDatepicker\", uploadDatepicker_r6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.uploadedDate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"for\", uploadDatepicker_r6);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", ctx.errorFileId === \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.importResultColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndex)(\"pageSize\", ctx.PageSize)(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true)(\"pageSizeOptions\", i0.ɵɵpureFunction0(19, _c1));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.canUploadFile());\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" Upload progress: \", ctx.UploadStatus, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.displayError);\n        }\n      },\n      dependencies: [i6.DefaultValueAccessor, i6.NgControlStatus, i7.MatInput, i8.MatFormField, i8.MatLabel, i8.MatSuffix, i9.MatDivider, i10.MatButton, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, i12.BdoTableComponent, i13.NgForOf, i13.NgIf, i6.NgModel],\n      styles: [\".import-title[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n  color: #00779b;\\n  display: block;\\n  margin: 0 auto;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  z-index: 0;\\n  position: relative;\\n  margin-right: 0.5em;\\n  min-height: 100% !important;\\n}\\n\\n.left-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 0.5em;\\n  min-width: 70%;\\n}\\n\\n.right-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: #F3FAFD;\\n  min-width: 30%;\\n  height: 55em;\\n}\\n\\n.display-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.display-flex-main[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100% !important;\\n}\\n\\n.top-action-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.top-action-row-import[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  margin-top: 1.5em;\\n}\\n\\n.upload-import-container[_ngcontent-%COMP%] {\\n  display: flex; \\n\\n}\\n\\n.span-new-line[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.custom-datepicker[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  height: 70px;\\n}\\n\\n.import-detail-container[_ngcontent-%COMP%] {\\n  max-height: 35em;\\n  overflow-y: scroll;\\n  margin-bottom: 0.5em;\\n}\\n\\n.entity-detail-text[_ngcontent-%COMP%] {\\n  font-weight: bolder;\\n}\\n\\n.divider-margin[_ngcontent-%COMP%] {\\n  margin: 1em;\\n}\\n\\n.left-column-titles[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n  color: #00779b;\\n  display: block;\\n  margin: 1em;\\n}\\n\\n.detail-note_header[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-style: italic;\\n  margin-top: 50px;\\n}\\n\\n.detail-note[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-style: italic;\\n  margin-top: 50px;\\n}\\n\\n.errormessage[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: red;\\n}\\n\\n.upload-file[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n  color: #00779b;\\n}\\n\\n.import-file[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n  margin-left: 1rem;\\n  color: #00779b;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: red;\\n  margin-top: 10px;\\n}\\n\\n.processing-message[_ngcontent-%COMP%] {\\n  color: #00779b;\\n  font-size: 1.2em;\\n  font-weight: bold;\\n  margin-top: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGVjbGFyYXRpb24taW1wb3J0L2NvbnRhaW5lcnMvZGVjbGFyYXRpb24taW1wb3J0L2RlY2xhcmF0aW9uLWltcG9ydC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGNBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBR0E7RUFDRSxVQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLDJCQUFBO0FBQUY7O0FBSUE7RUFDRSxPQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0FBREY7O0FBS0E7RUFDRSxPQUFBO0VBQ0EseUJBQUE7RUFDQSxjQUFBO0VBQ0EsWUFBQTtBQUZGOztBQUtBO0VBQ0UsYUFBQTtBQUZGOztBQU1BO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0FBSEY7O0FBS0E7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtBQUZGOztBQU1BO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSxpQkFBQTtBQUhGOztBQU9BO0VBQ0UsYUFBQSxFQUFBLCtEQUFBO0FBSkY7O0FBTUE7RUFDRSxjQUFBO0FBSEY7O0FBTUE7RUFFRSxnQkFBQTtFQUNBLFlBQUE7QUFKRjs7QUFRQTtFQUNFLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxvQkFBQTtBQUxGOztBQVFBO0VBQ0UsbUJBQUE7QUFMRjs7QUFRQTtFQUNFLFdBQUE7QUFMRjs7QUFRQTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGNBQUE7RUFDQSxXQUFBO0FBTEY7O0FBUUE7RUFDRSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFMRjs7QUFVQTtFQUNFLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQVBGOztBQVNBO0VBQ0UsZUFBQTtFQUNBLFVBQUE7QUFORjs7QUFTQTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtBQU5GOztBQVNDO0VBRUMsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7QUFQRjs7QUFXQTtFQUNFLFVBQUE7RUFDQSxnQkFBQTtBQVJGOztBQVdBO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQVJGO0FBQ0Esd2tJQUF3a0kiLCJzb3VyY2VzQ29udGVudCI6WyIuaW1wb3J0LXRpdGxlIHtcclxuICBmb250LXNpemU6IDEuNWVtO1xyXG4gIGNvbG9yOiAjMDA3NzliO1xyXG4gIGRpc3BsYXk6IGJsb2NrO1xyXG4gIG1hcmdpbjogMCBhdXRvO1xyXG59XHJcblxyXG5cclxuLnRhYmxlLWNvbnRhaW5lciB7XHJcbiAgei1pbmRleDogMDtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgbWFyZ2luLXJpZ2h0OiAwLjVlbTtcclxuICBtaW4taGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcblxyXG4ubGVmdC1jb2x1bW4ge1xyXG4gIGZsZXg6IDE7XHJcbiAgbWFyZ2luLXJpZ2h0OiAwLjVlbTtcclxuICBtaW4td2lkdGg6IDcwJTtcclxuICBcclxufVxyXG5cclxuLnJpZ2h0LWNvbHVtbiB7XHJcbiAgZmxleDogMTtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjNGQUZEO1xyXG4gIG1pbi13aWR0aDogMzAlO1xyXG4gIGhlaWdodDogNTVlbTtcclxufVxyXG5cclxuLmRpc3BsYXktZmxleCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBcclxufVxyXG5cclxuLmRpc3BsYXktZmxleC1tYWluIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGhlaWdodDogMTAwJSAhaW1wb3J0YW50O1xyXG59XHJcbi50b3AtYWN0aW9uLXJvdyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxufVxyXG5cclxuXHJcbi50b3AtYWN0aW9uLXJvdy1pbXBvcnQge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246Y29sdW1uO1xyXG4gIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xyXG4gIG1hcmdpbi10b3A6MS41ZW07XHJcbn1cclxuXHJcblxyXG4udXBsb2FkLWltcG9ydC1jb250YWluZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7IC8qIENyZWF0ZSBhIGZsZXggY29udGFpbmVyIGZvciB0aGUgdXBsb2FkIGFuZCBpbXBvcnQgZWxlbWVudHMgKi9cclxufVxyXG4uc3Bhbi1uZXctbGluZSB7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF0ZXBpY2tlclxyXG57XHJcbiAgbWFyZ2luLXRvcDoyMHB4O1xyXG4gIGhlaWdodDo3MHB4O1xyXG59XHJcblxyXG5cclxuLmltcG9ydC1kZXRhaWwtY29udGFpbmVyIHtcclxuICBtYXgtaGVpZ2h0OiAzNWVtO1xyXG4gIG92ZXJmbG93LXk6IHNjcm9sbDtcclxuICBtYXJnaW4tYm90dG9tOiAwLjVlbTtcclxufVxyXG5cclxuLmVudGl0eS1kZXRhaWwtdGV4dCB7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGRlcjtcclxufVxyXG5cclxuLmRpdmlkZXItbWFyZ2luIHtcclxuICBtYXJnaW46IDFlbTtcclxufVxyXG5cclxuLmxlZnQtY29sdW1uLXRpdGxlcyB7XHJcbiAgZm9udC1zaXplOiAxLjVlbTtcclxuICBjb2xvcjogIzAwNzc5YjtcclxuICBkaXNwbGF5OiBibG9jaztcclxuICBtYXJnaW46IDFlbTtcclxufVxyXG5cclxuLmRldGFpbC1ub3RlX2hlYWRlciB7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgZm9udC1zdHlsZTogaXRhbGljO1xyXG4gIG1hcmdpbi10b3A6IDUwcHg7XHJcbiBcclxufVxyXG5cclxuXHJcbi5kZXRhaWwtbm90ZSB7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgZm9udC1zdHlsZTogaXRhbGljO1xyXG4gIG1hcmdpbi10b3A6IDUwcHg7XHJcbn1cclxuLmVycm9ybWVzc2FnZSB7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIGNvbG9yOiByZWQ7XHJcbn1cclxuXHJcbi51cGxvYWQtZmlsZSB7XHJcbiAgZm9udC1zaXplOiAxLjVlbTtcclxuICBjb2xvcjogIzAwNzc5YjtcclxufVxyXG5cclxuIC5pbXBvcnQtZmlsZSB7XHJcblxyXG4gIGZvbnQtc2l6ZTogMS41ZW07XHJcbiAgbWFyZ2luLWxlZnQ6IDFyZW07XHJcbiAgY29sb3I6ICMwMDc3OWI7XHJcbn1cclxuXHJcblxyXG4uZXJyb3ItbWVzc2FnZSB7XHJcbiAgY29sb3I6IHJlZDtcclxuICBtYXJnaW4tdG9wOiAxMHB4O1xyXG59XHJcblxyXG4ucHJvY2Vzc2luZy1tZXNzYWdlIHtcclxuICBjb2xvcjogIzAwNzc5YjtcclxuICBmb250LXNpemU6IDEuMmVtO1xyXG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gIG1hcmdpbi10b3A6IDEwcHg7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BdoTableColumnType", "BdoTableData", "UploadedFileStatus", "AppComponentBase", "saveAs", "finalize", "interval", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "DeclarationImportComponent_span_8_Template_a_click_1_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "routeToImportedDataPage", "ɵɵtext", "ɵɵelementEnd", "DeclarationImportComponent_div_35_Template_input_change_2_listener", "$event", "_r4", "onSelectFile", "DeclarationImportComponent_div_35_Template_button_click_5_listener", "uploadFileClick", "ɵɵadvance", "ɵɵproperty", "selectedFile", "hasActiveFile", "uploading", "ɵɵtextInterpolate1", "item_r5", "ɵɵtemplate", "DeclarationImportComponent_div_41_li_2_Template", "items", "DeclarationImportComponent", "ngOnInit", "subscribeToEvents", "onLazyLoadEvent", "undefined", "ngOnDestroy", "notificationSubscription", "unsubscribe", "console", "log", "notificationSignalRService", "closeConnection", "constructor", "injector", "router", "declarationImportFileService", "fileUploadService", "permissionService", "TableId", "currentPageIndex", "stopPollingByFile", "stopPolling", "importResultColumns", "columnId", "type", "Date", "min<PERSON><PERSON><PERSON>", "frozenLeft", "isSortable", "columnName", "Link", "String", "Number", "PageSize", "templateData", "displayError", "UploadStatus", "input", "maxResultCount", "skip<PERSON><PERSON>nt", "sorting", "uploadedDate", "importResultRecords", "totalRecords", "fileErrorStatus", "FileErrors", "Rejected", "actionRequiredMessage", "errorFileId", "canUploadFile", "getGrantedPolicy", "event", "pageSize", "pageNumber", "isAscending", "sortField", "uploadedDateTime", "getFileListByInput", "subscribe", "response", "totalCount", "setTimeout", "setTableData", "hasFileInTriage", "getFileInTriage", "activeFileDetail", "fileName", "tableData", "resetToFirstPage", "tableId", "data", "map", "x", "id", "rawData", "cells", "value", "submitterName", "statusName", "status", "numberOfDeclarations", "numberOfWithErrorDeclarations", "numberOfToTriageDeclarations", "tableService", "setGridData", "getFormattedDate", "date", "getFullYear", "getMonth", "getDate", "startConnection", "then", "formData", "FormData", "append", "name", "pollingUploadProgress", "uploadDeclarationImportExcelByFile", "pipe", "next", "fileInput", "nativeElement", "includes", "statusId", "ReadyForTriage", "validationResult", "<PERSON><PERSON><PERSON><PERSON>", "errors", "errorMessage", "error", "err", "pollingUploadProgressByFile", "push", "catch", "target", "files", "onLinkClick", "getFileErrorsByFileId", "navigate", "queryParams", "rowClicked", "selectedRowStatusId", "rowData", "getDataErrorsByFileId", "applyClick", "resetSearchClick", "downloadFileErrors", "result", "decodedData", "atob", "toString", "blob", "Blob", "downloadDataErrors", "messageReceived", "message", "apiCallInterval$", "getUploadProgress", "getLastImportedFile", "displayUploadStatusByFile", "ɵɵdirectiveInject", "Injector", "i1", "Router", "i2", "DeclarationImportFileService", "i3", "FileUploadService", "i4", "NotificationSignalRService", "i5", "PermissionService", "selectors", "viewQuery", "DeclarationImportComponent_Query", "rf", "ctx", "DeclarationImportComponent_span_8_Template", "ɵɵelement", "ɵɵtwoWayListener", "DeclarationImportComponent_Template_input_ngModelChange_17_listener", "_r1", "ɵɵtwoWayBindingSet", "DeclarationImportComponent_Template_button_click_21_listener", "DeclarationImportComponent_Template_button_click_23_listener", "DeclarationImportComponent_Template_button_click_25_listener", "DeclarationImportComponent_Template_bdo_table_onLinkClick_28_listener", "DeclarationImportComponent_Template_bdo_table_onLazyLoad_28_listener", "DeclarationImportComponent_Template_bdo_table_onRowClick_28_listener", "DeclarationImportComponent_div_35_Template", "DeclarationImportComponent_div_41_Template", "uploadDatepicker_r6", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c1"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\declaration-import\\containers\\declaration-import\\declaration-import.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\declaration-import\\containers\\declaration-import\\declaration-import.component.html"], "sourcesContent": ["import { Component, Injector, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { BdoTableCellLinkClickEvent, BdoTableColumnDefinition, BdoTableColumnType, BdoTableData, BdoTableRowActionClickEvent } from '@app/shared/components/bdo-table/bdo-table.model';\r\nimport { UploadedFileStatus } from '../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declaration-imports';\r\nimport { DeclarationImportFileService } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/imports';\r\nimport { AppComponentBase } from '../../../../app-component-base';\r\nimport { ESSImportTableColumns, Permissions } from '../../../../shared/constants';\r\nimport { ExcelUploadResultDto, FileUploadService } from '../../../../shared/services/upload-file.service';\r\nimport { DeclarationImportFileDto, GetDeclarationImportFileDto } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declaration-imports/dtos';\r\nimport { saveAs } from 'file-saver'\r\nimport { Subscription, finalize, interval, switchMap, takeWhile, tap } from 'rxjs';\r\nimport { NotificationSignalRService } from '../../../../shared/services/notification-signalr-service'\r\nimport { PermissionService } from '@abp/ng.core';\r\n\r\n\r\n@Component({\r\n  selector: 'app-declaration-import',\r\n  templateUrl: './declaration-import.component.html',\r\n  styleUrls: ['./declaration-import.component.scss']\r\n})\r\nexport class DeclarationImportComponent extends AppComponentBase implements OnInit, OnDestroy {\r\n  @ViewChild('uploadFile') fileInput: any;\r\n  TableId = 'import-results';\r\n  currentPageIndex = 0;\r\n  private stopPollingByFile: boolean = false;\r\n  private stopPolling: boolean = false;\r\n  private notificationSubscription: Subscription;\r\n\r\n  importResultColumns: BdoTableColumnDefinition[] = [\r\n    {\r\n      columnId: ESSImportTableColumns.DATE,\r\n      type: BdoTableColumnType.Date,\r\n      minWidth: 100,\r\n      frozenLeft: true,\r\n      isSortable: true,\r\n      columnName: 'Date',\r\n    },\r\n    {\r\n      columnId: ESSImportTableColumns.FILE_NAME,\r\n      type: BdoTableColumnType.Link,\r\n      minWidth: 150,\r\n      isSortable: true,\r\n      columnName: 'Name',\r\n    },\r\n    {\r\n      columnId: ESSImportTableColumns.SUBMITTED_BY,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Submitted By',\r\n    },\r\n    {\r\n      columnId: ESSImportTableColumns.STATUS,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: 'Status',\r\n    },\r\n    {\r\n      columnId: ESSImportTableColumns.DECLERATIONS_NO,\r\n      type: BdoTableColumnType.Number,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: '# Declarations',\r\n    },\r\n    {\r\n      columnId: ESSImportTableColumns.DECLERATIONS_WITHERROR,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: '# Declarations with errors',\r\n    },\r\n    {\r\n      columnId: ESSImportTableColumns.DECLERATIONS_TO_TRIAGE,\r\n      type: BdoTableColumnType.String,\r\n      minWidth: 60,\r\n      isSortable: true,\r\n      columnName: '# Declarations To Triage',\r\n    },\r\n  ];\r\n  PageSize = 10;\r\n  selectedFile: File = null;\r\n  templateData = [];\r\n  displayError: boolean = false;\r\n  UploadStatus: any='';\r\n  input: GetDeclarationImportFileDto = { maxResultCount: 10, skipCount: 0, sorting: \"UploadedDateTime desc\" };\r\n  uploadedDate?: Date = undefined;\r\n  importResultRecords = [];\r\n  totalRecords=0;\r\n  items = [];\r\n  fileErrorStatus: any[] = [UploadedFileStatus.FileErrors, UploadedFileStatus.Rejected];\r\n  hasActiveFile: boolean = false;\r\n  actionRequiredMessage: string='';\r\n  activeFileDetail: DeclarationImportFileDto;\r\n  errorFileId: string = \"\";\r\n  selectedRowStatusId: UploadedFileStatus;\r\n  uploading = false;\r\n\r\n  ngOnInit(): void {\r\n    this.subscribeToEvents();\r\n    //this.setTableData();\r\n    this.onLazyLoadEvent(undefined);  \r\n  }  \r\n\r\n  ngOnDestroy(): void {\r\n    if (this.notificationSubscription) this.notificationSubscription.unsubscribe();\r\n    console.log(\"declaration import page closed. close notification connection\");\r\n    this.notificationSignalRService.closeConnection();\r\n  }\r\n\r\n  constructor(\r\n    injector: Injector,\r\n    private router: Router,\r\n    private declarationImportFileService: DeclarationImportFileService,\r\n    private fileUploadService: FileUploadService,\r\n    private notificationSignalRService: NotificationSignalRService,\r\n    private permissionService: PermissionService\r\n  ) {\r\n    super(injector);\r\n  \r\n  }\r\n\r\n  canUploadFile()\r\n  {\r\n    return this.permissionService.getGrantedPolicy(Permissions.DECLARATION_IMPORT_FILE);\r\n  }\r\n\r\n  onLazyLoadEvent(event): void {\r\n    if (event) {\r\n      if (this.PageSize === (event.pageSize ?? 10)) {\r\n        this.currentPageIndex = event.pageNumber ?? 0;\r\n      } else {        \r\n        this.PageSize = event.pageSize ?? 10;\r\n        this.currentPageIndex = 0;\r\n      }\r\n      this.input.skipCount = (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\r\n      this.input.maxResultCount = this.PageSize ?? 10;\r\n      if (event.isAscending === false) {\r\n        this.input.sorting = `${event.sortField} desc`;\r\n      }\r\n       else {\r\n        this.input.sorting = `${event.sortField} asc`;\r\n      }\r\n    }\r\n     else {\r\n      this.input.uploadedDateTime = undefined;\r\n      this.input.sorting = 'UploadedDateTime desc';\r\n      this.currentPageIndex = 0;\r\n      this.PageSize = 10;\r\n      this.input.skipCount = 0;\r\n      this.input.maxResultCount = this.PageSize;\r\n    }\r\n    \r\n    this.declarationImportFileService.getFileListByInput(this.input).subscribe((response) =>\r\n    {\r\n      this.totalRecords = response.totalCount;\r\n      this.importResultRecords = response.items;\r\n    \r\n      setTimeout(() => { this.setTableData(); }, 200);\r\n    });\r\n\r\n\r\n    this.declarationImportFileService.hasFileInTriage().subscribe((response) => {\r\n      this.hasActiveFile = response;\r\n    });\r\n\r\n    this.declarationImportFileService.getFileInTriage().subscribe((response) =>\r\n    {\r\n      if (response != null) {\r\n        this.activeFileDetail = response;\r\n        this.actionRequiredMessage = `${this.activeFileDetail.fileName} ready for triage`;\r\n      }\r\n      else {\r\n        this.activeFileDetail = null;\r\n        this.actionRequiredMessage = '';\r\n      }\r\n    });\r\n  }\r\n\r\n  setTableData(): void {\r\n    const tableData = new BdoTableData();\r\n    tableData.resetToFirstPage = false;\r\n    tableData.tableId = this.TableId;\r\n    tableData.totalRecords = this.totalRecords;\r\n    tableData.data = this.importResultRecords.map(x => {\r\n      return {\r\n        id: x.id,\r\n        rawData: x,\r\n        cells: [\r\n          { columnId: ESSImportTableColumns.DATE, value: x.uploadedDateTime },\r\n          { columnId: ESSImportTableColumns.FILE_NAME, value: x.fileName },\r\n          { columnId: ESSImportTableColumns.SUBMITTED_BY, value: x.submitterName },\r\n          { columnId: ESSImportTableColumns.STATUS, value: x.statusName },\r\n          { columnId: ESSImportTableColumns.DECLERATIONS_NO, value: x.status === UploadedFileStatus.FileErrors ? null : x.numberOfDeclarations },\r\n          { columnId: ESSImportTableColumns.DECLERATIONS_WITHERROR, value: x.status === UploadedFileStatus.FileErrors ? null : x.numberOfWithErrorDeclarations },\r\n          { columnId: ESSImportTableColumns.DECLERATIONS_TO_TRIAGE, value: x.status === UploadedFileStatus.FileErrors ? null : x.numberOfToTriageDeclarations },\r\n        ],\r\n\r\n      };\r\n    });\r\n    setTimeout(() => {\r\n      this.tableService.setGridData(tableData);\r\n    }, 10);\r\n\r\n  }\r\n\r\n\r\n  getFormattedDate(date?: Date): string {\r\n    if (!date)\r\n      return undefined;\r\n\r\n    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();\r\n  }\r\n\r\n\r\n  uploadFileClick(): void {\r\n    this.uploading = true;\r\n\r\n    console.log(\"Upload File button clicked. Start connect Notifcation Hub\");\r\n\r\n    this.notificationSignalRService.startConnection().then(()=> {\r\n      //\r\n      // When signalR connected, start file import process.\r\n      //\r\n      console.log(\"Notification connection is connected. Start file import\");\r\n\r\n      const formData: FormData = new FormData();\r\n      formData.append(\"fileName\", this.selectedFile.name);\r\n      formData.append(\"file\", this.selectedFile);\r\n      formData.append(\"fileType\", this.selectedFile.type);\r\n\r\n      this.pollingUploadProgress();\r\n\r\n      this.fileUploadService.uploadDeclarationImportExcelByFile(formData).pipe(\r\n          finalize(() =>{\r\n            this.uploading = false;\r\n            this.stopPolling = true;\r\n          })\r\n        )\r\n        .subscribe(\r\n        {\r\n          next: (response) => {\r\n            console.log(\"File Import is completed succssfully\", response.statusName);\r\n\r\n            this.onLazyLoadEvent(undefined);\r\n            this.UploadStatus = response.statusName;\r\n            this.selectedFile = null;\r\n            this.fileInput.nativeElement.value = '';\r\n            if (this.fileErrorStatus.includes(response.statusId) || response.statusId === UploadedFileStatus.ReadyForTriage) {\r\n              if (!response.validationResult.isValid) {\r\n                this.displayError = true;\r\n                this.items = response.validationResult.errors.map(x => x.errorMessage);\r\n              }\r\n            }\r\n            else{\r\n              this.displayError = false;\r\n            }\r\n          },\r\n          error: (err) => {\r\n            if (err.status === 504) {\r\n              console.log (\"Gateway time out error, please refresh screen\");\r\n              this.pollingUploadProgressByFile();\r\n            }\r\n            this.displayError = true;\r\n            this.items = [];\r\n            this.items.push(\"System internal error, please try again\");\r\n          }\r\n        }\r\n      );\r\n    }).catch(err=> {\r\n      //This caused by signal R error\r\n      console.log(\"Notification Connection failed\", err);\r\n      //If signal R auto-reconnect, line below should be commented out?\r\n      this.notificationSignalRService.closeConnection();   \r\n    });\r\n  }\r\n\r\n  onSelectFile(event: any) {\r\n    this.selectedFile = event.target.files[0];\r\n  }\r\n\r\n  onLinkClick(event: BdoTableCellLinkClickEvent)\r\n  {\r\n    this.items = [];\r\n    const data = event.rawData as DeclarationImportFileDto;\r\n    if (data.status === UploadedFileStatus.FileErrors) {\r\n      this.declarationImportFileService.getFileErrorsByFileId(data.id).subscribe((response) => {\r\n        this.displayError = true;\r\n        this.items = response.errors.map(x => x.errorMessage);\r\n      });\r\n    }\r\n    else {\r\n      this.router.navigate(['/es-import/importdetail'], { queryParams: { id: data.id} });\r\n    }\r\n  }\r\n\r\n  rowClicked(event):void{\r\n    this.items = [];\r\n    this.errorFileId = \"\";\r\n    this.selectedRowStatusId = null;\r\n    const data = event.rowData.rawData as DeclarationImportFileDto;\r\n    if (data.status === UploadedFileStatus.FileErrors) {\r\n      this.declarationImportFileService.getFileErrorsByFileId(data.id).subscribe((response) => {\r\n        this.errorFileId = data.id;\r\n        this.selectedRowStatusId = data.status;\r\n        this.displayError = true;\r\n        this.items = response.errors.map(x => x.errorMessage);\r\n      });\r\n    }\r\n    else if (data.status === UploadedFileStatus.ReadyForTriage && data.numberOfWithErrorDeclarations > 0){\r\n      this.declarationImportFileService.getDataErrorsByFileId(data.id).subscribe((response) => {\r\n        this.errorFileId = data.id;\r\n        this.selectedRowStatusId = data.status;\r\n        this.displayError = true;\r\n        if (!response.isValid) {\r\n          this.items = response.errors.map(x => x.errorMessage);\r\n        }\r\n      });\r\n    }\r\n    else {\r\n      this.errorFileId = \"\";\r\n      this.displayError = false;\r\n    }\r\n  }\r\n\r\n  applyClick()\r\n  {    \r\n    this.input.uploadedDateTime = this.getFormattedDate(this.uploadedDate);\r\n    this.declarationImportFileService.getFileListByInput(this.input).subscribe((response) => {\r\n      this.totalRecords = response.totalCount;\r\n      this.importResultRecords = response.items;\r\n\r\n      setTimeout(() => { this.setTableData(); }, 200);\r\n    });\r\n  }\r\n\r\n  resetSearchClick()\r\n  {\r\n    this.uploadedDate = undefined;\r\n    this.onLazyLoadEvent(undefined);\r\n  }\r\n\r\n  routeToImportedDataPage() {\r\n    if (this.activeFileDetail)\r\n    this.router.navigate(['/es-import/importdetail'], { queryParams: { id: this.activeFileDetail.id } });\r\n\r\n  }\r\n\r\n  downloadFileErrors(){\r\n    if(this.errorFileId !== ''){\r\n      if (this.selectedRowStatusId === UploadedFileStatus.FileErrors){\r\n        this.declarationImportFileService.downloadFileErrors(this.errorFileId).subscribe(result=>{\r\n          const decodedData = atob(result.toString());\r\n          const blob = new Blob([decodedData], { type: 'text/csv' });\r\n          saveAs(blob, `FileErrors_${this.errorFileId}.csv`);\r\n        });\r\n      } else if (this.selectedRowStatusId === UploadedFileStatus.ReadyForTriage) {\r\n        this.declarationImportFileService.downloadDataErrors(this.errorFileId).subscribe(result=>{\r\n          const decodedData = atob(result.toString());\r\n          const blob = new Blob([decodedData], { type: 'text/csv' });\r\n          saveAs(blob, `DataErrors_${this.errorFileId}.csv`);\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  private subscribeToEvents(): void {\r\n    this.notificationSubscription = this.notificationSignalRService.messageReceived.subscribe(\r\n      (message: string) => {\r\n        if (message) {\r\n          this.UploadStatus = message;\r\n        }\r\n      }\r\n    );\r\n  }\r\n  \r\n  private pollingUploadProgress()\r\n  {\r\n    const apiCallInterval$ = interval(2000);\r\n    apiCallInterval$\r\n      .pipe(\r\n        switchMap(() => this.declarationImportFileService.getUploadProgress()),\r\n        takeWhile((data) => !this.stopPolling && data.statusName !== 'Failed to Import' \r\n        && data.statusName !== 'Ready For Triage' && data.statusName !== 'Has File Errors')\r\n      )\r\n      .subscribe(\r\n        {\r\n          next: (response) => {\r\n            //console.log(response);\r\n            this.UploadStatus = response.message;\r\n          },\r\n          error:(err) => {\r\n            this.stopPolling = true;\r\n            console.log(\"Error occured when polling file import progress (event bus message)\", err);\r\n          }\r\n        }\r\n      );\r\n  }\r\n\r\n  private pollingUploadProgressByFile() {\r\n    const apiCallInterval$ = interval(2000);\r\n\r\n    // Use switchMap to trigger an API call at each interval\r\n    apiCallInterval$\r\n      .pipe(\r\n        switchMap(() => this.declarationImportFileService.getLastImportedFile()),\r\n        takeWhile((data) => !this.stopPollingByFile && data.statusName !== 'Failed to Import' \r\n          && data.statusName !== 'Ready For Triage' && data.statusName !== 'Has File Errors')\r\n      )\r\n      .subscribe(\r\n        {\r\n          next: (response) => {\r\n            this.displayUploadStatusByFile(response);\r\n          },\r\n          error:(err) => {\r\n            this.stopPollingByFile = true;\r\n            console.log(\"Error occured when polling file import progress\", err);\r\n          }\r\n        }\r\n      );\r\n  }\r\n\r\n  private displayUploadStatusByFile(data: DeclarationImportFileDto) {\r\n    this.onLazyLoadEvent(undefined);\r\n    this.UploadStatus = data.statusName;\r\n    this.selectedFile = null;\r\n    this.fileInput.nativeElement.value = '';\r\n    this.items = [];\r\n    if (data.status === UploadedFileStatus.FileErrors) {\r\n      this.declarationImportFileService.getFileErrorsByFileId(data.id).subscribe((response) => {\r\n        this.displayError = true;\r\n        this.items = response.errors.map(x => x.errorMessage);\r\n      });\r\n    }\r\n    else{\r\n      this.displayError = false;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n", "<div class=\"display-flex-main\">\r\n  \r\n  <div class=\"left-column\">\r\n    <div class=\"display-flex\">\r\n      <div class=\"top-action-row-import\">\r\n        <div class=\"upload-import-container\">\r\n          <span class=\"upload-file\">\r\n            <a id=\"importedDataUploadFile\"> UPLOAD FILE</a>\r\n          </span>\r\n\r\n\r\n          <span class=\"import-file\" *ngIf=\"activeFileDetail\">\r\n            <a id=\"importedDataUploadFile\" (click)=\"routeToImportedDataPage()\" style=\"cursor: pointer; text-decoration: underline;\"> IMPORTED DATA</a>\r\n          </span>\r\n        </div>\r\n        <div class=\"error-message\">\r\n          <span>\r\n            {{actionRequiredMessage}}\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <span class=\"fill-extra-space\"></span>\r\n\r\n\r\n      <div class=\"top-action-row\">\r\n\r\n        <mat-form-field class=\"custom-datepicker\">\r\n          <mat-label>Search Date</mat-label>\r\n          <input matInput [matDatepicker]=\"uploadDatepicker\" [(ngModel)]=\"uploadedDate\" title=\"Choose Date\"/>\r\n          <mat-datepicker-toggle matIconSuffix [for]=\"uploadDatepicker\"></mat-datepicker-toggle>\r\n          <mat-datepicker #uploadDatepicker></mat-datepicker>\r\n        </mat-form-field>\r\n\r\n        <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\" (click)=\"applyClick()\">Apply</button>\r\n        <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\" (click)=\"resetSearchClick()\">Reset Search</button>\r\n        <button type=\"button\" mat-raised-button class=\"ui-button margin-l-5\" (click)=\"downloadFileErrors()\" [disabled] = \"errorFileId === '' \">Download Errors</button>\r\n      </div>\r\n    </div>\r\n\r\n    \r\n    <div class=\"table-container\">\r\n      <bdo-table [id]=\"TableId\"\r\n                 [columns]=\"importResultColumns\"\r\n                 scrollHeight=\"68vh\"                  \r\n                 defaultSortColumnId=\"uploadedDateTime\"\r\n                 [defaultSortOrder]=\"'desc'\"\r\n                 [pageIndex]=\"currentPageIndex\"\r\n                 [pageSize]=\"PageSize\"\r\n                 [isVirtualScroll]=\"false\"\r\n                 [hidePagination]=\"false\"\r\n                 [rowSelectable]=\"true\"\r\n                 [lazyLoad]=\"true\"\r\n                 [pageSizeOptions]=\"[10, 20, 50, 100, 200]\"\r\n                 (onLinkClick)=\"onLinkClick($event)\"\r\n                 (onLazyLoad)=\"onLazyLoadEvent($event)\"\r\n                 (onRowClick) = \"rowClicked($event)\">\r\n      </bdo-table>\r\n    </div>\r\n  </div>\r\n  <div class=\"right-column\">\r\n    <div>\r\n      <span class=\"left-column-titles\">SELECT FILE</span>\r\n      <mat-divider class=\"divider-margin\"></mat-divider>\r\n      <div class=\"entity-detail-container\">\r\n        <div *ngIf=\"canUploadFile()\">\r\n          <div>\r\n            <input #uploadFile title=\"Select File\"\r\n                  accept=\"*\"\r\n                  type=\"file\"\r\n                  class=\"form-control\"\r\n                  id=\"UploadProfileImportFile\"\r\n                  (change)=\"onSelectFile($event)\" />\r\n          </div>\r\n          <div>\r\n            <button type=\"button\" mat-raised-button class=\"ui-button button-right\" [disabled]=\"selectedFile == null || hasActiveFile || uploading\" (click)=\"uploadFileClick()\">Upload</button>\r\n          </div>          \r\n        </div>\r\n        <div class=\"detail-note_header\">\r\n          If the number of records in your file upload exceeds 100, you may need to refresh the page after 5 minutes to see the uploaded records displayed.\r\n        </div>\r\n        <div class=\"processing-message\">\r\n          Upload progress: {{UploadStatus}}\r\n        </div>\r\n        <div class=\"detail-note\">\r\n          <div *ngIf=\"displayError\" class=\"errormessage\">\r\n            <ul>\r\n              <li *ngFor=\"let item of items\">\r\n                {{ item }}\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n</div>\r\n"], "mappings": "AAEA,SAA+DA,kBAAkB,EAAEC,YAAY,QAAqC,kDAAkD;AACtL,SAASC,kBAAkB,QAAQ,6GAA6G;AAEhJ,SAASC,gBAAgB,QAAQ,gCAAgC;AAIjE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAAuBC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,QAAa,MAAM;;;;;;;;;;;;;;;;;;;;ICEtEC,EADF,CAAAC,cAAA,eAAmD,YACuE;IAAzFD,EAAA,CAAAE,UAAA,mBAAAC,8DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,uBAAA,EAAyB;IAAA,EAAC;IAAuDT,EAAA,CAAAU,MAAA,qBAAa;IACxIV,EADwI,CAAAW,YAAA,EAAI,EACrI;;;;;;IAqDLX,EAFJ,CAAAC,cAAA,UAA6B,UACtB,mBAMqC;IAAlCD,EAAA,CAAAE,UAAA,oBAAAU,mEAAAC,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAS,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IACvCb,EANE,CAAAW,YAAA,EAKwC,EACpC;IAEJX,EADF,CAAAC,cAAA,UAAK,iBACgK;IAA5BD,EAAA,CAAAE,UAAA,mBAAAc,mEAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,eAAA,EAAiB;IAAA,EAAC;IAACjB,EAAA,CAAAU,MAAA,aAAM;IAE7KV,EAF6K,CAAAW,YAAA,EAAS,EAC9K,EACF;;;;IAFqEX,EAAA,CAAAkB,SAAA,GAA+D;IAA/DlB,EAAA,CAAAmB,UAAA,aAAAb,MAAA,CAAAc,YAAA,YAAAd,MAAA,CAAAe,aAAA,IAAAf,MAAA,CAAAgB,SAAA,CAA+D;;;;;IAYpItB,EAAA,CAAAC,cAAA,SAA+B;IAC7BD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAuB,kBAAA,MAAAC,OAAA,MACF;;;;;IAHFxB,EADF,CAAAC,cAAA,cAA+C,SACzC;IACFD,EAAA,CAAAyB,UAAA,IAAAC,+CAAA,iBAA+B;IAInC1B,EADE,CAAAW,YAAA,EAAK,EACD;;;;IAJmBX,EAAA,CAAAkB,SAAA,GAAQ;IAARlB,EAAA,CAAAmB,UAAA,YAAAb,MAAA,CAAAqB,KAAA,CAAQ;;;ADlE3C,OAAM,MAAOC,0BAA2B,SAAQlC,gBAAgB;EA8E9DmC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB;IACA,IAAI,CAACC,eAAe,CAACC,SAAS,CAAC;EACjC;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,wBAAwB,EAAE,IAAI,CAACA,wBAAwB,CAACC,WAAW,EAAE;IAC9EC,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;IAC5E,IAAI,CAACC,0BAA0B,CAACC,eAAe,EAAE;EACnD;EAEAC,YACEC,QAAkB,EACVC,MAAc,EACdC,4BAA0D,EAC1DC,iBAAoC,EACpCN,0BAAsD,EACtDO,iBAAoC;IAE5C,KAAK,CAACJ,QAAQ,CAAC;IANP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,4BAA4B,GAA5BA,4BAA4B;IAC5B,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAN,0BAA0B,GAA1BA,0BAA0B;IAC1B,KAAAO,iBAAiB,GAAjBA,iBAAiB;IA9F3B,KAAAC,OAAO,GAAG,gBAAgB;IAC1B,KAAAC,gBAAgB,GAAG,CAAC;IACZ,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,WAAW,GAAY,KAAK;IAGpC,KAAAC,mBAAmB,GAA+B,CAChD;MACEC,QAAQ;MACRC,IAAI,EAAE7D,kBAAkB,CAAC8D,IAAI;MAC7BC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7D,kBAAkB,CAACmE,IAAI;MAC7BJ,QAAQ,EAAE,GAAG;MACbE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7D,kBAAkB,CAACoE,MAAM;MAC/BL,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7D,kBAAkB,CAACoE,MAAM;MAC/BL,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7D,kBAAkB,CAACqE,MAAM;MAC/BN,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7D,kBAAkB,CAACoE,MAAM;MAC/BL,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,EACD;MACEN,QAAQ;MACRC,IAAI,EAAE7D,kBAAkB,CAACoE,MAAM;MAC/BL,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACb,CACF;IACD,KAAAI,QAAQ,GAAG,EAAE;IACb,KAAAzC,YAAY,GAAS,IAAI;IACzB,KAAA0C,YAAY,GAAG,EAAE;IACjB,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,YAAY,GAAM,EAAE;IACpB,KAAAC,KAAK,GAAgC;MAAEC,cAAc,EAAE,EAAE;MAAEC,SAAS,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAuB,CAAE;IAC3G,KAAAC,YAAY,GAAUrC,SAAS;IAC/B,KAAAsC,mBAAmB,GAAG,EAAE;IACxB,KAAAC,YAAY,GAAC,CAAC;IACd,KAAA5C,KAAK,GAAG,EAAE;IACV,KAAA6C,eAAe,GAAU,CAAC/E,kBAAkB,CAACgF,UAAU,EAAEhF,kBAAkB,CAACiF,QAAQ,CAAC;IACrF,KAAArD,aAAa,GAAY,KAAK;IAC9B,KAAAsD,qBAAqB,GAAS,EAAE;IAEhC,KAAAC,WAAW,GAAW,EAAE;IAExB,KAAAtD,SAAS,GAAG,KAAK;EAwBjB;EAEAuD,aAAaA,CAAA;IAEX,OAAO,IAAI,CAAChC,iBAAiB,CAACiC,gBAAgB,oFAAqC;EACrF;EAEA/C,eAAeA,CAACgD,KAAK;IACnB,IAAIA,KAAK,EAAE;MACT,IAAI,IAAI,CAAClB,QAAQ,MAAMkB,KAAK,CAACC,QAAQ,IAAI,EAAE,CAAC,EAAE;QAC5C,IAAI,CAACjC,gBAAgB,GAAGgC,KAAK,CAACE,UAAU,IAAI,CAAC;MAC/C,CAAC,MAAM;QACL,IAAI,CAACpB,QAAQ,GAAGkB,KAAK,CAACC,QAAQ,IAAI,EAAE;QACpC,IAAI,CAACjC,gBAAgB,GAAG,CAAC;MAC3B;MACA,IAAI,CAACkB,KAAK,CAACE,SAAS,GAAG,CAAC,IAAI,CAACpB,gBAAgB,IAAI,CAAC,KAAK,IAAI,CAACc,QAAQ,IAAI,EAAE,CAAC;MAC3E,IAAI,CAACI,KAAK,CAACC,cAAc,GAAG,IAAI,CAACL,QAAQ,IAAI,EAAE;MAC/C,IAAIkB,KAAK,CAACG,WAAW,KAAK,KAAK,EAAE;QAC/B,IAAI,CAACjB,KAAK,CAACG,OAAO,GAAG,GAAGW,KAAK,CAACI,SAAS,OAAO;MAChD,CAAC,MACK;QACJ,IAAI,CAAClB,KAAK,CAACG,OAAO,GAAG,GAAGW,KAAK,CAACI,SAAS,MAAM;MAC/C;IACF,CAAC,MACK;MACJ,IAAI,CAAClB,KAAK,CAACmB,gBAAgB,GAAGpD,SAAS;MACvC,IAAI,CAACiC,KAAK,CAACG,OAAO,GAAG,uBAAuB;MAC5C,IAAI,CAACrB,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACc,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACI,KAAK,CAACE,SAAS,GAAG,CAAC;MACxB,IAAI,CAACF,KAAK,CAACC,cAAc,GAAG,IAAI,CAACL,QAAQ;IAC3C;IAEA,IAAI,CAAClB,4BAA4B,CAAC0C,kBAAkB,CAAC,IAAI,CAACpB,KAAK,CAAC,CAACqB,SAAS,CAAEC,QAAQ,IAAI;MAEtF,IAAI,CAAChB,YAAY,GAAGgB,QAAQ,CAACC,UAAU;MACvC,IAAI,CAAClB,mBAAmB,GAAGiB,QAAQ,CAAC5D,KAAK;MAEzC8D,UAAU,CAAC,MAAK;QAAG,IAAI,CAACC,YAAY,EAAE;MAAE,CAAC,EAAE,GAAG,CAAC;IACjD,CAAC,CAAC;IAGF,IAAI,CAAC/C,4BAA4B,CAACgD,eAAe,EAAE,CAACL,SAAS,CAAEC,QAAQ,IAAI;MACzE,IAAI,CAAClE,aAAa,GAAGkE,QAAQ;IAC/B,CAAC,CAAC;IAEF,IAAI,CAAC5C,4BAA4B,CAACiD,eAAe,EAAE,CAACN,SAAS,CAAEC,QAAQ,IAAI;MAEzE,IAAIA,QAAQ,IAAI,IAAI,EAAE;QACpB,IAAI,CAACM,gBAAgB,GAAGN,QAAQ;QAChC,IAAI,CAACZ,qBAAqB,GAAG,GAAG,IAAI,CAACkB,gBAAgB,CAACC,QAAQ,mBAAmB;MACnF,CAAC,MACI;QACH,IAAI,CAACD,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAAClB,qBAAqB,GAAG,EAAE;MACjC;IACF,CAAC,CAAC;EACJ;EAEAe,YAAYA,CAAA;IACV,MAAMK,SAAS,GAAG,IAAIvG,YAAY,EAAE;IACpCuG,SAAS,CAACC,gBAAgB,GAAG,KAAK;IAClCD,SAAS,CAACE,OAAO,GAAG,IAAI,CAACnD,OAAO;IAChCiD,SAAS,CAACxB,YAAY,GAAG,IAAI,CAACA,YAAY;IAC1CwB,SAAS,CAACG,IAAI,GAAG,IAAI,CAAC5B,mBAAmB,CAAC6B,GAAG,CAACC,CAAC,IAAG;MAChD,OAAO;QACLC,EAAE,EAAED,CAAC,CAACC,EAAE;QACRC,OAAO,EAAEF,CAAC;QACVG,KAAK,EAAE,CACL;UAAEpD,QAAQ;UAA8BqD,KAAK,EAAEJ,CAAC,CAAChB;QAAgB,CAAE,EACnE;UAAEjC,QAAQ;UAAmCqD,KAAK,EAAEJ,CAAC,CAACN;QAAQ,CAAE,EAChE;UAAE3C,QAAQ;UAAsCqD,KAAK,EAAEJ,CAAC,CAACK;QAAa,CAAE,EACxE;UAAEtD,QAAQ;UAAgCqD,KAAK,EAAEJ,CAAC,CAACM;QAAU,CAAE,EAC/D;UAAEvD,QAAQ;UAAyCqD,KAAK,EAAEJ,CAAC,CAACO,MAAM,KAAKlH,kBAAkB,CAACgF,UAAU,GAAG,IAAI,GAAG2B,CAAC,CAACQ;QAAoB,CAAE,EACtI;UAAEzD,QAAQ;UAAgDqD,KAAK,EAAEJ,CAAC,CAACO,MAAM,KAAKlH,kBAAkB,CAACgF,UAAU,GAAG,IAAI,GAAG2B,CAAC,CAACS;QAA6B,CAAE,EACtJ;UAAE1D,QAAQ;UAAgDqD,KAAK,EAAEJ,CAAC,CAACO,MAAM,KAAKlH,kBAAkB,CAACgF,UAAU,GAAG,IAAI,GAAG2B,CAAC,CAACU;QAA4B,CAAE;OAGxJ;IACH,CAAC,CAAC;IACFrB,UAAU,CAAC,MAAK;MACd,IAAI,CAACsB,YAAY,CAACC,WAAW,CAACjB,SAAS,CAAC;IAC1C,CAAC,EAAE,EAAE,CAAC;EAER;EAGAkB,gBAAgBA,CAACC,IAAW;IAC1B,IAAI,CAACA,IAAI,EACP,OAAOlF,SAAS;IAElB,OAAOkF,IAAI,CAACC,WAAW,EAAE,GAAG,GAAG,IAAID,IAAI,CAACE,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGF,IAAI,CAACG,OAAO,EAAE;EAChF;EAGApG,eAAeA,CAAA;IACb,IAAI,CAACK,SAAS,GAAG,IAAI;IAErBc,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;IAExE,IAAI,CAACC,0BAA0B,CAACgF,eAAe,EAAE,CAACC,IAAI,CAAC,MAAI;MACzD;MACA;MACA;MACAnF,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MAEtE,MAAMmF,QAAQ,GAAa,IAAIC,QAAQ,EAAE;MACzCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAACtG,YAAY,CAACuG,IAAI,CAAC;MACnDH,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAACtG,YAAY,CAAC;MAC1CoG,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAACtG,YAAY,CAACgC,IAAI,CAAC;MAEnD,IAAI,CAACwE,qBAAqB,EAAE;MAE5B,IAAI,CAAChF,iBAAiB,CAACiF,kCAAkC,CAACL,QAAQ,CAAC,CAACM,IAAI,CACpElI,QAAQ,CAAC,MAAK;QACZ,IAAI,CAAC0B,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC2B,WAAW,GAAG,IAAI;MACzB,CAAC,CAAC,CACH,CACAqC,SAAS,CACV;QACEyC,IAAI,EAAGxC,QAAQ,IAAI;UACjBnD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEkD,QAAQ,CAACmB,UAAU,CAAC;UAExE,IAAI,CAAC3E,eAAe,CAACC,SAAS,CAAC;UAC/B,IAAI,CAACgC,YAAY,GAAGuB,QAAQ,CAACmB,UAAU;UACvC,IAAI,CAACtF,YAAY,GAAG,IAAI;UACxB,IAAI,CAAC4G,SAAS,CAACC,aAAa,CAACzB,KAAK,GAAG,EAAE;UACvC,IAAI,IAAI,CAAChC,eAAe,CAAC0D,QAAQ,CAAC3C,QAAQ,CAAC4C,QAAQ,CAAC,IAAI5C,QAAQ,CAAC4C,QAAQ,KAAK1I,kBAAkB,CAAC2I,cAAc,EAAE;YAC/G,IAAI,CAAC7C,QAAQ,CAAC8C,gBAAgB,CAACC,OAAO,EAAE;cACtC,IAAI,CAACvE,YAAY,GAAG,IAAI;cACxB,IAAI,CAACpC,KAAK,GAAG4D,QAAQ,CAAC8C,gBAAgB,CAACE,MAAM,CAACpC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACoC,YAAY,CAAC;YACxE;UACF,CAAC,MACG;YACF,IAAI,CAACzE,YAAY,GAAG,KAAK;UAC3B;QACF,CAAC;QACD0E,KAAK,EAAGC,GAAG,IAAI;UACb,IAAIA,GAAG,CAAC/B,MAAM,KAAK,GAAG,EAAE;YACtBvE,OAAO,CAACC,GAAG,CAAE,+CAA+C,CAAC;YAC7D,IAAI,CAACsG,2BAA2B,EAAE;UACpC;UACA,IAAI,CAAC5E,YAAY,GAAG,IAAI;UACxB,IAAI,CAACpC,KAAK,GAAG,EAAE;UACf,IAAI,CAACA,KAAK,CAACiH,IAAI,CAAC,yCAAyC,CAAC;QAC5D;OACD,CACF;IACH,CAAC,CAAC,CAACC,KAAK,CAACH,GAAG,IAAE;MACZ;MACAtG,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqG,GAAG,CAAC;MAClD;MACA,IAAI,CAACpG,0BAA0B,CAACC,eAAe,EAAE;IACnD,CAAC,CAAC;EACJ;EAEAxB,YAAYA,CAACgE,KAAU;IACrB,IAAI,CAAC3D,YAAY,GAAG2D,KAAK,CAAC+D,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EAC3C;EAEAC,WAAWA,CAACjE,KAAiC;IAE3C,IAAI,CAACpD,KAAK,GAAG,EAAE;IACf,MAAMuE,IAAI,GAAGnB,KAAK,CAACuB,OAAmC;IACtD,IAAIJ,IAAI,CAACS,MAAM,KAAKlH,kBAAkB,CAACgF,UAAU,EAAE;MACjD,IAAI,CAAC9B,4BAA4B,CAACsG,qBAAqB,CAAC/C,IAAI,CAACG,EAAE,CAAC,CAACf,SAAS,CAAEC,QAAQ,IAAI;QACtF,IAAI,CAACxB,YAAY,GAAG,IAAI;QACxB,IAAI,CAACpC,KAAK,GAAG4D,QAAQ,CAACgD,MAAM,CAACpC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACoC,YAAY,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,MACI;MACH,IAAI,CAAC9F,MAAM,CAACwG,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAE9C,EAAE,EAAEH,IAAI,CAACG;QAAE;MAAC,CAAE,CAAC;IACpF;EACF;EAEA+C,UAAUA,CAACrE,KAAK;IACd,IAAI,CAACpD,KAAK,GAAG,EAAE;IACf,IAAI,CAACiD,WAAW,GAAG,EAAE;IACrB,IAAI,CAACyE,mBAAmB,GAAG,IAAI;IAC/B,MAAMnD,IAAI,GAAGnB,KAAK,CAACuE,OAAO,CAAChD,OAAmC;IAC9D,IAAIJ,IAAI,CAACS,MAAM,KAAKlH,kBAAkB,CAACgF,UAAU,EAAE;MACjD,IAAI,CAAC9B,4BAA4B,CAACsG,qBAAqB,CAAC/C,IAAI,CAACG,EAAE,CAAC,CAACf,SAAS,CAAEC,QAAQ,IAAI;QACtF,IAAI,CAACX,WAAW,GAAGsB,IAAI,CAACG,EAAE;QAC1B,IAAI,CAACgD,mBAAmB,GAAGnD,IAAI,CAACS,MAAM;QACtC,IAAI,CAAC5C,YAAY,GAAG,IAAI;QACxB,IAAI,CAACpC,KAAK,GAAG4D,QAAQ,CAACgD,MAAM,CAACpC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACoC,YAAY,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,MACI,IAAItC,IAAI,CAACS,MAAM,KAAKlH,kBAAkB,CAAC2I,cAAc,IAAIlC,IAAI,CAACW,6BAA6B,GAAG,CAAC,EAAC;MACnG,IAAI,CAAClE,4BAA4B,CAAC4G,qBAAqB,CAACrD,IAAI,CAACG,EAAE,CAAC,CAACf,SAAS,CAAEC,QAAQ,IAAI;QACtF,IAAI,CAACX,WAAW,GAAGsB,IAAI,CAACG,EAAE;QAC1B,IAAI,CAACgD,mBAAmB,GAAGnD,IAAI,CAACS,MAAM;QACtC,IAAI,CAAC5C,YAAY,GAAG,IAAI;QACxB,IAAI,CAACwB,QAAQ,CAAC+C,OAAO,EAAE;UACrB,IAAI,CAAC3G,KAAK,GAAG4D,QAAQ,CAACgD,MAAM,CAACpC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACoC,YAAY,CAAC;QACvD;MACF,CAAC,CAAC;IACJ,CAAC,MACI;MACH,IAAI,CAAC5D,WAAW,GAAG,EAAE;MACrB,IAAI,CAACb,YAAY,GAAG,KAAK;IAC3B;EACF;EAEAyF,UAAUA,CAAA;IAER,IAAI,CAACvF,KAAK,CAACmB,gBAAgB,GAAG,IAAI,CAAC6B,gBAAgB,CAAC,IAAI,CAAC5C,YAAY,CAAC;IACtE,IAAI,CAAC1B,4BAA4B,CAAC0C,kBAAkB,CAAC,IAAI,CAACpB,KAAK,CAAC,CAACqB,SAAS,CAAEC,QAAQ,IAAI;MACtF,IAAI,CAAChB,YAAY,GAAGgB,QAAQ,CAACC,UAAU;MACvC,IAAI,CAAClB,mBAAmB,GAAGiB,QAAQ,CAAC5D,KAAK;MAEzC8D,UAAU,CAAC,MAAK;QAAG,IAAI,CAACC,YAAY,EAAE;MAAE,CAAC,EAAE,GAAG,CAAC;IACjD,CAAC,CAAC;EACJ;EAEA+D,gBAAgBA,CAAA;IAEd,IAAI,CAACpF,YAAY,GAAGrC,SAAS;IAC7B,IAAI,CAACD,eAAe,CAACC,SAAS,CAAC;EACjC;EAEAvB,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAACoF,gBAAgB,EACzB,IAAI,CAACnD,MAAM,CAACwG,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;MAAEC,WAAW,EAAE;QAAE9C,EAAE,EAAE,IAAI,CAACR,gBAAgB,CAACQ;MAAE;IAAE,CAAE,CAAC;EAEtG;EAEAqD,kBAAkBA,CAAA;IAChB,IAAG,IAAI,CAAC9E,WAAW,KAAK,EAAE,EAAC;MACzB,IAAI,IAAI,CAACyE,mBAAmB,KAAK5J,kBAAkB,CAACgF,UAAU,EAAC;QAC7D,IAAI,CAAC9B,4BAA4B,CAAC+G,kBAAkB,CAAC,IAAI,CAAC9E,WAAW,CAAC,CAACU,SAAS,CAACqE,MAAM,IAAE;UACvF,MAAMC,WAAW,GAAGC,IAAI,CAACF,MAAM,CAACG,QAAQ,EAAE,CAAC;UAC3C,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,WAAW,CAAC,EAAE;YAAExG,IAAI,EAAE;UAAU,CAAE,CAAC;UAC1DzD,MAAM,CAACoK,IAAI,EAAE,cAAc,IAAI,CAACnF,WAAW,MAAM,CAAC;QACpD,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAACyE,mBAAmB,KAAK5J,kBAAkB,CAAC2I,cAAc,EAAE;QACzE,IAAI,CAACzF,4BAA4B,CAACsH,kBAAkB,CAAC,IAAI,CAACrF,WAAW,CAAC,CAACU,SAAS,CAACqE,MAAM,IAAE;UACvF,MAAMC,WAAW,GAAGC,IAAI,CAACF,MAAM,CAACG,QAAQ,EAAE,CAAC;UAC3C,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,WAAW,CAAC,EAAE;YAAExG,IAAI,EAAE;UAAU,CAAE,CAAC;UAC1DzD,MAAM,CAACoK,IAAI,EAAE,cAAc,IAAI,CAACnF,WAAW,MAAM,CAAC;QACpD,CAAC,CAAC;MACJ;IACF;EACF;EAEQ9C,iBAAiBA,CAAA;IACvB,IAAI,CAACI,wBAAwB,GAAG,IAAI,CAACI,0BAA0B,CAAC4H,eAAe,CAAC5E,SAAS,CACtF6E,OAAe,IAAI;MAClB,IAAIA,OAAO,EAAE;QACX,IAAI,CAACnG,YAAY,GAAGmG,OAAO;MAC7B;IACF,CAAC,CACF;EACH;EAEQvC,qBAAqBA,CAAA;IAE3B,MAAMwC,gBAAgB,GAAGvK,QAAQ,CAAC,IAAI,CAAC;IACvCuK,gBAAgB,CACbtC,IAAI,CACHhI,SAAS,CAAC,MAAM,IAAI,CAAC6C,4BAA4B,CAAC0H,iBAAiB,EAAE,CAAC,EACtEtK,SAAS,CAAEmG,IAAI,IAAK,CAAC,IAAI,CAACjD,WAAW,IAAIiD,IAAI,CAACQ,UAAU,KAAK,kBAAkB,IAC5ER,IAAI,CAACQ,UAAU,KAAK,kBAAkB,IAAIR,IAAI,CAACQ,UAAU,KAAK,iBAAiB,CAAC,CACpF,CACApB,SAAS,CACR;MACEyC,IAAI,EAAGxC,QAAQ,IAAI;QACjB;QACA,IAAI,CAACvB,YAAY,GAAGuB,QAAQ,CAAC4E,OAAO;MACtC,CAAC;MACD1B,KAAK,EAAEC,GAAG,IAAI;QACZ,IAAI,CAACzF,WAAW,GAAG,IAAI;QACvBb,OAAO,CAACC,GAAG,CAAC,qEAAqE,EAAEqG,GAAG,CAAC;MACzF;KACD,CACF;EACL;EAEQC,2BAA2BA,CAAA;IACjC,MAAMyB,gBAAgB,GAAGvK,QAAQ,CAAC,IAAI,CAAC;IAEvC;IACAuK,gBAAgB,CACbtC,IAAI,CACHhI,SAAS,CAAC,MAAM,IAAI,CAAC6C,4BAA4B,CAAC2H,mBAAmB,EAAE,CAAC,EACxEvK,SAAS,CAAEmG,IAAI,IAAK,CAAC,IAAI,CAAClD,iBAAiB,IAAIkD,IAAI,CAACQ,UAAU,KAAK,kBAAkB,IAChFR,IAAI,CAACQ,UAAU,KAAK,kBAAkB,IAAIR,IAAI,CAACQ,UAAU,KAAK,iBAAiB,CAAC,CACtF,CACApB,SAAS,CACR;MACEyC,IAAI,EAAGxC,QAAQ,IAAI;QACjB,IAAI,CAACgF,yBAAyB,CAAChF,QAAQ,CAAC;MAC1C,CAAC;MACDkD,KAAK,EAAEC,GAAG,IAAI;QACZ,IAAI,CAAC1F,iBAAiB,GAAG,IAAI;QAC7BZ,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEqG,GAAG,CAAC;MACrE;KACD,CACF;EACL;EAEQ6B,yBAAyBA,CAACrE,IAA8B;IAC9D,IAAI,CAACnE,eAAe,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACgC,YAAY,GAAGkC,IAAI,CAACQ,UAAU;IACnC,IAAI,CAACtF,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC4G,SAAS,CAACC,aAAa,CAACzB,KAAK,GAAG,EAAE;IACvC,IAAI,CAAC7E,KAAK,GAAG,EAAE;IACf,IAAIuE,IAAI,CAACS,MAAM,KAAKlH,kBAAkB,CAACgF,UAAU,EAAE;MACjD,IAAI,CAAC9B,4BAA4B,CAACsG,qBAAqB,CAAC/C,IAAI,CAACG,EAAE,CAAC,CAACf,SAAS,CAAEC,QAAQ,IAAI;QACtF,IAAI,CAACxB,YAAY,GAAG,IAAI;QACxB,IAAI,CAACpC,KAAK,GAAG4D,QAAQ,CAACgD,MAAM,CAACpC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACoC,YAAY,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,MACG;MACF,IAAI,CAACzE,YAAY,GAAG,KAAK;IAC3B;EACF;;;uBAjaWnC,0BAA0B,EAAA5B,EAAA,CAAAwK,iBAAA,CAAAxK,EAAA,CAAAyK,QAAA,GAAAzK,EAAA,CAAAwK,iBAAA,CAAAE,EAAA,CAAAC,MAAA,GAAA3K,EAAA,CAAAwK,iBAAA,CAAAI,EAAA,CAAAC,4BAAA,GAAA7K,EAAA,CAAAwK,iBAAA,CAAAM,EAAA,CAAAC,iBAAA,GAAA/K,EAAA,CAAAwK,iBAAA,CAAAQ,EAAA,CAAAC,0BAAA,GAAAjL,EAAA,CAAAwK,iBAAA,CAAAU,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAA1BvJ,0BAA0B;MAAAwJ,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCb3BvL,EAPZ,CAAAC,cAAA,aAA+B,aAEJ,aACG,aACW,aACI,cACT,WACO;UAACD,EAAA,CAAAU,MAAA,mBAAW;UAC7CV,EAD6C,CAAAW,YAAA,EAAI,EAC1C;UAGPX,EAAA,CAAAyB,UAAA,IAAAgK,0CAAA,kBAAmD;UAGrDzL,EAAA,CAAAW,YAAA,EAAM;UAEJX,EADF,CAAAC,cAAA,cAA2B,YACnB;UACJD,EAAA,CAAAU,MAAA,IACF;UAEJV,EAFI,CAAAW,YAAA,EAAO,EACH,EACF;UACNX,EAAA,CAAA0L,SAAA,gBAAsC;UAMlC1L,EAHJ,CAAAC,cAAA,eAA4B,0BAEgB,iBAC7B;UAAAD,EAAA,CAAAU,MAAA,mBAAW;UAAAV,EAAA,CAAAW,YAAA,EAAY;UAClCX,EAAA,CAAAC,cAAA,iBAAmG;UAAhDD,EAAA,CAAA2L,gBAAA,2BAAAC,oEAAA/K,MAAA;YAAAb,EAAA,CAAAI,aAAA,CAAAyL,GAAA;YAAA7L,EAAA,CAAA8L,kBAAA,CAAAN,GAAA,CAAAnH,YAAA,EAAAxD,MAAA,MAAA2K,GAAA,CAAAnH,YAAA,GAAAxD,MAAA;YAAA,OAAAb,EAAA,CAAAQ,WAAA,CAAAK,MAAA;UAAA,EAA0B;UAA7Eb,EAAA,CAAAW,YAAA,EAAmG;UAEnGX,EADA,CAAA0L,SAAA,iCAAsF,+BACnC;UACrD1L,EAAA,CAAAW,YAAA,EAAiB;UAEjBX,EAAA,CAAAC,cAAA,kBAA4F;UAAvBD,EAAA,CAAAE,UAAA,mBAAA6L,6DAAA;YAAA/L,EAAA,CAAAI,aAAA,CAAAyL,GAAA;YAAA,OAAA7L,EAAA,CAAAQ,WAAA,CAASgL,GAAA,CAAAhC,UAAA,EAAY;UAAA,EAAC;UAACxJ,EAAA,CAAAU,MAAA,aAAK;UAAAV,EAAA,CAAAW,YAAA,EAAS;UAC1GX,EAAA,CAAAC,cAAA,kBAAkG;UAA7BD,EAAA,CAAAE,UAAA,mBAAA8L,6DAAA;YAAAhM,EAAA,CAAAI,aAAA,CAAAyL,GAAA;YAAA,OAAA7L,EAAA,CAAAQ,WAAA,CAASgL,GAAA,CAAA/B,gBAAA,EAAkB;UAAA,EAAC;UAACzJ,EAAA,CAAAU,MAAA,oBAAY;UAAAV,EAAA,CAAAW,YAAA,EAAS;UACvHX,EAAA,CAAAC,cAAA,kBAAuI;UAAlED,EAAA,CAAAE,UAAA,mBAAA+L,6DAAA;YAAAjM,EAAA,CAAAI,aAAA,CAAAyL,GAAA;YAAA,OAAA7L,EAAA,CAAAQ,WAAA,CAASgL,GAAA,CAAA9B,kBAAA,EAAoB;UAAA,EAAC;UAAoC1J,EAAA,CAAAU,MAAA,uBAAe;UAE1JV,EAF0J,CAAAW,YAAA,EAAS,EAC3J,EACF;UAIJX,EADF,CAAAC,cAAA,eAA6B,qBAeoB;UAApCD,EAFA,CAAAE,UAAA,yBAAAgM,sEAAArL,MAAA;YAAAb,EAAA,CAAAI,aAAA,CAAAyL,GAAA;YAAA,OAAA7L,EAAA,CAAAQ,WAAA,CAAegL,GAAA,CAAAxC,WAAA,CAAAnI,MAAA,CAAmB;UAAA,EAAC,wBAAAsL,qEAAAtL,MAAA;YAAAb,EAAA,CAAAI,aAAA,CAAAyL,GAAA;YAAA,OAAA7L,EAAA,CAAAQ,WAAA,CACrBgL,GAAA,CAAAzJ,eAAA,CAAAlB,MAAA,CAAuB;UAAA,EAAC,wBAAAuL,qEAAAvL,MAAA;YAAAb,EAAA,CAAAI,aAAA,CAAAyL,GAAA;YAAA,OAAA7L,EAAA,CAAAQ,WAAA,CACtBgL,GAAA,CAAApC,UAAA,CAAAvI,MAAA,CAAkB;UAAA,EAAC;UAGlDb,EAFI,CAAAW,YAAA,EAAY,EACR,EACF;UAGFX,EAFJ,CAAAC,cAAA,eAA0B,WACnB,gBAC8B;UAAAD,EAAA,CAAAU,MAAA,mBAAW;UAAAV,EAAA,CAAAW,YAAA,EAAO;UACnDX,EAAA,CAAA0L,SAAA,uBAAkD;UAClD1L,EAAA,CAAAC,cAAA,eAAqC;UACnCD,EAAA,CAAAyB,UAAA,KAAA4K,0CAAA,kBAA6B;UAa7BrM,EAAA,CAAAC,cAAA,eAAgC;UAC9BD,EAAA,CAAAU,MAAA,2JACF;UAAAV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,eAAgC;UAC9BD,EAAA,CAAAU,MAAA,IACF;UAAAV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAyB,UAAA,KAAA6K,0CAAA,kBAA+C;UAazDtM,EANQ,CAAAW,YAAA,EAAM,EAEF,EACF,EACF,EAEF;;;;UAtF+BX,EAAA,CAAAkB,SAAA,GAAsB;UAAtBlB,EAAA,CAAAmB,UAAA,SAAAqK,GAAA,CAAA3F,gBAAA,CAAsB;UAM/C7F,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAuB,kBAAA,MAAAiK,GAAA,CAAA7G,qBAAA,MACF;UAUgB3E,EAAA,CAAAkB,SAAA,GAAkC;UAAlClB,EAAA,CAAAmB,UAAA,kBAAAoL,mBAAA,CAAkC;UAACvM,EAAA,CAAAwM,gBAAA,YAAAhB,GAAA,CAAAnH,YAAA,CAA0B;UACxCrE,EAAA,CAAAkB,SAAA,EAAwB;UAAxBlB,EAAA,CAAAmB,UAAA,QAAAoL,mBAAA,CAAwB;UAMqCvM,EAAA,CAAAkB,SAAA,GAAkC;UAAlClB,EAAA,CAAAmB,UAAA,aAAAqK,GAAA,CAAA5G,WAAA,QAAkC;UAM7H5E,EAAA,CAAAkB,SAAA,GAAc;UAWdlB,EAXA,CAAAmB,UAAA,OAAAqK,GAAA,CAAA1I,OAAA,CAAc,YAAA0I,GAAA,CAAAtI,mBAAA,CACiB,4BAGJ,cAAAsI,GAAA,CAAAzI,gBAAA,CACG,aAAAyI,GAAA,CAAA3H,QAAA,CACT,0BACI,yBACD,uBACF,kBACL,oBAAA7D,EAAA,CAAAyM,eAAA,KAAAC,GAAA,EACyB;UAY7C1M,EAAA,CAAAkB,SAAA,GAAqB;UAArBlB,EAAA,CAAAmB,UAAA,SAAAqK,GAAA,CAAA3G,aAAA,GAAqB;UAiBzB7E,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAuB,kBAAA,uBAAAiK,GAAA,CAAAxH,YAAA,MACF;UAEQhE,EAAA,CAAAkB,SAAA,GAAkB;UAAlBlB,EAAA,CAAAmB,UAAA,SAAAqK,GAAA,CAAAzH,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}