﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Http.Client.IdentityModel;
using Volo.Abp.Modularity;
using Volo.Abp.Timing;

namespace Bdo.Ess.Jobs.Dashboard
{
    [DependsOn(
        //typeof(ClientServiceHttpApiClientModule),
        typeof(AbpTimingModule),
        typeof(AbpHttpClientIdentityModelModule)
        )]
    public class DashboardJobModule : AbpModule
    {
        public override void PreConfigureServices(ServiceConfigurationContext context)
        {
            // Set the clock provider to UTC
            Configure<AbpClockOptions>(options =>
            {
                options.Kind = DateTimeKind.Utc;
            });
        }
    }
}
