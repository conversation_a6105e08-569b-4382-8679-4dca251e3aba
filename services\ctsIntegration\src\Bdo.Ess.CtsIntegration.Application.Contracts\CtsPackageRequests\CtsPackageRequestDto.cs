using Bdo.Ess.CtsIntegration.Constants;
using Bdo.Ess.CtsIntegration.enums;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests
{
    public class CtsPackageRequestDto : EntityDto<Guid>
    {
        public ExchangeReason? ExchangeReason { get; set; }
        public Guid CtsPackageId { get; set; }
        public string? DataPacket { get; set; }
        public int FinancialPeriodEndYear { get; set; }
        public DateTime? FileCreationDate { get; set; }
        public string ReceivingCountry { get; set; } = "";
        public CTSUploadStatus? CtsUploadStatus { get; set; }
        public decimal CtsUploadStatusSortValue { get; set; } = -1;
        public DateTime? UploadedAt { get; set; }
        public string? CtsTransmissionStatus { get; set; }       
        public bool ViewExchangeRecords { get; set; }
        public IList<CtsPackageCommentDto>? ViewComments { get; set; }
        public bool RegeneratePacket { get; set; }
        public bool CtsUpload { get; set; }
        public bool ExcludeFromCtsUpload { get; set; }
        public List<DataPacketAction> AllowedActions { get; set; } = new List<DataPacketAction>();

    }
}
