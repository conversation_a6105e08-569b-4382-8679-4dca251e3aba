{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class FileUploadService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'EconomicSubstanceService';\n    this.uploadDeclarationImportExcelByFile = file => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/Import/File/UploadDeclarationImportExcel',\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    /**\n     * Since Proxy client cannot generate associated web API method since IFormFile compile error, it needs to be manually called here.\n     * Called by CA portal information exchange import page, upload excel file.\n     *\n     */\n    this.uploadInformationExchangeImportExcelByFile = file => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/Import/InfoExchange/UploadInfoExchangeImportExcel',\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    this.uploadBahamasCertificate = (file, password) => {\n      // Add password to the FormData instead of query params for security\n      file.append('password', password);\n      return this.restService.request({\n        method: 'POST',\n        url: '/api/CtsIntegrationService/certificate/UploadBahamasCertificate',\n        body: file\n      }, {\n        apiName: this.apiName\n      });\n    };\n    this.uploadHistoricalXml = (file, receivingCountry, financialPeriodEnd) => this.restService.request({\n      method: 'POST',\n      url: '/api/ESService/CtsIntegration/UploadHistoricalXml',\n      params: {\n        receivingCountry,\n        financialPeriodEnd\n      },\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    this.unpackCtsPackage = file => this.restService.request({\n      method: 'POST',\n      url: '/api/CtsIntegrationService/CtsPackageRequest/UnpackCtsPackage',\n      body: file\n    }, {\n      apiName: this.apiName\n    });\n    this.createBahamasCtsSettings = (input, file, ctsCert) => {\n      const formData = new FormData();\n      // Add sensitive data to form body instead of query params for security\n      formData.append('systemUserName', input.systemUserName || '');\n      formData.append('systemUserPassword', input.systemUserPassword || '');\n      formData.append('sftpUserName', input.sftpUserName || '');\n      if (file) {\n        formData.append('file', file.get('file'));\n      }\n      if (ctsCert) {\n        formData.append('ctsCert', ctsCert.get('file'));\n      }\n      return this.restService.request({\n        method: 'POST',\n        url: '/api/CtsIntegrationService/bahamas-cts-settings',\n        body: formData\n      }, {\n        apiName: this.apiName\n      });\n    };\n    this.updateBahamasCtsSettings = (input, file, ctsCert) => {\n      const formData = new FormData();\n      // Add sensitive data to form body instead of query params for security\n      formData.append('id', input.id || '');\n      formData.append('systemUserName', input.systemUserName || '');\n      formData.append('systemUserPassword', input.systemUserPassword || '');\n      formData.append('sftpUserName', input.sftpUserName || '');\n      if (file) {\n        formData.append('file', file.get('file'));\n      }\n      if (ctsCert) {\n        formData.append('ctsCert', ctsCert.get('file'));\n      }\n      return this.restService.request({\n        method: 'PUT',\n        url: '/api/CtsIntegrationService/bahamas-cts-settings',\n        body: formData\n      }, {\n        apiName: this.apiName\n      });\n    };\n  }\n  static {\n    this.ɵfac = function FileUploadService_Factory(t) {\n      return new (t || FileUploadService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FileUploadService,\n      factory: FileUploadService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["FileUploadService", "constructor", "restService", "apiName", "uploadDeclarationImportExcelByFile", "file", "request", "method", "url", "body", "uploadInformationExchangeImportExcelByFile", "uploadBahamasCertificate", "password", "append", "uploadHistoricalXml", "receivingCountry", "financialPeriodEnd", "params", "unpackCtsPackage", "createBahamasCtsSettings", "input", "ctsCert", "formData", "FormData", "systemUserName", "systemUserPassword", "sftpUserName", "get", "updateBahamasCtsSettings", "id", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\services\\upload-file.service.ts"], "sourcesContent": ["import { RestService } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\nimport { UploadedFileStatus } from '../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declaration-imports';\r\nimport { ValidationResult } from '../../../../proxies/economic-service/lib/proxy/fluent-validation/results';\r\nimport { UploadedInfoExchangeFileStatus } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges';\r\nimport { BahamasCertificateDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/models';\r\nimport { CtsActionResultDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-requests';\r\nimport { BahamasCtsSettingDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings';\r\n\r\nexport interface ExcelUploadResultDto {\r\n  statusId: UploadedFileStatus;\r\n  statusName?: string;\r\n  validationResult: ValidationResult;\r\n}\r\n\r\nexport interface ExcelUploadInfoExchangeResultDto {\r\n  statusId: UploadedInfoExchangeFileStatus;\r\n  statusName?: string;\r\n  validationResult: ValidationResult;\r\n}\r\n\r\nexport interface UploadHistoricalXmlResultDto {\r\n  errors: string[];\r\n  success: boolean;\r\n  message?: string;\r\n}\r\n\r\nexport interface CreateBahamasCtsSettingDto {\r\n  systemUserName?: string;\r\n  systemUserPassword?: string;\r\n  sftpUserName?: string;\r\n}\r\n\r\nexport interface UpdateBahamasCtsSettingDto {\r\n  id?: string;\r\n  systemUserName?: string;\r\n  systemUserPassword?: string;\r\n  sftpUserName?: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class FileUploadService {\r\n  apiName = 'EconomicSubstanceService';\r\n\r\n  constructor(private restService: RestService) { }\r\n\r\n  uploadDeclarationImportExcelByFile = (file: FormData) =>\r\n    this.restService.request<any, ExcelUploadResultDto>(\r\n      {\r\n        method: 'POST',\r\n        url: '/api/ESService/Import/File/UploadDeclarationImportExcel',\r\n        body: file,\r\n      },\r\n      { apiName: this.apiName }\r\n    );\r\n\r\n  /**\r\n   * Since Proxy client cannot generate associated web API method since IFormFile compile error, it needs to be manually called here.\r\n   * Called by CA portal information exchange import page, upload excel file.\r\n   *\r\n   */\r\n  uploadInformationExchangeImportExcelByFile = (file: FormData) =>\r\n    this.restService.request<any, ExcelUploadInfoExchangeResultDto>(\r\n      {\r\n        method: 'POST',\r\n        url: '/api/ESService/Import/InfoExchange/UploadInfoExchangeImportExcel',\r\n        body: file,\r\n      },\r\n      { apiName: this.apiName }\r\n    );\r\n\r\n  uploadBahamasCertificate = (file: FormData, password: string) => {\r\n    // Add password to the FormData instead of query params for security\r\n    file.append('password', password);\r\n    return this.restService.request<any, BahamasCertificateDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/certificate/UploadBahamasCertificate',\r\n      body: file,\r\n    },\r\n      { apiName: this.apiName });\r\n  };\r\n\r\n  uploadHistoricalXml = (file: FormData, receivingCountry: string, financialPeriodEnd: string) =>\r\n    this.restService.request<any, UploadHistoricalXmlResultDto>({\r\n      method: 'POST',\r\n      url: '/api/ESService/CtsIntegration/UploadHistoricalXml',\r\n      params: { receivingCountry, financialPeriodEnd },\r\n      body: file,\r\n    },\r\n      { apiName: this.apiName });\r\n\r\n  unpackCtsPackage = (file: FormData) =>\r\n    this.restService.request<any, any>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/CtsPackageRequest/UnpackCtsPackage',\r\n      body: file\r\n    },\r\n      { apiName: this.apiName });\r\n\r\n  createBahamasCtsSettings = (input: CreateBahamasCtsSettingDto, file?: FormData, ctsCert?: FormData) => {\r\n    const formData = new FormData();\r\n    // Add sensitive data to form body instead of query params for security\r\n    formData.append('systemUserName', input.systemUserName || '');\r\n    formData.append('systemUserPassword', input.systemUserPassword || '');\r\n    formData.append('sftpUserName', input.sftpUserName || '');\r\n\r\n    if (file) {\r\n      formData.append('file', file.get('file') as Blob);\r\n    }\r\n    if (ctsCert) {\r\n      formData.append('ctsCert', ctsCert.get('file') as Blob);\r\n    }\r\n\r\n    return this.restService.request<any, BahamasCtsSettingDto>({\r\n      method: 'POST',\r\n      url: '/api/CtsIntegrationService/bahamas-cts-settings',\r\n      body: formData,\r\n    },\r\n      { apiName: this.apiName });\r\n  };\r\n\r\n  updateBahamasCtsSettings = (input: UpdateBahamasCtsSettingDto, file?: FormData, ctsCert?: FormData) => {\r\n    const formData = new FormData();\r\n    // Add sensitive data to form body instead of query params for security\r\n    formData.append('id', input.id || '');\r\n    formData.append('systemUserName', input.systemUserName || '');\r\n    formData.append('systemUserPassword', input.systemUserPassword || '');\r\n    formData.append('sftpUserName', input.sftpUserName || '');\r\n\r\n    if (file) {\r\n      formData.append('file', file.get('file') as Blob);\r\n    }\r\n    if (ctsCert) {\r\n      formData.append('ctsCert', ctsCert.get('file') as Blob);\r\n    }\r\n\r\n    return this.restService.request<any, BahamasCtsSettingDto>({\r\n      method: 'PUT',\r\n      url: '/api/CtsIntegrationService/bahamas-cts-settings',\r\n      body: formData,\r\n    },\r\n      { apiName: this.apiName });\r\n  };\r\n}\r\n"], "mappings": ";;AA2CA,OAAM,MAAOA,iBAAiB;EAG5BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAF/B,KAAAC,OAAO,GAAG,0BAA0B;IAIpC,KAAAC,kCAAkC,GAAIC,IAAc,IAClD,IAAI,CAACH,WAAW,CAACI,OAAO,CACtB;MACEC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,yDAAyD;MAC9DC,IAAI,EAAEJ;KACP,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B;IAEH;;;;;IAKA,KAAAO,0CAA0C,GAAIL,IAAc,IAC1D,IAAI,CAACH,WAAW,CAACI,OAAO,CACtB;MACEC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,kEAAkE;MACvEC,IAAI,EAAEJ;KACP,EACD;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B;IAEH,KAAAQ,wBAAwB,GAAG,CAACN,IAAc,EAAEO,QAAgB,KAAI;MAC9D;MACAP,IAAI,CAACQ,MAAM,CAAC,UAAU,EAAED,QAAQ,CAAC;MACjC,OAAO,IAAI,CAACV,WAAW,CAACI,OAAO,CAA6B;QAC1DC,MAAM,EAAE,MAAM;QACdC,GAAG,EAAE,iEAAiE;QACtEC,IAAI,EAAEJ;OACP,EACC;QAAEF,OAAO,EAAE,IAAI,CAACA;MAAO,CAAE,CAAC;IAC9B,CAAC;IAED,KAAAW,mBAAmB,GAAG,CAACT,IAAc,EAAEU,gBAAwB,EAAEC,kBAA0B,KACzF,IAAI,CAACd,WAAW,CAACI,OAAO,CAAoC;MAC1DC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,mDAAmD;MACxDS,MAAM,EAAE;QAAEF,gBAAgB;QAAEC;MAAkB,CAAE;MAChDP,IAAI,EAAEJ;KACP,EACC;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;IAE9B,KAAAe,gBAAgB,GAAIb,IAAc,IAChC,IAAI,CAACH,WAAW,CAACI,OAAO,CAAW;MACjCC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,+DAA+D;MACpEC,IAAI,EAAEJ;KACP,EACC;MAAEF,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;IAE9B,KAAAgB,wBAAwB,GAAG,CAACC,KAAiC,EAAEf,IAAe,EAAEgB,OAAkB,KAAI;MACpG,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B;MACAD,QAAQ,CAACT,MAAM,CAAC,gBAAgB,EAAEO,KAAK,CAACI,cAAc,IAAI,EAAE,CAAC;MAC7DF,QAAQ,CAACT,MAAM,CAAC,oBAAoB,EAAEO,KAAK,CAACK,kBAAkB,IAAI,EAAE,CAAC;MACrEH,QAAQ,CAACT,MAAM,CAAC,cAAc,EAAEO,KAAK,CAACM,YAAY,IAAI,EAAE,CAAC;MAEzD,IAAIrB,IAAI,EAAE;QACRiB,QAAQ,CAACT,MAAM,CAAC,MAAM,EAAER,IAAI,CAACsB,GAAG,CAAC,MAAM,CAAS,CAAC;MACnD;MACA,IAAIN,OAAO,EAAE;QACXC,QAAQ,CAACT,MAAM,CAAC,SAAS,EAAEQ,OAAO,CAACM,GAAG,CAAC,MAAM,CAAS,CAAC;MACzD;MAEA,OAAO,IAAI,CAACzB,WAAW,CAACI,OAAO,CAA4B;QACzDC,MAAM,EAAE,MAAM;QACdC,GAAG,EAAE,iDAAiD;QACtDC,IAAI,EAAEa;OACP,EACC;QAAEnB,OAAO,EAAE,IAAI,CAACA;MAAO,CAAE,CAAC;IAC9B,CAAC;IAED,KAAAyB,wBAAwB,GAAG,CAACR,KAAiC,EAAEf,IAAe,EAAEgB,OAAkB,KAAI;MACpG,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B;MACAD,QAAQ,CAACT,MAAM,CAAC,IAAI,EAAEO,KAAK,CAACS,EAAE,IAAI,EAAE,CAAC;MACrCP,QAAQ,CAACT,MAAM,CAAC,gBAAgB,EAAEO,KAAK,CAACI,cAAc,IAAI,EAAE,CAAC;MAC7DF,QAAQ,CAACT,MAAM,CAAC,oBAAoB,EAAEO,KAAK,CAACK,kBAAkB,IAAI,EAAE,CAAC;MACrEH,QAAQ,CAACT,MAAM,CAAC,cAAc,EAAEO,KAAK,CAACM,YAAY,IAAI,EAAE,CAAC;MAEzD,IAAIrB,IAAI,EAAE;QACRiB,QAAQ,CAACT,MAAM,CAAC,MAAM,EAAER,IAAI,CAACsB,GAAG,CAAC,MAAM,CAAS,CAAC;MACnD;MACA,IAAIN,OAAO,EAAE;QACXC,QAAQ,CAACT,MAAM,CAAC,SAAS,EAAEQ,OAAO,CAACM,GAAG,CAAC,MAAM,CAAS,CAAC;MACzD;MAEA,OAAO,IAAI,CAACzB,WAAW,CAACI,OAAO,CAA4B;QACzDC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE,iDAAiD;QACtDC,IAAI,EAAEa;OACP,EACC;QAAEnB,OAAO,EAAE,IAAI,CAACA;MAAO,CAAE,CAAC;IAC9B,CAAC;EAlG+C;;;uBAHrCH,iBAAiB,EAAA8B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAjBjC,iBAAiB;MAAAkC,OAAA,EAAjBlC,iBAAiB,CAAAmC,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}