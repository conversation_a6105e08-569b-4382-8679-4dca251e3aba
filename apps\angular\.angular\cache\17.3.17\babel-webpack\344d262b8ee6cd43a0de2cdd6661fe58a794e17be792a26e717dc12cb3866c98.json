{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { tap } from 'rxjs';\nimport { BdoTableConstants } from './bdo-table.constants';\nimport { BdoTableCellLinkClickEvent, BdoTableCheckboxClickEvent, BdoTableColumnType, BdoTableLazyLoadEvent, BdoTableRowActionClickEvent, BdoTableRowClickEvent } from './bdo-table.model';\nimport { BdoTableRowDetailTemplateDirective } from './bdo-table-row-detail-template.directive';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { BdoTableFooterRowTemplateDirective } from './bdo-table-footer-row-template.directive';\nimport { AppComponentBase } from '../../../app-component-base';\nimport { StringHelper } from '@app/shared/utils';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/menu\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/paginator\";\nimport * as i8 from \"@angular/material/table\";\nimport * as i9 from \"@angular/material/sort\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/tooltip\";\nimport * as i12 from \"ngx-intl-tel-input\";\nconst _c0 = [\"bdotable\"];\nconst _c1 = () => [\"expandedDetail\"];\nconst _c2 = a0 => ({\n  row: a0\n});\nconst _c3 = () => ({});\nfunction BdoTableComponent_mat_header_cell_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 20);\n    i0.ɵɵtext(1, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n  }\n}\nfunction BdoTableComponent_mat_cell_5_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_mat_cell_5_button_1_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const element_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.expandedElement = ctx_r3.expandedElement === element_r3 ? null : element_r3;\n      ctx_r3.toggleExpandRow(element_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BdoTableComponent_mat_cell_5_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_mat_cell_5_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const element_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.expandedElement = ctx_r3.expandedElement === element_r3 ? null : element_r3;\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BdoTableComponent_mat_cell_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\");\n    i0.ɵɵtemplate(1, BdoTableComponent_mat_cell_5_button_1_Template, 1, 0, \"button\", 21)(2, BdoTableComponent_mat_cell_5_button_2_Template, 1, 0, \"button\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !element_r3.hideExpand && ctx_r3.expandedElement !== element_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !element_r3.hideExpand && ctx_r3.expandedElement === element_r3);\n  }\n}\nfunction BdoTableComponent_mat_footer_cell_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\");\n  }\n  if (rf & 2) {\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n  }\n}\nfunction BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template_mat_checkbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.isAllSelected, $event) || (ctx_r3.isAllSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template_mat_checkbox_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.selectAll($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.isAllSelected);\n  }\n}\nfunction BdoTableComponent_mat_header_cell_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\");\n    i0.ɵɵtemplate(1, BdoTableComponent_mat_header_cell_8_mat_checkbox_1_Template, 1, 1, \"mat-checkbox\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasCheckboxAll);\n  }\n}\nfunction BdoTableComponent_mat_cell_9_mat_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BdoTableComponent_mat_cell_9_mat_checkbox_1_Template_mat_checkbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const row_r8 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(row_r8.checked, $event) || (row_r8.checked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function BdoTableComponent_mat_cell_9_mat_checkbox_1_Template_mat_checkbox_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const row_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckRow(row_r8, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", row_r8.checked);\n  }\n}\nfunction BdoTableComponent_mat_cell_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\");\n    i0.ɵɵtemplate(1, BdoTableComponent_mat_cell_9_mat_checkbox_1_Template, 1, 1, \"mat-checkbox\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r8 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !row_r8.cannotCheck);\n  }\n}\nfunction BdoTableComponent_mat_footer_cell_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-cell\");\n  }\n  if (rf & 2) {\n    i0.ɵɵstyleProp(\"max-width\", \"60px\")(\"min-width\", \"60px\");\n  }\n}\nfunction BdoTableComponent_mat_cell_12_2_ng_template_0_Template(rf, ctx) {}\nfunction BdoTableComponent_mat_cell_12_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BdoTableComponent_mat_cell_12_2_ng_template_0_Template, 0, 0, \"ng-template\", 28);\n  }\n  if (rf & 2) {\n    const element_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.rowDetailTemplate.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c2, element_r9));\n  }\n}\nfunction BdoTableComponent_mat_cell_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"div\", 27);\n    i0.ɵɵtemplate(2, BdoTableComponent_mat_cell_12_2_Template, 1, 4, null, 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const element_r9 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"colspan\", ctx_r3.displayedColumns.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", element_r9 === ctx_r3.expandedElement ? \"bdo-table-row-expanded\" : \"bdo-table-row-collapsed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.rowDetailTemplate);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_header_cell_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 33);\n    i0.ɵɵelement(1, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵstyleProp(\"width\", col_r10.width ? col_r10.width + \"px\" : \"auto\")(\"min-width\", col_r10.minWidth ? col_r10.minWidth + \"px\" : \"100px\")(\"max-width\", col_r10.maxWidth ? col_r10.maxWidth + \"px\" : \"auto\");\n    i0.ɵɵproperty(\"mat-sort-header\", col_r10.columnId != \"actions\" && col_r10.isSortable ? col_r10.sortColumnId : null)(\"disabled\", col_r10.columnId === \"actions\" || !col_r10.isSortable ? true : false);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(col_r10.class);\n    i0.ɵɵproperty(\"innerHtml\", col_r10.columnName, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getStatusClass(row_r11, col_r10.columnId));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, ctx_r3.getCell(row_r11, col_r10.columnId).value));\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getCell(row_r11, col_r10.columnId).value, \" \");\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getCell(row_r11, col_r10.columnId).value ? i0.ɵɵpipeBind1(2, 1, \"::General:Yes\") : i0.ɵɵpipeBind1(3, 3, \"::General:No\"), \" \");\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 41);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 42);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_1_Template, 1, 0, \"span\", 39)(2, BdoTableComponent_ng_container_13_mat_cell_2_div_5_span_2_Template, 1, 0, \"span\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.getCell(row_r11, col_r10.columnId).value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getStatusClass(row_r11, col_r10.columnId));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(2, 2, (tmp_7_0 = ctx_r3.getCell(row_r11, col_r10.columnId)) == null ? null : tmp_7_0.value, \"dd/MM/yyyy\", \"local\"));\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getStatusClass(row_r11, col_r10.columnId));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(2, 2, (tmp_7_0 = ctx_r3.getCell(row_r11, col_r10.columnId)) == null ? null : tmp_7_0.value, \"dd/MM/yyyy HH:mm\", \"local\"));\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getCell(row_r11, col_r10.columnId).value, \" \");\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_9_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 45);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_9_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const row_r11 = i0.ɵɵnextContext(2).$implicit;\n      const col_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.linkClick(row_r11, col_r10.columnId, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext(2).$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r3.getCell(row_r11, col_r10.columnId).value, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext(2).$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_9_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_9_a_1_Template, 1, 1, \"a\", 44)(2, BdoTableComponent_ng_container_13_mat_cell_2_div_9_div_2_Template, 2, 1, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.getCell(row_r11, col_r10.columnId).isTextInsteadOfLink);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Link && ctx_r3.getCell(row_r11, col_r10.columnId).isTextInsteadOfLink);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 48);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const i_r16 = i0.ɵɵnextContext().index;\n      const row_r11 = i0.ɵɵnextContext(2).$implicit;\n      const col_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.linkClick(row_r11, col_r10.columnId, $event, i_r16));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const child_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(child_r17);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_a_1_Template, 2, 1, \"a\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext(2).$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.getCell(row_r11, col_r10.columnId).isTextInsteadOfLink);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_10_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_10_div_1_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const child_r18 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(child_r18);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_11_div_1_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"mat-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template_mat_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const row_r11 = i0.ɵɵnextContext().$implicit;\n      const col_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.getCell(row_r11, col_r10.columnId).value, $event) || (ctx_r3.getCell(row_r11, col_r10.columnId).value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template_mat_checkbox_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const row_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onCheckRow(row_r11, $event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 49);\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r3.getCell(row_r11, col_r10.columnId).value, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_14_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 51);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_14_a_1_Template_a_click_0_listener($event) {\n      const action_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const row_r11 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.actionClick(action_r22.actionType, row_r11.id, row_r11, $event));\n    });\n    i0.ɵɵelement(1, \"span\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", action_r22.tooltip);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(action_r22.icon);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_14_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_14_a_1_Template, 2, 4, \"a\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const row_r11 = i0.ɵɵnextContext().$implicit;\n      const col_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.actionClick(ctx_r3.getCell(row_r11, col_r10.columnId).value.actionType, row_r11.id, row_r11, null));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"matTooltip\", ctx_r3.getCell(row_r11, col_r10.columnId).value.tooltip);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getCell(row_r11, col_r10.columnId).value.icon);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const item_r26 = i0.ɵɵnextContext().$implicit;\n      const row_r11 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.actionClick(item_r26.actionType, row_r11.id, row_r11, null));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r26.displayName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r26.icon);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_button_1_Template, 4, 2, \"button\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r26 = ctx.$implicit;\n    const row_r11 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hideActionButton(item_r26, row_r11));\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_ng_container_13_mat_cell_2_div_16_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"button\", 54)(2, \"mat-icon\", 55);\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 1);\n    i0.ɵɵtemplate(6, BdoTableComponent_ng_container_13_mat_cell_2_div_16_div_6_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const menu_r27 = i0.ɵɵreference(5);\n    const row_r11 = i0.ɵɵnextContext().$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matMenuTriggerFor\", menu_r27);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_cell_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\");\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_cell_2_div_1_Template, 2, 2, \"div\", 35)(2, BdoTableComponent_ng_container_13_mat_cell_2_div_2_Template, 3, 3, \"div\", 17)(3, BdoTableComponent_ng_container_13_mat_cell_2_div_3_Template, 2, 1, \"div\", 17)(4, BdoTableComponent_ng_container_13_mat_cell_2_div_4_Template, 4, 5, \"div\", 17)(5, BdoTableComponent_ng_container_13_mat_cell_2_div_5_Template, 3, 2, \"div\", 17)(6, BdoTableComponent_ng_container_13_mat_cell_2_div_6_Template, 3, 6, \"div\", 35)(7, BdoTableComponent_ng_container_13_mat_cell_2_div_7_Template, 3, 6, \"div\", 35)(8, BdoTableComponent_ng_container_13_mat_cell_2_div_8_Template, 2, 1, \"div\", 17)(9, BdoTableComponent_ng_container_13_mat_cell_2_div_9_Template, 3, 2, \"div\", 36)(10, BdoTableComponent_ng_container_13_mat_cell_2_div_10_Template, 2, 1, \"div\", 36)(11, BdoTableComponent_ng_container_13_mat_cell_2_div_11_Template, 2, 1, \"div\", 17)(12, BdoTableComponent_ng_container_13_mat_cell_2_div_12_Template, 2, 1, \"div\", 36)(13, BdoTableComponent_ng_container_13_mat_cell_2_div_13_Template, 1, 1, \"div\", 37)(14, BdoTableComponent_ng_container_13_mat_cell_2_div_14_Template, 2, 1, \"div\", 36)(15, BdoTableComponent_ng_container_13_mat_cell_2_div_15_Template, 4, 2, \"div\", 36)(16, BdoTableComponent_ng_container_13_mat_cell_2_div_16_Template, 7, 2, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const row_r11 = ctx.$implicit;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap((tmp_5_0 = ctx_r3.getCell(row_r11, col_r10.columnId)) == null ? null : tmp_5_0.class);\n    i0.ɵɵstyleProp(\"width\", col_r10.width ? col_r10.width + \"px\" : \"auto\")(\"min-width\", col_r10.minWidth ? col_r10.minWidth + \"px\" : \"100px\")(\"max-width\", col_r10.maxWidth ? col_r10.maxWidth + \"px\" : \"auto\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.String);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Number);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Currency);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Boolean);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.YesNo);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Date);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.DateTime);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.FileSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.LinkArray);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Array);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Checkbox && !ctx_r3.getCell(row_r11, col_r10.columnId).hide);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Html);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.Actions && ctx_r3.getCell(row_r11, col_r10.columnId).value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.SingleActionButton && ctx_r3.getCell(row_r11, col_r10.columnId).value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r10.type === ctx_r3.BdoTableColumnType.ThreeDotActions && ctx_r3.getCell(row_r11, col_r10.columnId).value);\n  }\n}\nfunction BdoTableComponent_ng_container_13_mat_footer_cell_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-footer-cell\");\n    i0.ɵɵelement(1, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const col_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"width\", col_r10.width ? col_r10.width + \"px\" : \"auto\")(\"min-width\", col_r10.minWidth ? col_r10.minWidth + \"px\" : \"100px\")(\"max-width\", col_r10.maxWidth ? col_r10.maxWidth + \"px\" : \"auto\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", (tmp_7_0 = ctx_r3.getFooterCell(col_r10.columnId)) == null ? null : tmp_7_0.value, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BdoTableComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 29);\n    i0.ɵɵtemplate(1, BdoTableComponent_ng_container_13_mat_header_cell_1_Template, 2, 11, \"mat-header-cell\", 30)(2, BdoTableComponent_ng_container_13_mat_cell_2_Template, 17, 24, \"mat-cell\", 31)(3, BdoTableComponent_ng_container_13_mat_footer_cell_3_Template, 2, 7, \"mat-footer-cell\", 32);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"matColumnDef\", col_r10.columnId)(\"sticky\", col_r10.frozenLeft)(\"stickyEnd\", col_r10.frozenRight || col_r10.columnId === \"actions\");\n  }\n}\nfunction BdoTableComponent_mat_header_row_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nfunction BdoTableComponent_mat_row_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-row\", 58);\n    i0.ɵɵlistener(\"click\", function BdoTableComponent_mat_row_15_Template_mat_row_click_0_listener() {\n      const element_r29 = i0.ɵɵrestoreView(_r28).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.rowClick(element_r29));\n    })(\"click\", function BdoTableComponent_mat_row_15_Template_mat_row_click_0_listener() {\n      const element_r29 = i0.ɵɵrestoreView(_r28).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.expandedElement = ctx_r3.expandedElement === element_r29 ? null : element_r29);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r29 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(element_r29.class);\n    i0.ɵɵclassProp(\"bdo-table-row-selectable\", ctx_r3.rowSelectable)(\"bdo-table-row-selected\", ctx_r3.selectedRow === element_r29)(\"example-expanded-row\", ctx_r3.expandedElement === element_r29);\n  }\n}\nfunction BdoTableComponent_mat_row_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\", 59);\n  }\n}\nfunction BdoTableComponent_mat_footer_row_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-footer-row\");\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r3.footerCells && ctx_r3.footerCells.length > 0 ? \"flex\" : \"none\");\n  }\n}\nfunction BdoTableComponent_18_ng_template_0_Template(rf, ctx) {}\nfunction BdoTableComponent_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BdoTableComponent_18_ng_template_0_Template, 0, 0, \"ng-template\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.footerTemplate.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction0(2, _c3));\n  }\n}\nfunction BdoTableComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 60);\n  }\n}\nconst tablePaginationRowOption = [50, 100, 200];\nexport let BdoTableComponent = /*#__PURE__*/(() => {\n  class BdoTableComponent extends AppComponentBase {\n    constructor(injector, renderer, changeDetectorRef, permissionService) {\n      super(injector);\n      this.renderer = renderer;\n      this.changeDetectorRef = changeDetectorRef;\n      this.permissionService = permissionService;\n      this.defaultSortOrder = 'asc'; //asc, desc\n      this.pageSize = 25; //if 0, pagination is disabled\n      this.pageIndex = 0; //page number to display initially\n      this.selectedRow = null;\n      this.hasRowCheckbox = false; // indicates whether the rows have checkboxes\n      this.isVirtualScroll = false;\n      this.hidePagination = false;\n      this.scrollHeight = '70vh'; // define height of the scrollable body of table\n      this.headerHeight = 40; // height of column header row\n      this.footerHeight = 40; // height of footer row\n      this.isRowExpandable = false; // indicates if each row can be expanded to see more details\n      this.rowDetailHeight = 100; //height of row detail expanded section\n      this.pageSizeOptions = tablePaginationRowOption;\n      this.hideLastPage = false;\n      //event triggered when selecting a row on grid\n      this.onRowClick = new EventEmitter();\n      //event triggered when clicking on action icon on row\n      this.onActionClick = new EventEmitter();\n      //event triggered when clicking on link (anchor tag) on row cell\n      this.onLinkClick = new EventEmitter();\n      //event triggered instructing consumer component to initiate request to server to load data\n      //Used for server-side paging to work\n      this.onLazyLoad = new EventEmitter();\n      //event triggered when user clicks checkbox on grid row\n      this.onCheckboxClick = new EventEmitter();\n      //event triggered when paging the grid (user navigates to different page using pagination controls)\n      this.onPage = new EventEmitter();\n      // event triggered when a row is expanded\n      this.onRowExpand = new EventEmitter();\n      this.deleteAction = BdoTableConstants.ActionName.Delete;\n      //store copy of unfiltered data -> used for filter results for search filter\n      this.rows = [];\n      this.allRows = [];\n      this.cache = {};\n      //TODO - localize\n      this.totalMessage = 'total';\n      this.selectedMessage = null;\n      this.BdoTableColumnType = BdoTableColumnType;\n      this.displayedColumns = [];\n      this.sortField = '';\n      this.sortDirection = '';\n      this.expanded = {};\n      //avoids scroll event from firing multiple API calls to fetch same paged data\n      this.disableNextRowFetch = false;\n      this.footerCells = [];\n      this.isAllSelected = false;\n      this.totalMessage = \"Total\";\n    }\n    ngOnInit() {\n      this.sortField = this.defaultSortColumnId;\n      this.sortDirection = this.defaultSortOrder;\n      this.dataSubscription = this.tableService.data.subscribe(data => {\n        //update the correct table component if multiple instances of table on same page\n        if (data && data.tableId === this.id) {\n          this.footerCells = data.footerCells;\n          // set paginator page size\n          this.paginator.pageSize = this.pageSize;\n          this.sort.active = this.sortField;\n          this.sort.direction = this.sortDirection;\n          //need a delay to update columns on table if dynamically changed\n          setTimeout(() => {\n            this.displayedColumns = [];\n            if (this.hasRowCheckbox) {\n              this.displayedColumns.push('select');\n            }\n            if (this.isRowExpandable) {\n              this.displayedColumns.push('expand');\n            }\n            this.columns.forEach(x => {\n              this.displayedColumns.push(x.columnId);\n            });\n            this.changeDetectorRef.detectChanges();\n          }, 200);\n          //set height of table\n          if (this.id) {\n            const tableElement = document.getElementsByClassName(this.id);\n            if (tableElement && tableElement.length > 0 && tableElement[0].tagName === 'MAT-TABLE') {\n              this.renderer.setStyle(tableElement[0], 'max-height', this.scrollHeight);\n              this.renderer.setStyle(tableElement[0], 'overflow-y', 'auto');\n              //scroll to top whenever resetting to first page\n              if (data.resetToFirstPage) {\n                tableElement[0].scrollTop = 0;\n              }\n            }\n          }\n          this.changeDetectorRef.detectChanges();\n          //reset table to first page\n          if (data.resetToFirstPage) {\n            this.pageIndex = 0;\n            this.rows = [];\n            this.totalRecords = 0;\n            this.cache = {};\n          }\n          if (this.isVirtualScroll) {\n            // Update total count\n            this.totalRecords = data.totalRecords;\n            // Create array to store data if missing\n            // The array should have the correct number of with \"holes\" for missing data\n            if (!this.rows) {\n              this.rows = JSON.parse(JSON.stringify(data.data));\n            }\n            // Calc starting row offset\n            // This is the position to insert the new data\n            const start = this.pageIndex * this.pageSize;\n            // Copy existing data\n            const rows = [...this.rows];\n            // Insert new rows into correct position\n            if (data.data && data.data.length > 0) {\n              rows.splice(start, this.pageSize, ...JSON.parse(JSON.stringify(data.data)));\n            }\n            // Set rows to our new rows for display\n            this.rows = rows;\n            this.disableNextRowFetch = false;\n          } else {\n            this.allRows = JSON.parse(JSON.stringify(data.data));\n            if (this.lazyLoad) {\n              this.rows = this.allRows;\n            } else {\n              const start = this.pageIndex * this.pageSize;\n              var tempRows = [...this.allRows];\n              this.rows = tempRows.splice(start, this.pageSize);\n            }\n            this.totalRecords = data.totalRecords;\n          }\n          if (this.rowSelectable) {\n            if (this.table && this.rows[0]) {\n              //this.selectedRow = this.rows[0]; //auto selects first row in the table when rendered if rows are selectable \n              //this.rowClick(this.selectedRow);\n            }\n          }\n        }\n      });\n    }\n    ngAfterViewInit() {\n      if (this.paginator) {\n        this.paginator.pageSize = this.pageSize;\n      }\n      if (this.sort) {\n        this.sort.active = this.sortField;\n        this.sort.direction = this.sortDirection;\n      }\n      // when paginator event is invoked, retrieve the related data\n      this.pageSubscription = this.paginator.page.pipe(tap(() => {\n        this.pageIndex = this.paginator.pageIndex;\n        this.pageSize = this.paginator.pageSize;\n        if (this.lazyLoad) {\n          const lazyLoadEvent = new BdoTableLazyLoadEvent();\n          lazyLoadEvent.pageNumber = this.pageIndex;\n          lazyLoadEvent.pageSize = this.pageSize;\n          lazyLoadEvent.sortField = StringHelper.capitalizeFirstLetter(this.sortField);\n          lazyLoadEvent.isAscending = this.sortDirection === 'desc' ? false : true;\n          //triggers event instructing consumer component to initiate request to server to load data\n          //passes sorting and pagination info for consumer to include in requets object to load data from server\n          this.onLazyLoad.emit(lazyLoadEvent);\n        } else {\n          var tempRows = [...this.allRows];\n          var start = this.pageIndex * this.pageSize;\n          this.rows = tempRows.splice(start, this.pageSize);\n        }\n      })).subscribe();\n      this.sortSubscription = this.sort.sortChange.pipe(tap(() => {\n        this.sortField = this.sort.active;\n        this.sortDirection = this.sort.direction;\n        if (this.lazyLoad) {\n          this.pageIndex = 0;\n          const lazyLoadEvent = new BdoTableLazyLoadEvent();\n          lazyLoadEvent.pageNumber = this.pageIndex;\n          lazyLoadEvent.pageSize = this.pageSize;\n          lazyLoadEvent.sortField = StringHelper.capitalizeFirstLetter(this.sortField);\n          lazyLoadEvent.isAscending = this.sortDirection === 'desc' ? false : true;\n          //triggers event instructing consumer component to initiate request to server to load data\n          //passes sorting and pagination info for consumer to include in requets object to load data from server\n          this.onLazyLoad.emit(lazyLoadEvent);\n        } else {\n          const rows = [...this.rows];\n          rows.sort((a, b) => {\n            //TODO - handling sorting for dates, html, boolean, etc..\n            const fieldA = a.cells.find(x => x.columnId.toLowerCase() === this.sortField.toLowerCase())?.value;\n            const fieldB = b.cells.find(x => x.columnId.toLowerCase() === this.sortField.toLowerCase())?.value;\n            return fieldA.localeCompare(fieldB) * (this.sortDirection === 'desc' ? -1 : 1);\n          });\n          this.rows = rows;\n        }\n      })).subscribe();\n    }\n    ngAfterViewChecked() {\n      //set height of table\n      if (this.id) {\n        const tableElement = document.getElementsByClassName(this.id);\n        if (tableElement && tableElement.length > 0 && tableElement[0].tagName === 'MAT-TABLE') {\n          this.renderer.setStyle(tableElement[0], 'max-height', this.scrollHeight);\n          this.renderer.setStyle(tableElement[0], 'overflow-y', 'auto');\n        }\n      }\n      if (this.hideLastPage) {\n        var lastPageEl = document.getElementsByClassName('mat-mdc-paginator-navigation-last');\n        if (lastPageEl) {\n          var l = lastPageEl[0];\n          l.classList.add('hidden');\n        }\n      }\n    }\n    onTableScroll(e) {\n      //virtual scrolling is disabled\n      if (!this.isVirtualScroll) return;\n      //we have retrieved all records\n      if (this.rows.length === this.totalRecords || this.disableNextRowFetch) return;\n      const tableViewHeight = e.target.offsetHeight; // viewport: ~500px\n      const tableScrollHeight = e.target.scrollHeight; // length of all table\n      const scrollLocation = e.target.scrollTop; // how far user scrolled\n      // If the user has scrolled within 10px of the bottom, add more data\n      const buffer = 10;\n      const limit = tableScrollHeight - tableViewHeight - buffer;\n      if (scrollLocation > limit) {\n        this.disableNextRowFetch = true;\n        // We keep a index of server loaded pages so we don't load same data twice\n        // This is based on the server page not the UI\n        if (this.cache[this.pageIndex + 1]) return;\n        this.cache[this.pageIndex + 1] = true;\n        this.pageIndex = this.pageIndex + 1;\n        const lazyLoadEvent = new BdoTableLazyLoadEvent();\n        lazyLoadEvent.pageNumber = this.pageIndex;\n        lazyLoadEvent.pageSize = this.pageSize;\n        lazyLoadEvent.sortField = this.sortField;\n        lazyLoadEvent.isAscending = this.sortDirection === 'desc' ? false : true;\n        //triggers event instructing consumer component to initiate request to server to load data\n        //passes sorting and pagination info for consumer to include in requets object to load data from server\n        this.onLazyLoad.emit(lazyLoadEvent);\n      }\n    }\n    rowClick(row) {\n      //row is not selectable\n      if (!this.rowSelectable) return;\n      this.selectedRow = row;\n      const event = new BdoTableRowClickEvent();\n      event.id = row.id;\n      event.rowData = row;\n      this.onRowClick.emit(event);\n    }\n    linkClick(row, columnId, ev, i) {\n      const event = new BdoTableCellLinkClickEvent();\n      event.id = row.id;\n      event.rawData = row.rawData;\n      event.columnId = columnId;\n      event.event = ev;\n      event.arrayIndex = i;\n      this.onLinkClick.emit(event);\n    }\n    actionClick(action, id, row, ev) {\n      this.selectedRow = this.rows.find(x => {\n        return x.id === id;\n      });\n      if (this.selectedRow) {\n        this.rowClick(row);\n      }\n      const event = new BdoTableRowActionClickEvent();\n      event.action = action;\n      event.id = id;\n      event.data = row;\n      event.event = ev;\n      this.onActionClick.emit(event);\n    }\n    getCell(row, columnId) {\n      if (!row || !row.cells) return null;\n      const cell = row.cells.find(cell => {\n        return cell.columnId === columnId;\n      });\n      return cell;\n    }\n    getStatusClass(row, columnName) {\n      if (row.rawData?.isDeleted) {\n        return 'legend-pink legend-linethrough';\n      }\n      if (columnName === \"declarationStatus\" || columnName === 'assessmentStatus') {\n        if (row.rawData.assessmentStatus?.toLowerCase() == 'not started') return 'legend-black';\n        const status = row.rawData?.status ? row.rawData?.status.toLowerCase() : row.rawData.assessmentStatus?.toLowerCase();\n        switch (status) {\n          case 'not started':\n            return 'legend-pink';\n          case 'draft':\n          case 'information required':\n            return 'legend-navy';\n          case 'reopened':\n          case 'provisional fail':\n            return 'legend-orange';\n          case 'submitted':\n          case 'resubmitted':\n          case 'unsubmitted':\n            return 'legend-black';\n          case 'deleted':\n            return 'legend-pink legend-linethrough';\n          case 'pass':\n            return 'legend-green';\n          case 'fail':\n            return 'legend-red';\n          case 'provisional pass':\n            return 'legend-yellow';\n          case 'closed':\n            return 'legend-grey';\n          default:\n            return 'legend-black';\n        }\n      }\n      return '';\n    }\n    hideActionButton(buttonInfo, row) {\n      if (buttonInfo.source === \"declarationHistory\") {\n        const addEditSubmitPermission = this.permissionService.getGrantedPolicy('EsService.Declaration.Submit');\n        const viewPermission = this.permissionService.getGrantedPolicy('SearchService.BasicSearch.ViewDeclaration');\n        if (buttonInfo.actionType === 'delete') {\n          if (!(row.rawData.status === \"Draft\" || row.rawData.status === 'Reopened') || !addEditSubmitPermission) {\n            return false;\n          }\n        }\n        if (buttonInfo.actionType === 'edit') {\n          if (row.rawData.status === \"Submitted\" || row.rawData.status === 'Resubmitted' || !addEditSubmitPermission) {\n            return false;\n          }\n        }\n        if (buttonInfo.actionType === 'view') {\n          if (!viewPermission) {\n            return false;\n          }\n        }\n      }\n      return true;\n    }\n    getRowHeight(row) {\n      if (!row) {\n        return 40;\n      }\n      if (row.height === undefined) {\n        return 40;\n      }\n      return row.height;\n    }\n    getCellClass({\n      row,\n      column,\n      value\n    }) {\n      return row.cells.find(x => x.columnId === column.name)?.class;\n    }\n    getFooterCell(columnId) {\n      if (!this.footerCells) return null;\n      const cell = this.footerCells.find(cell => {\n        return cell.columnId === columnId;\n      });\n      return cell;\n    }\n    getRowClass(row) {\n      return row.class;\n    }\n    toggleExpandRow(row) {\n      setTimeout(() => {\n        this.onRowExpand.emit({\n          id: row.id,\n          rawData: row.rawData\n        });\n      }, 200);\n    }\n    selectAll(event) {\n      this.rows.forEach(r => {\n        r.checked = event.checked;\n      });\n      const checkBoxEvent = new BdoTableCheckboxClickEvent(event.checked ? this.rows : [], null, event.checked, true);\n      this.onCheckboxClick.emit(checkBoxEvent);\n    }\n    onCheckRow(row, event) {\n      const checkBoxEvent = new BdoTableCheckboxClickEvent([row], row.id, event.checked, false);\n      this.onCheckboxClick.emit(checkBoxEvent);\n    }\n    ngOnDestroy() {\n      if (this.dataSubscription) this.dataSubscription.unsubscribe();\n      if (this.pageSubscription) this.pageSubscription.unsubscribe();\n      if (this.sortSubscription) this.sortSubscription.unsubscribe();\n    }\n    static {\n      this.ɵfac = function BdoTableComponent_Factory(t) {\n        return new (t || BdoTableComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PermissionService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: BdoTableComponent,\n        selectors: [[\"bdo-table\"]],\n        contentQueries: function BdoTableComponent_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, BdoTableRowDetailTemplateDirective, 5);\n            i0.ɵɵcontentQuery(dirIndex, BdoTableFooterRowTemplateDirective, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowDetailTemplate = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerTemplate = _t.first);\n          }\n        },\n        viewQuery: function BdoTableComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.table = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        inputs: {\n          id: \"id\",\n          columns: \"columns\",\n          isSortable: \"isSortable\",\n          defaultSortColumnId: \"defaultSortColumnId\",\n          defaultSortOrder: \"defaultSortOrder\",\n          pageSize: \"pageSize\",\n          pageIndex: \"pageIndex\",\n          width: \"width\",\n          lazyLoad: \"lazyLoad\",\n          rowSelectable: \"rowSelectable\",\n          allowOnlySingleSelection: \"allowOnlySingleSelection\",\n          hasCheckboxAll: \"hasCheckboxAll\",\n          selectedRow: \"selectedRow\",\n          hasRowCheckbox: \"hasRowCheckbox\",\n          isVirtualScroll: \"isVirtualScroll\",\n          hidePagination: \"hidePagination\",\n          scrollHeight: \"scrollHeight\",\n          headerHeight: \"headerHeight\",\n          footerHeight: \"footerHeight\",\n          isRowExpandable: \"isRowExpandable\",\n          rowDetailHeight: \"rowDetailHeight\",\n          pageSizeOptions: \"pageSizeOptions\",\n          hideLastPage: \"hideLastPage\",\n          headerCheckboxValue: \"headerCheckboxValue\",\n          totalMessage: \"totalMessage\",\n          selectedMessage: \"selectedMessage\"\n        },\n        outputs: {\n          onRowClick: \"onRowClick\",\n          onActionClick: \"onActionClick\",\n          onLinkClick: \"onLinkClick\",\n          onLazyLoad: \"onLazyLoad\",\n          onCheckboxClick: \"onCheckboxClick\",\n          onPage: \"onPage\",\n          onRowExpand: \"onRowExpand\"\n        },\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 21,\n        vars: 25,\n        consts: [[\"bdotable\", \"\"], [\"menu\", \"matMenu\"], [1, \"bdo-table-container\", \"table-responsive\"], [\"matSort\", \"\", \"matSortDisableClear\", \"\", \"multiTemplateDataRows\", \"\", 1, \"row-hoverable\", 3, \"scroll\", \"dataSource\", \"matSortActive\", \"matSortDirection\"], [\"matColumnDef\", \"expand\", \"sticky\", \"\"], [\"aria-label\", \"row actions\", 3, \"max-width\", \"min-width\", 4, \"matHeaderCellDef\"], [3, \"max-width\", \"min-width\", 4, \"matCellDef\"], [3, \"max-width\", \"min-width\", 4, \"matFooterCellDef\"], [\"matColumnDef\", \"select\", 3, \"sticky\"], [3, \"max-width\", \"min-width\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"expandedDetail\"], [4, \"matCellDef\"], [3, \"matColumnDef\", \"sticky\", \"stickyEnd\", 4, \"ngFor\", \"ngForOf\"], [4, \"matHeaderRowDef\", \"matHeaderRowDefSticky\"], [\"class\", \"bdo-table-expanded-element-row\", 3, \"class\", \"bdo-table-row-selectable\", \"bdo-table-row-selected\", \"example-expanded-row\", \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"bdo-table-expanded-detail-row\", 4, \"matRowDef\", \"matRowDefColumns\"], [3, \"display\", 4, \"matFooterRowDef\", \"matFooterRowDefSticky\"], [4, \"ngIf\"], [\"class\", \"bdo-table-virtual-scroll-paginator-footer p-1 pb-0 ml-0 mr-0 grid\", 4, \"ngIf\"], [\"sticky\", \"\", 1, \"mat-paginator-sticky\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"disabled\", \"hidePageSize\", \"showFirstLastButtons\", \"pageIndex\"], [\"aria-label\", \"row actions\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"rm-icon rm-icon-angle-right\", \"class\", \"p-button-rounded p-button-text\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"rm-icon rm-icon-angle-down\", \"class\", \"p-button-rounded p-button-text\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"rm-icon rm-icon-angle-right\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"rm-icon rm-icon-angle-down\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"click\"], [\"class\", \"checkBox form-field\", 3, \"ngModel\", \"ngModelChange\", \"change\", 4, \"ngIf\"], [1, \"checkBox\", \"form-field\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"bdo-table-expanded-element-detail\", 3, \"ngClass\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"matColumnDef\", \"sticky\", \"stickyEnd\"], [3, \"mat-sort-header\", \"disabled\", \"width\", \"min-width\", \"max-width\", 4, \"matHeaderCellDef\"], [3, \"class\", \"width\", \"min-width\", \"max-width\", 4, \"matCellDef\"], [3, \"width\", \"min-width\", \"max-width\", 4, \"matFooterCellDef\"], [3, \"mat-sort-header\", \"disabled\"], [1, \"multi-line\", 3, \"innerHtml\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"click\", 4, \"ngIf\"], [3, \"innerHTML\", 4, \"ngIf\"], [3, \"ngClass\"], [\"class\", \"cross-icon rm-icon rm-icon-close\", 4, \"ngIf\"], [\"class\", \"tick-icon rm-icon rm-icon-check\", 4, \"ngIf\"], [1, \"cross-icon\", \"rm-icon\", \"rm-icon-close\"], [1, \"tick-icon\", \"rm-icon\", \"rm-icon-check\"], [3, \"click\"], [\"class\", \"bdo-table-cell-link\", \"target\", \"_blank\", 3, \"innerHTML\", \"click\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"bdo-table-cell-link\", 3, \"click\", \"innerHTML\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"bdo-table-cell-link\", \"target\", \"_blank\", 3, \"click\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"bdo-table-cell-link\", 3, \"click\"], [3, \"innerHTML\"], [\"class\", \"bdo-table-cell-action-link mr-2\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"bdo-table-cell-action-link\", \"mr-2\", 3, \"click\", \"title\"], [\"mat-raised-button\", \"\", 1, \"bdo-table-single-action-button\", 3, \"click\", \"matTooltip\"], [1, \"bdo-table-single-action-button-icon\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [1, \"rm-icon\", \"rm-icon-more-horiz\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"bdo-table-expanded-element-row\", 3, \"click\"], [1, \"bdo-table-expanded-detail-row\"], [1, \"bdo-table-virtual-scroll-paginator-footer\", \"p-1\", \"pb-0\", \"ml-0\", \"mr-0\", \"grid\"]],\n        template: function BdoTableComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"mat-table\", 3, 0);\n            i0.ɵɵlistener(\"scroll\", function BdoTableComponent_Template_mat_table_scroll_1_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onTableScroll($event));\n            });\n            i0.ɵɵelementContainerStart(3, 4);\n            i0.ɵɵtemplate(4, BdoTableComponent_mat_header_cell_4_Template, 2, 4, \"mat-header-cell\", 5)(5, BdoTableComponent_mat_cell_5_Template, 3, 6, \"mat-cell\", 6)(6, BdoTableComponent_mat_footer_cell_6_Template, 1, 4, \"mat-footer-cell\", 7);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(7, 8);\n            i0.ɵɵtemplate(8, BdoTableComponent_mat_header_cell_8_Template, 2, 5, \"mat-header-cell\", 9)(9, BdoTableComponent_mat_cell_9_Template, 2, 5, \"mat-cell\", 6)(10, BdoTableComponent_mat_footer_cell_10_Template, 1, 4, \"mat-footer-cell\", 7);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(11, 10);\n            i0.ɵɵtemplate(12, BdoTableComponent_mat_cell_12_Template, 3, 3, \"mat-cell\", 11);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵtemplate(13, BdoTableComponent_ng_container_13_Template, 4, 3, \"ng-container\", 12)(14, BdoTableComponent_mat_header_row_14_Template, 1, 0, \"mat-header-row\", 13)(15, BdoTableComponent_mat_row_15_Template, 1, 8, \"mat-row\", 14)(16, BdoTableComponent_mat_row_16_Template, 1, 0, \"mat-row\", 15)(17, BdoTableComponent_mat_footer_row_17_Template, 1, 2, \"mat-footer-row\", 16)(18, BdoTableComponent_18_Template, 1, 3, null, 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(19, BdoTableComponent_div_19_Template, 1, 0, \"div\", 18);\n            i0.ɵɵelement(20, \"mat-paginator\", 19);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵclassMap(ctx.id);\n            i0.ɵɵproperty(\"dataSource\", ctx.rows)(\"matSortActive\", ctx.defaultSortColumnId)(\"matSortDirection\", ctx.defaultSortOrder === \"desc\" ? \"desc\" : \"asc\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"sticky\", true);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngForOf\", ctx.columns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns)(\"matHeaderRowDefSticky\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", i0.ɵɵpureFunction0(24, _c1));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matFooterRowDef\", ctx.displayedColumns)(\"matFooterRowDefSticky\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.footerTemplate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isVirtualScroll);\n            i0.ɵɵadvance();\n            i0.ɵɵstyleProp(\"display\", ctx.footerHeight === 0 || ctx.hidePagination || ctx.isVirtualScroll ? \"none\" : \"block\");\n            i0.ɵɵproperty(\"length\", ctx.totalRecords)(\"pageSize\", ctx.pageSize)(\"pageSizeOptions\", ctx.pageSizeOptions)(\"disabled\", ctx.isVirtualScroll || ctx.hidePagination)(\"hidePageSize\", ctx.isVirtualScroll || ctx.hidePagination)(\"showFirstLastButtons\", !ctx.hidePagination)(\"pageIndex\", ctx.pageIndex);\n          }\n        },\n        dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i3.NgControlStatus, i4.MatMenu, i4.MatMenuItem, i4.MatMenuTrigger, i5.MatIcon, i6.MatButton, i6.MatIconButton, i7.MatPaginator, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatFooterCellDef, i8.MatFooterRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatFooterCell, i8.MatHeaderRow, i8.MatRow, i8.MatFooterRow, i9.MatSort, i9.MatSortHeader, i10.MatCheckbox, i11.MatTooltip, i3.NgModel, i12.NativeElementInjectorDirective, i2.DecimalPipe, i2.DatePipe, i1.LocalizationPipe],\n        styles: [\".bdo-table-cell{overflow:hidden;word-wrap:break-word}.bdo-table-cell-action-link,.bdo-table-cell-link{cursor:pointer}.bdo-table-checkbox-toggles{padding-top:2px}.mdc-data-table__table .required-label:after,.mat-table .required-label:after{font-size:1em!important}table{width:100%}.table-responsive{display:block;width:100%;overflow-x:auto}.bdo-table-expanded-detail-row{min-height:0}.bdo-table-row-expanded{width:100%;height:auto}.bdo-table-row-collapsed{height:0;min-height:0}.mat-mdc-cell.mat-column-expandedDetail{background-color:#f2f2f2}mat-cell div,mat-cell p{word-break:break-word;white-space:break-spaces}.bdo-table-virtual-scroll-paginator-footer{font-size:13px}.mat-mdc-cell .mat-icon-button{width:30px!important;height:30px!important;line-height:30px!important}.mat-mdc-cell,.row-hoverable .mat-mdc-row:not(.bdo-table-expanded-detail-row):hover .mat-mdc-cell{background-color:#7fbeb0}.bdo-table-row-selected{background-color:#f0ecec!important}.mat-mdc-header-cell div.multi-line{display:block;font-weight:700!important}.bdo-table-row-selectable.mat-mdc-row .mat-mdc-cell{cursor:pointer}mat-row:nth-child(4n){background-color:#eaf0f3}.bdo-table-single-action-button{background-color:#000!important;color:#fff!important}.bdo-table-single-action-button-icon{top:18%;left:27%;margin-top:-.5em;margin-left:-.5em!important;width:1em!important;height:1em!important}.mat-paginator-sticky .mat-mdc-paginator-navigation-last.hidden{display:none}\\n\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return BdoTableComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}