﻿using Bdo.Ess.CtsIntegration.CtsPackageRequests;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace Bdo.Ess.CtsIntegration.PackageGeneration
{
    public interface ICtsPackageGenerationAppService : IApplicationService
    {
        Task<string> GeneratePackageAsync(Guid packageRequestId,Guid tenantId);
        Task<string> GeneratePackageAsync(CtsPackageRequestDataDto packageRequest);

       Task<byte[]> UnpackAsync(byte[] zipContent);

        Task<byte[]> UnpackAsync(IFormFile file);

        Task<string> MigrateMetaCountryCode();
    }
}
