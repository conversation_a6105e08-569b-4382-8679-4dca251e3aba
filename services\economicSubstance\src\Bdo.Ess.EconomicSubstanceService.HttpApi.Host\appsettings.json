{
  "App": {
    "SelfUrl": "https://localhost:45186",
    "CorsOrigins": "https://localhost:44325,https://localhost:44353"
  },
  "AuthServer": {
    "Authority": "https://localhost:44322",
    "RequireHttpsMetadata": "true",
    "SwaggerClientId": "WebGateway_Swagger"
  },
  "RemoteServices": {
    "LookupService": {
      "BaseUrl": "https://localhost:44502/",
      "UseCurrentAccessToken": "false"
    },
    "CorporateEntityService": {
      "BaseUrl": "https://localhost:44450/",
      "UseCurrentAccessToken": "false"
    },
    "IdentityService": {
      "BaseUrl": "https://localhost:44388/",
      "UseCurrentAccessToken": "false"
    },
    "AbpIdentity": {
      "BaseUrl": "https://localhost:44388/",
      "UseCurrentAccessToken": "false"
    },
    "SearchService": {
      "BaseUrl": "https://localhost:45037/",
      "UseCurrentAccessToken": "false"
    },
    "SaasService": {
      "BaseUrl": "https://localhost:44381/",
      "UseCurrentAccessToken": "false"
    }
  },
  "IdentityClients": {
    "Default": {
      "GrantType": "client_credentials",
      "ClientId": "EconomicSubstanceService",
      "ClientSecret": "1q2w3e*",
      "Authority": "https://localhost:44322",
      "Scope": "LookupService CorporateEntityService IdentityService SearchService SaasService"
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "EconomicSubstanceService": "***********************************************************",
    "AdministrationService": "Server=localhost,1434;Database=Ess_Administration;User Id=sa;password=**********;MultipleActiveResultSets=true;TrustServerCertificate=True",
    "SaasService": "Server=localhost,1434;Database=Ess_Saas;User Id=sa;password=**********;MultipleActiveResultSets=true;TrustServerCertificate=True",
    "IdentityService": "Server=localhost,1434;Database=Ess_Identity;User Id=sa;password=**********;MultipleActiveResultSets=true;TrustServerCertificate=True"
  },
  "StringEncryption": {
    "DefaultPassPhrase": "FjItyg0mRv8XcFQ7"
    //"DefaultSalt": "R7!j98v#L3@c$D2x"
  },
  "Redis": {
    "Configuration": "localhost:6379"
  },
  "RabbitMQ": {
    "Connections": {
      "Default": {
        "HostName": "localhost",
        "UserName": "guest",
        "Password": "guest",
        "Port": 5672
      }
    },
    "EventBus": {
      "ClientName": "Ess_EconomicSubstanceService",
      "ExchangeName": "Ess"
    }
  },
  "ElasticSearch": {
    "Url": "http://localhost:9200"
  },
  "Azure": {
    "StorageDeclaration": {
      "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=essangulardev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
      "ContainerName": "Declarations"
    },
    "StorageAssessment": {
      "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=essangulardev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
      "ContainerName": "Assessments"
    },
    "StorageESLetter": {
      "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=essangulardev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
      "ContainerName": "ESLetter"
    },
    "StorageESLetterTemplate": {
      "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=essangulardev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
      "ContainerName": "ESLetterTemplate"
    },
    "StorageCtsIntegration": {
      "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=essangulardev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
    }
    
  },
  "ESLetterTemplatePath": "ComplianceLetterForm.pdf",
  "CASearchIgnoreIds": [
    //{
    //  "TenantId": "********-7AF5-CD35-C421-3A0DE3779B38",
    //  "TenantName": "RA1"
    //}
  ],
  "InfoExchange": {
    "StandardMonitoringFromYear": "2023"
  },
  "AngularURL": "http://localhost:4200/",
  "ProcessRedFlagsOnAllDeclarations": "false",
  "MaintenanceKey": "jJ36Lf4jjXU8ohSesOiSPmXOef6fgi5luia+fr0dChf1WltsWRI4Hi1OMtrHM0N4wD2tZeLr4YoiNr7bjPrA==",
  "DataProtection": {
    "Enabled": "true",
    "KeyVaultName": "ess-dp-kv-dev",
    "ManagedIdentityClientId": "ccd41834-acc7-4a1a-ac52-8c4a9f84c1eb"
  },
  "KeyVaultName": "ess-zkv-dev",
  "ManagedIdentityClientId": "ccd41834-acc7-4a1a-ac52-8c4a9f84c1eb"
  //"AngularURL": "https://{{tenantName}}.ess-portal.ess.bs/"
}
