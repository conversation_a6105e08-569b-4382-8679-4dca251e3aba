﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Bdo.Ess.SearchService.DbMigrations;
using Bdo.Ess.SearchService.EntityFrameworkCore;
using Bdo.Ess.Shared.Hosting.Microservices;
using Bdo.Ess.Shared.Hosting.AspNetCore;
using Prometheus;
using Volo.Abp;
using Volo.Abp.Modularity;
using Elastic.Transport;
using Bdo.Ess.Shared.HttpApi.Audit;
using Bdo.Ess.Shared.HttpApi;
using Volo.Abp.Http.Client.IdentityModel;
using Volo.Abp.Security.Claims;
using Volo.Abp.Http.Client;

namespace Bdo.Ess.SearchService;

[DependsOn(
    typeof(EssSharedHostingMicroservicesModule),
    typeof(SearchServiceApplicationModule),
    typeof(SearchServiceHttpApiModule),
    typeof(SearchServiceEntityFrameworkCoreModule),
    typeof(ESSSharedHttpApiModule),
    typeof(AbpHttpClientIdentityModelModule)
    )]
public class SearchServiceHttpApiHostModule : AbpModule
{

	public override void PreConfigureServices(ServiceConfigurationContext context)
	{
		PreConfigure<AbpHttpClientBuilderOptions>(options =>
		{
			options.ProxyClientActions.Add((remoteServiceName, clientBuilder, client) =>
			{
				client.Timeout = TimeSpan.FromMinutes(55);
			});
		});
	}

	public override void ConfigureServices(ServiceConfigurationContext context)
    {
        //You can disable this setting in production to avoid any potential security risks.
        Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
        
        // Enable if you need these
        // var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();
                
        JwtBearerConfigurationHelper.Configure(context, "SearchService");
        /* Version 7
        SwaggerConfigurationHelper.ConfigureWithAuth(
            context: context,
            authority: configuration["AuthServer:Authority"],
            scopes: new
                Dictionary<string, string> 
                {
                    {"SearchService", "SearchService API"}
                },
            apiTitle: "SearchService API"
        );
        */
        SwaggerConfigurationHelper.ConfigureWithOidc(
            context: context,
            authority: configuration["AuthServer:Authority"]!,
            scopes: new[] { "SearchService" },
            flows: new[] { "authorization_code" },
            discoveryEndpoint: configuration["AuthServer:MetadataAddress"],
            apiTitle: "SearchService API"
        );
        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = false;
        });
        context.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(
                        configuration["App:CorsOrigins"]?
                            .Split(",", StringSplitOptions.RemoveEmptyEntries)
                            .Select(o => o.Trim().RemovePostFix("/"))
                            .ToArray()
                    )
                    .WithAbpExposedHeaders()
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });
        ConfigureElasticSearch(context);
	}

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseCorrelationId();
        app.UseAbpRequestLocalization();
        app.UseStaticFiles();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        app.UseCors();
        app.UseAuthentication();
        app.UseAbpClaimsMap();
        app.UseMultiTenancy();
        app.UseUnitOfWork();
        //app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "SearchService API");
            options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
        });
        app.UseAbpSerilogEnrichers();
        app.UseAuditing();
        app.UseConfiguredEndpoints(endpoints => endpoints.MapMetrics());
    }

    public async override Task OnPostApplicationInitializationAsync(ApplicationInitializationContext context)
    {
        using (var scope = context.ServiceProvider.CreateScope())
        {
            await scope.ServiceProvider
                .GetRequiredService<SearchServiceDatabaseMigrationChecker>()
                .CheckAndApplyDatabaseMigrationsAsync();
        }
    }

    public void ConfigureElasticSearch(ServiceConfigurationContext context)
    {
        var configuration = context.Services.GetConfiguration();
        context.Services.AddOptions<ElasticSearchConnectionOptions>().Configure(options =>
        {
            options.ElasticSearchUrl = configuration["ElasticSearch:Url"];
            options.CertificateThumbprint = configuration["ElasticSearch:Thumbprint"];
            options.UserName = configuration["ElasticSearch:Username"];
            options.Password = configuration["ElasticSearch:Password"];
            options.RAIndexName = configuration["ElasticSearch:RAIndex"];
            options.CAIndexName = configuration["ElasticSearch:CAIndex"];
            options.CASearchIgnoreIds = configuration.GetSection("CASearchIgnoreIds").Get<List<CAIgnoreTenant>>();
        });
    }
}
