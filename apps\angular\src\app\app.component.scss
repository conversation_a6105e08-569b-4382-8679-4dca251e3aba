/***********************************************************************************/
// Root styling.
/***********************************************************************************/
html,
body {
    height: 100%;
    min-height: 100%;
}

body {
    padding: 0;
    margin: 0;

    /* font styling outside material framework */
    font-family: 'Trebuchet MS', 'Source Sans Pro', 'sans-serif';

    * {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

.header,
.footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.bdo-header {
    display: block;
    width: 100%;
}


.footer {
    margin-top: 1em;

    .footer-content {
        font-size: 12px;
        font-weight: normal;

        a {
            text-decoration: none;
        }
    }
}

a {
    text-decoration: none;
}

.dev-hint {
    font-size: 0.8em;
    color: #008000 !important;
}

.swal2-styled {
    margin: 0.3125em !important;
}

.colored-toast.swal2-icon-success {
    background-color: #a5dc86 !important;
}

.colored-toast.swal2-icon-error {
    background-color: #f27474 !important;
}

.colored-toast.swal2-icon-warning {
    background-color: #f8bb86 !important;
}

.colored-toast.swal2-icon-info {
    background-color: #3fc3ee !important;
}

.colored-toast.swal2-icon-question {
    background-color: #87adbd !important;
}

.colored-toast .swal2-title {
    color: #fffefe;
}

.colored-toast .swal2-close {
    color: #fffefe;
}

.colored-toast .swal2-html-container {
    color: #fffefe;

}

.font-dialog-title {
    font-size: 1.25em;
}

.font-card-title {
    font-size: 1.875em !important;
}

.button-margin {
    margin: 0.625em;
}

.button-right {
    float: right;
}

.margin-10 {
    margin: 0.625em;
}

.margin-l-5 {
    margin-left: 0.3125em;
}

.display-flex-space {
    display: flex;
    justify-content: space-between;
}

.fill-extra-space {
    flex: 1 1 auto;
}

.ui-button {
    background-color: #000000 !important;
    color: #fff !important;
}


.ui-button[disabled] {
    background-color: #77C8ED !important;
    color: #fff !important;
}


.ui-button-red {
    background-color: red !important;
    color: #fff !important;
}

.legend-pink {
    color: white;
    background: lightcoral;
    padding: 3px 10px;
    border-radius: 1em;
    display: inline-block;
    max-width: 90%;
}

.legend-navy {
    color: white;
    background: navy;
    padding: 3px 10px;
    border-radius: 1em;
    display: inline-block;
    max-width: 90%;
}

.legend-linethrough {
    text-decoration: line-through;
}


.legend-orange {
    color: white;
    background: orange;
    padding: 3px 10px;
    border-radius: 1em;
    display: inline-block;
    max-width: 90%;
}

.legend-black {
    color: white;
    background: black;
    padding: 3px 10px;
    border-radius: 1em;
    display: inline-block;
    max-width: 90%;
}

.legend-green {
    color: white;
    background: green;
    padding: 3px 10px;
    border-radius: 1em;
    display: inline-block;
    max-width: 90%;
}

.legend-red {
    color: white;
    background: red;
    padding: 3px 10px;
    border-radius: 1em;
    display: inline-block;
    max-width: 90%;
}

.legend-yellow {
    color: black;
    background: yellow;
    padding: 3px 10px;
    border-radius: 1em;
    display: inline-block;
    max-width: 90%;
}

.legend-grey {
    color: white;
    background: grey;
    padding: 3px 10px;
    border-radius: 1em;
    display: inline-block;
    max-width: 90%;
}

.form-field {
    width: 30em;
    margin: 0 auto;
    display: block;
}

.form-field-reduce-length {
    width: 20em;
    height: 60px;
}


.form {
    display: block;
}

.card-class {
    text-align: center;
    max-width: 60em;
    margin: 0 auto;
}

.checkBox {
    margin-right: 5em;
}

mat-row:nth-child(odd) {
    background-color: #eaf0f3;
}

mat-row:nth-child(even) {
    background-color: #ffffff;
}

.title-color-font {
    color: #00779b !important;
    font-size: 2em !important;
}

.confirmation .confirmation-dialog .footer {
    flex-direction: row;
}

.mat-button {
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon {
    top: 18%;
    left: 27%;
    //margin-top: -.5em;
    margin-left: -.5em !important;
    width: 1em !important;
    height: 1em !important;
}

.confirmation .confirmation-dialog .icon-container .icon {
    margin-top: 0em;
    margin-left: 0em !important;
    width: 100px !important;
    height: 100px !important;
}

.line {
    height: 1px !important;
    background-color: #929292 !important;
    margin-top: 5em !important;
    z-index: 999;
    position: relative;
}

.info-exchange-header {
    text-align: left !important;
}

.cts-data-packet-dashboard-header {    
    justify-content: center !important;
}

.cts-data-packet-dashboard-value {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;

    mat-checkbox {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 0 auto !important;

        .mat-checkbox-layout {
            justify-content: center !important;
        }

        .mat-checkbox-label {
            text-align: center !important;
        }
    }
}

//Survey JS modifications
.sv-dropdown_select-wrapper {
    text-wrap: wrap;
}