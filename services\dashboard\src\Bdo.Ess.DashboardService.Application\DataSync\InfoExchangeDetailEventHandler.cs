﻿using Bdo.Ess.Shared.Hosting.Microservices.Eto.InformationExchange;
using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.DistributedLocking;
using Volo.Abp.EventBus.Distributed;

namespace Bdo.Ess.DashboardService.DataSync
{
    //This event handler is for information detail status update sync
    public class InfoExchangeDetailEventHandler : ITransientDependency
        , IDistributedEventHandler<InformationExchangeDetailsListEto>
    {
        private readonly IInfoExchangeDetailSyncAppService _detailSyncAppService;
        private readonly IJobLogAppService _jobLogAppService;
        private readonly IAbpDistributedLock _distributedLock;

        private static readonly TimeSpan BatchLockWait = TimeSpan.FromMinutes(4);

        public InfoExchangeDetailEventHandler(
            IInfoExchangeDetailSyncAppService detailSyncAppService
            , IJobLogAppService jobLogAppService
            , IAbpDistributedLock distributedLock
            )
        {
            _detailSyncAppService = detailSyncAppService;
            _jobLogAppService = jobLogAppService;
            _distributedLock = distributedLock;
        }

        [Obsolete("This method will be deprecated, Directly update status instead.")]
        public async Task HandleEventAsync(InformationExchangeDetailsListEto eventData)
        {
            try
            {
                await using var handle = await _distributedLock.TryAcquireAsync("SyncInfoExchangeStatus", BatchLockWait);
                if (handle != null)
                {
                    await _jobLogAppService.WriteSingleLogAsync("Info Exchange Status Update Received", null, $"Count: {eventData.DetailsList?.Count}");
                    await _detailSyncAppService.UpdateInformationExchangeStatus(eventData);
                }
                else
                {
                    await _jobLogAppService.WriteBatchLogAsync("Aquire Lock Failed", 0, eventData.DetailsList?.Count, "InformationExchanges Status");
                }
            }
            catch (Exception ex)
            {
                await _jobLogAppService.WriteSingleLogAsync("Info Exchange Status Update Received Exception", null, "Error", ex.Message);
            }
        }
    }
}