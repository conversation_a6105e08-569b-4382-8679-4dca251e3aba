<mat-tab-group (selectedTabChange)="tabChanged($event)" dynamicHeight>
  <mat-tab label="Info Exchange Readiness">
    <div class="top-action-row-exchange row">
      <div class="table-container">
        <bdo-table [id]="TableIdS" [columns]="summaryExchangeColumns" scrollHeight="100%"
          defaultSortColumnId="uploadedDateTime" [defaultSortOrder]="'desc'" [pageIndex]="currentPageIndexS"
          [pageSize]="PageSizeS" [isVirtualScroll]="false" [hidePagination]="true" [rowSelectable]="true"
          [lazyLoad]="true" (onLazyLoad)="onLazyLoadEventS($event)" [pageSizeOptions]="[10, 20, 50, 100]">
        </bdo-table>
      </div>
    </div>
    <div class="top-action-column-exchange row justify-content-end">
      <div class="col-md-auto">
        <span class="certificate-text" *ngIf="certificateExpirationDate">CA Certificate expires at {{
          certificateExpirationDate | date:'dd/MM/yyyy'}}</span>
        <button *ngIf="showBahamasSettings" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="openUpdateCtsSettingDialog()">
          Update CTS Settings
        </button>
        <button *ngIf="showUpdateCACertificate" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="openUpdateCaCertificateDialog()">
          Update CA Certificate
        </button>
        <button *ngIf="showButton" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="GenerateXMlByType(0)">
          Non-compliance XML
        </button>
        <button *ngIf="showButton" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="GenerateXMlByType(1)">
          High Risk IP XML
        </button>
        <button *ngIf="showButton" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="GenerateXMlByType(2)">
          Non-resident XML
        </button>
        <button *ngIf="showButton && showOtherCase" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="GenerateXMlByType(3)">
          Other Cases XML
        </button>
      </div>
    </div>
    <div class="top-action-column-exchange row">
      <div class="col-md-4 col-sm-12 margin-top">
        <mat-label class="outside-mat-label">Financial Period End</mat-label>
        <mat-form-field class="form-field-reduce-length">
          <mat-select placeholder="Financial Period End" [(value)]="selectedYear"
            (selectionChange)="onYearChange($event)">
            <mat-option *ngFor="let element of year" [value]="element">
              {{ element }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="col-md-3 col-sm-12 margin-top">
        <mat-label class="outside-mat-label">Report Status</mat-label>
        <mat-form-field class="form-field-reduce-length">
          <mat-select placeholder="Report Status" [(value)]="selectReportStatus"
            (selectionChange)="onReportChange($event)">
            <mat-option *ngFor="let element of informationExchangedDic" [value]="element.value">
              {{ element.description }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="col-md-3 col-sm-12 margin-top">
        <mat-label class="outside-mat-label">Entity Name</mat-label>
        <mat-form-field class="form-field-reduce-length">
          <input matInput placeholder="File Name" [(ngModel)]="searchEntityName" />
        </mat-form-field>
      </div>
      <div class="col-md-2 col-sm-12 margin-top search-button-column">
        <button type="button" mat-raised-button (click)="onSearch()" class="ui-button search-button">
          Search
        </button>
      </div>
    </div>
    <div class="top-action-row-exchange row">
      <div class="table-container">
        <bdo-table [id]="TableId" scrollHeight="36vh" [columns]="exchangeResultColumns"
          defaultSortColumnId="ExchangeReason" [defaultSortOrder]="'desc'" [pageIndex]="currentPageIndex"
          [pageSize]="PageSize" [pageSizeOptions]="[10, 20, 50, 100]" [isVirtualScroll]="false" [hidePagination]="false"
          [rowSelectable]="true" [lazyLoad]="true" (onLazyLoad)="onLazyLoadEvent($event)"
          (onLinkClick)="onLinkClick($event)">
        </bdo-table>
      </div>
    </div>
  </mat-tab>

  <mat-tab *ngIf="showDataPacketDashboard" label="CTS Upload & Transmission">
    <!-- 1. Dashboard Section -->
    <div class="row top-action-row-exchange">
      <div class="table-container">
        <bdo-table [id]="ctsDashboardTableId" [columns]="ctsDashboardColumns" scrollHeight="auto"
          [defaultSortOrder]="'desc'" [pageIndex]="currentPageIndexS" [pageSize]="PageSizeS" [isVirtualScroll]="false"
          [hidePagination]="true" [rowSelectable]="false" [lazyLoad]="false">
        </bdo-table>
      </div>
    </div>

    <!-- 2. Button List Section -->
    <div class="top-action-column-exchange row justify-content-end">
      <div class="col-md-auto">
        <button *ngIf="showDecryptReceivedDataPacket" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="openDecryptDialog()"><mat-icon>lock_open</mat-icon> Decrypt Received Data Packet</button>
        <button *ngIf="showUploadHistoricalXml" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="openUploadHistoricalXmlDialog()"><mat-icon>cloud_upload</mat-icon> Upload Historical XML</button>
        <button *ngIf="showCTSUpload" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="openCtsUploadDialog()"><mat-icon>cloud_upload</mat-icon> CTS Upload</button>
        <button *ngIf="showRefreshStatus" type="button" mat-raised-button class="ui-button margin-l-5"
          (click)="refreshAllTransmissionStatus()"><mat-icon>refresh</mat-icon> Refresh CTS Transmission Status</button>
      </div>
    </div>

    <!-- 3. Filters and Search Section -->
    <div class="top-action-column-exchange row">
      <div class="col-md-3 col-sm-12 margin-top">
        <mat-label class="outside-label outside-mat-label">Financial Period End</mat-label>
        <mat-form-field class="form-field-reduce-length">
          <mat-select placeholder="Financial Period End" [(value)]="ctsUploadSelectedYear"
            (selectionChange)="onCtsUploadYearChange($event)">
            <mat-option *ngFor="let element of year" [value]="element">
              {{ element }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="col-md-3 col-sm-12 margin-top">
        <mat-label class="outside-label outside-mat-label">Exchange Reason</mat-label>
        <mat-form-field class="form-field-reduce-length">
          <mat-select placeholder="Exchange Reason" [(value)]="selectExchangeReason"
            (selectionChange)="onCtsUploadExchangeReasonChange($event)">
            <mat-option *ngFor="let element of ctsUploadExchangeReasonDic" [value]="element.value">
              {{ element.description }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="col-md-3 col-sm-12 margin-top">
        <mat-label class="outside-label outside-mat-label">Receiving Country</mat-label>
        <mat-form-field class="form-field-reduce-length">
          <mat-select placeholder="Receiving Country" [(value)]="selectReceivingCountry"
            (selectionChange)="onCtsUploadReceivingCountryChange($event)">
            <mat-option *ngFor="let element of countries" [value]="element.code2">
              {{element.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="col-md-3 col-sm-12 margin-top">
        <mat-label class="outside-label outside-mat-label">CTS Upload Status</mat-label>
        <mat-form-field class="form-field-reduce-length">
          <mat-select placeholder="CTS Upload Status" [(value)]="selectCtsUploadStatus"
            (selectionChange)="onCtsUploadStatusChange($event)">
            <mat-option *ngFor="let element of ctsUploadStatusDic" [value]="element.value">
              {{ element.description }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <!-- <div class="col-md-1 col-sm-12 margin-top search-button-column">
        <button type="button" mat-raised-button class="ui-button search-button search-button-m-top"
          (click)="onCtsUploadSearch()">Search</button>
      </div> -->
    </div>

    <!-- 4. Grid Section -->
    <div class="top-action-row-exchange row">
      <div class="table-container">
        <bdo-table [id]="ctsUploadTableId" [columns]="ctsUploadColumns" scrollHeight="67vh"
          defaultSortColumnId="ExchangeReason" [defaultSortOrder]="'desc'" [pageIndex]="ctsUploadCurrentPage"
          [pageSize]="ctsUploadPageSize" [pageSizeOptions]="[10, 20, 50, 100]" [isVirtualScroll]="false"
          [hidePagination]="false" [rowSelectable]="true" [lazyLoad]="true"
          (onLazyLoad)="onCtsUploadLazyLoadEvent($event)" (onLinkClick)="onCtsUploadLinkClick($event)"
          (onActionClick)="onCtsUploadActionClick($event)" (onCheckboxClick)="onCheckboxClick($event)">
        </bdo-table>
      </div>
    </div>

  </mat-tab>
</mat-tab-group>