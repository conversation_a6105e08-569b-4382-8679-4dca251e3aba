﻿using Bdo.Ess.CtsIntegration.BahamasCtsSettings;
using Bdo.Ess.CtsIntegration.Encryption;
using Bdo.Ess.CtsIntegration.Entities;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Audit;
using Bdo.Ess.Shared.Utility.Exensions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.OpenSsl;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Uow;
using Volo.Abp.Users;

namespace Bdo.Ess.CtsIntegration.Certificate;

public class CertificateAppService : CtsIntegrationAppService, ICertificateAppService
{
    private readonly IBahamasCertificateRepository _bahamasCertificateRepository;
    private readonly ICtsEncryptionManager _ctsEncryptionManager;
    private readonly IDistributedEventBus _auditEventBus;

    /// <summary>
    ///  Note: _auditWebInfo is scoped dependency instance,
    ///  so, it will be shared between HttpApi and AppService
    ///  Work for Auditing purpose to get client IP address.
    /// </summary>
    private readonly IAuditWebInfo _auditWebInfo;
    private readonly ICurrentUser _currentUser;
    private readonly IDataFilter _dataFilter;

    public CertificateAppService(
        IBahamasCertificateRepository bahamasCertificateRepository
        , ICtsEncryptionManager ctsEncryptionManager
        , IDistributedEventBus auditEventBus
        , IAuditWebInfo auditWebInfo
        , ICurrentUser currentUser
        , IDataFilter dataFilter
        )
    {
        _bahamasCertificateRepository = bahamasCertificateRepository;
        _ctsEncryptionManager = ctsEncryptionManager;
        _auditWebInfo = auditWebInfo;
        _currentUser = currentUser;
        _dataFilter = dataFilter;
        _auditEventBus = auditEventBus;
    }

    [RemoteService(IsEnabled = false)]
    public async Task<string> GetBahamasCertificatePublicKey()
    {
        Logger.LogInformation("Getting Bahamas Certificate Public Key");
        var bahamasCertificate = await _bahamasCertificateRepository.GetBahamasCertificate().ConfigureAwait(false);
        bahamasCertificate = _ctsEncryptionManager.DecryptBahamasCertificate(bahamasCertificate!,true);
        return bahamasCertificate?.PublicKey ?? throw new BusinessException("No active Bahamas certificate found.");
    }

    [RemoteService(IsEnabled = false)]
    public async Task<BahamasCertificateDto?> GetBahamasCertificateInfo()
    {
        Logger.LogInformation("Getting Bahamas Certificate Info");
        var bahamasCertificate = await _bahamasCertificateRepository.GetBahamasCertificate().ConfigureAwait(false);
        if (bahamasCertificate == null) return null;
        //Must add unit of work, otherwise decrypted data would be saved to Db as plain text
        bahamasCertificate = _ctsEncryptionManager.DecryptBahamasCertificate(bahamasCertificate!,true);
        return ObjectMapper.Map<BahamasCertificate, BahamasCertificateDto>(bahamasCertificate!);
    }

    public async Task<BahamasCertificateDto?> GetBahamasCertificateInfoForDisplay()
    {
        var dto = await GetBahamasCertificateInfo();
        if (dto == null) return null;
        if (!string.IsNullOrEmpty(dto.CertificateContent))
        {
            dto.CertificateContent = dto.CertificateContent.Length > 30
                ? dto.CertificateContent.Substring(0, 30) + "..."
                : dto.CertificateContent;
        }
        dto.CertificatePassword = BahamasCtsSettingAppService.GetPasswordForDisplay(dto.CertificatePassword??"");
        dto.CertificateContent = BahamasCtsSettingAppService.GetCertficateForDisplay(dto.CertificateContent);
        return dto;
    }

    public async Task<BahamasCertificateDto?> GetBahamasCertificateByCreationTimeAsync (DateTime creationTime)
    {
        using (_dataFilter.Disable<ISoftDelete>())
        {
            var cert = (await _bahamasCertificateRepository.GetListAsync(x => x.ValidFrom <= creationTime && x.ExpiredAt >= creationTime))
                .OrderByDescending(x => x.CreationTime).FirstOrDefault();
            if (cert == null) return null;

            cert = _ctsEncryptionManager.DecryptBahamasCertificate(cert,true);

            return new BahamasCertificateDto
            {
                Id = cert.Id,
                PublicKey = cert.PublicKey,
                CertificateContent = cert.CertificateContent,
                CertificateContentType = cert.CertificateContentType,
                CertificateFileName = cert.CertificateFileName,
                CertificatePassword = cert.CertificatePassword, // This would be encrypted, no need to convert to base64
                ExpiredAt = cert.ExpiredAt,
                ValidFrom = cert.ValidFrom,
                IsActive = cert.IsActive
            };
        }
    }

    public async Task<BahamasCertificateDto> UploadBahamasCertificate(UploadCertificateDto input)
    {
        ArgumentNullException.ThrowIfNull(input);
        ArgumentNullException.ThrowIfNull(input.File);

        var result = await ProcessAndSaveCertificateAsync(input.File, input.Password);
        await AddAuditLogForCertificateUpdateAsync();
        return result;
    }

    private async Task<BahamasCertificateDto> ProcessAndSaveCertificateAsync(IFormFile file, string? password)
    {
        if (string.IsNullOrWhiteSpace(file.FileName))
            throw new UserFriendlyException("File name must be provided.", nameof(file));
        if (file.Length == 0)
            throw new UserFriendlyException("Uploaded file is empty.", nameof(file));

        var allowedExtensions = new HashSet<string> { ".cer", ".crt", ".pem", ".pfx", ".p12", ".der" };
        var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(fileExtension))
            throw new UserFriendlyException($"Unsupported certificate file extension: {fileExtension}");

        await using var ms = new MemoryStream();
        await file.OpenReadStream().CopyToAsync(ms);
        var rawData = ms.ToArray();

        X509Certificate2 cert = fileExtension switch
        {
            ".pfx" or ".p12" => LoadPkcs12Certificate(rawData, password!),
            ".cer" or ".crt" or ".der" => LoadX509Certificate(rawData),
            ".pem" => LoadPemCertificate(rawData),
            _ => throw new UserFriendlyException($"Unsupported certificate file extension: {fileExtension}")
        };

        var publicKey = cert.Export(X509ContentType.Cert);

        var certificate = new BahamasCertificate
        {
            CertificateContent = Convert.ToBase64String(rawData),
            CertificateContentType = file.ContentType,
            CertificateFileName = file.FileName,
            CertificatePassword = password!,  //password would be encrypted, no need to convert to base64
            PublicKey = Convert.ToBase64String(publicKey),
            ExpiredAt = DateTime.SpecifyKind(cert.NotAfter,DateTimeKind.Utc),
            ValidFrom = DateTime.SpecifyKind(cert.NotBefore,DateTimeKind.Utc),
            IsActive = true
        };

        var allExistings = await _bahamasCertificateRepository.GetListAsync().ConfigureAwait(false);
        if (allExistings.Count > 0)
        {
            // Deactivate all existing certificates
            foreach (var existing in allExistings)
            {
                existing.IsActive = false;
            }
            await _bahamasCertificateRepository.DeleteManyAsync(allExistings).ConfigureAwait(false);
        }
        _ctsEncryptionManager.EncryptBahamasCertificate(certificate);
        var bahamasCertificate = await _bahamasCertificateRepository.InsertAsync(certificate, true).ConfigureAwait(false);
        
        //Somehow, unit of work complete wouldn't block Decrypted data was Decrypted again and saved to database with plain text
        //Using Clone() instead
        var cloned = _ctsEncryptionManager.DecryptBahamasCertificate(bahamasCertificate, true);
        return ObjectMapper.Map<BahamasCertificate, BahamasCertificateDto>(cloned);
    }

    private async Task AddAuditLogForCertificateUpdateAsync()
    {
        try
        {
            var eto = new AuditCaCertificateUpdateEto
            {
                UserName = CurrentUser.UserName,
                UserId = CurrentUser.Id,
                IPAddress = _auditWebInfo.IPAddress,
                Action = AuditActionEnum.UpdateCACertificate,
                TenantId = CurrentTenant.Id,
                AuditDateTime = DateTime.UtcNow,
                NewValue = new AuditCaCertificateUpdateDto()
                {
                    EmailAddress = CurrentUser.Email,
                    UserName = CurrentUser.UserName,
                    FirstName = CurrentUser.Name,
                    LastName = CurrentUser.SurName,
                    UserId = CurrentUser.Id.ToString(),
                }
            };
            await _auditEventBus.PublishAsync(eto);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to add audit log for ca certificate update");
        }
    }

    private X509Certificate2 LoadPkcs12Certificate(byte[] rawData, string password)
    {
        ArgumentNullException.ThrowIfNull(rawData);
        // Password can be null or empty for some PKCS#12 files, so no check here
        try
        {
            //return X509CertificateLoader.LoadPkcs12(rawData, password, X509KeyStorageFlags.Exportable | X509KeyStorageFlags.PersistKeySet | X509KeyStorageFlags.MachineKeySet);  // uncomment this line for DotNet 9
            return new X509Certificate2(rawData, password, X509KeyStorageFlags.Exportable | X509KeyStorageFlags.PersistKeySet | X509KeyStorageFlags.MachineKeySet);
        }
        catch (CryptographicException ex)
        {
            Logger.LogError(ex, "Failed to load PKCS#12 certificate. Ensure the file and password are correct.");
            throw new UserFriendlyException("Failed to load the certificate. Ensure the file and password are correct.");
        }
    }

    private static X509Certificate2 LoadX509Certificate(byte[] rawData)
    {
        ArgumentNullException.ThrowIfNull(rawData);
        try
        {
            //return X509CertificateLoader.LoadCertificate(rawData);  // uncomment this line for DotNet 9
            return new X509Certificate2(rawData);
        }
        catch (CryptographicException ex)
        {
            throw new CryptographicException("Failed to load X.509 certificate. Ensure the file is a valid certificate.", ex);
        }
    }

    private static X509Certificate2 LoadPemCertificate(byte[] rawData)
    {
        ArgumentNullException.ThrowIfNull(rawData);
        var pem = System.Text.Encoding.UTF8.GetString(rawData);

        // Extract all certificates from PEM
        var certs = new List<X509Certificate2>();
        var certPattern = "-----BEGIN CERTIFICATE-----";
        var endCertPattern = "-----END CERTIFICATE-----";
        int start = 0;
        while ((start = pem.IndexOf(certPattern, start, StringComparison.Ordinal)) != -1)
        {
            int end = pem.IndexOf(endCertPattern, start, StringComparison.Ordinal);
            if (end == -1) break;
            int certStart = start + certPattern.Length;
            string base64 = pem.Substring(certStart, end - certStart).Replace("\r", "").Replace("\n", "").Trim();
            var certBytes = Convert.FromBase64String(base64);
            //certs.Add(X509CertificateLoader.LoadCertificate(certBytes));  // uncomment this line for DotNet 9
            certs.Add(new X509Certificate2(certBytes));
            start = end + endCertPattern.Length;
        }

        if (certs.Count > 0)
            return certs[0];

        throw new CryptographicException("No valid certificate found in PEM file.");
    }

    private static string ConvertToPem(RSA rsaKey, bool includePrivateParameters = false)
    {
        ArgumentNullException.ThrowIfNull(rsaKey, nameof(rsaKey));
        var keyParams = rsaKey.ExportParameters(includePrivateParameters);

        using var sw = new StringWriter();
        var pemWriter = new PemWriter(sw);

        if (includePrivateParameters)
        {
            var bcKey = new RsaPrivateCrtKeyParameters(
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.Modulus),
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.Exponent),
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.D),
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.P),
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.Q),
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.DP),
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.DQ),
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.InverseQ)
            );
            pemWriter.WriteObject(bcKey);
        }
        else
        {
            var bcKey = new RsaKeyParameters(
                false,
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.Modulus),
                new Org.BouncyCastle.Math.BigInteger(1, keyParams.Exponent)
            );
            pemWriter.WriteObject(bcKey);
        }

        pemWriter.Writer.Flush();
        return sw.ToString();
    }
}