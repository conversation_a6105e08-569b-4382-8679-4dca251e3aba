﻿using Bdo.Ess.CorporateEntityService.Permissions;
using Bdo.Ess.EconomicSubstanceService.Permissions;
using Bdo.Ess.LookupService.Permissions;
using Bdo.Ess.Shared.Hosting.Services;
using JetBrains.Annotations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using OpenIddict.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.OpenIddict.Applications;
using Volo.Abp.OpenIddict.Scopes;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Uow;
using Volo.Saas.Host;
using static Bdo.Ess.CtsIntegration.Permissions.CtsIntegrationPermissions;

namespace Bdo.Ess.DbMigrator;

public class OpenIddictDataSeeder : ITransientDependency
{
    private readonly IConfiguration _configuration;
    private readonly ICurrentTenant _currentTenant;
    private readonly IOpenIddictApplicationRepository _openIddictApplicationRepository;
    private readonly IAbpApplicationManager _applicationManager;
    private readonly IOpenIddictScopeRepository _openIddictScopeRepository;
    private readonly IOpenIddictScopeManager _scopeManager;
    private readonly IPermissionDataSeeder _permissionDataSeeder;
    private readonly IStringLocalizer<OpenIddictResponse> L;
    private readonly ILogger<OpenIddictDataSeeder> _logger;

    public OpenIddictDataSeeder(
        IConfiguration configuration,
        ICurrentTenant currentTenant,
        IOpenIddictApplicationRepository openIddictApplicationRepository,
        IAbpApplicationManager applicationManager,
        IOpenIddictScopeRepository openIddictScopeRepository,
        IOpenIddictScopeManager scopeManager,
        IPermissionDataSeeder permissionDataSeeder,
        IStringLocalizer<OpenIddictResponse> l,
        ILogger<OpenIddictDataSeeder> logger)
    {
        _configuration = configuration;
        _currentTenant = currentTenant;
        _openIddictApplicationRepository = openIddictApplicationRepository;
        _applicationManager = applicationManager;
        _openIddictScopeRepository = openIddictScopeRepository;
        _scopeManager = scopeManager;
        _permissionDataSeeder = permissionDataSeeder;
        L = l;
        _logger = logger;
    }

    public Task SeedAsync(DataSeedContext context)
    {
        return SeedAsync();
    }

    [UnitOfWork]
    public virtual async Task SeedAsync()
    {
        using (_currentTenant.Change(null))
        {
            await CreateApiScopesAsync();
            await CreateWebGatewaySwaggerClientsAsync();
            await CreateClientsAsync();
        }
    }

    private async Task CreateApiScopesAsync()
    {
        foreach (var service in ServiceConstants.AllServices)
        {
            await CreateScopesAsync(service);
        }
    }

    private async Task CreateWebGatewaySwaggerClientsAsync()
    {
        await CreateSwaggerClientAsync("WebGateway", ServiceConstants.AllServices );
    }

    private async Task CreateSwaggerClientAsync(string name, string[]? scopes = null)
    {
        var commonScopes = new List<string>
        {
            OpenIddictConstants.Permissions.Scopes.Address,
            OpenIddictConstants.Permissions.Scopes.Email,
            OpenIddictConstants.Permissions.Scopes.Phone,
            OpenIddictConstants.Permissions.Scopes.Profile,
            OpenIddictConstants.Permissions.Scopes.Roles
        };

        scopes ??= new[] { name };

        // Swagger Client
        var swaggerClientId = $"{name}_Swagger";
        if (!swaggerClientId.IsNullOrWhiteSpace())
        {
            var webGatewaySwaggerRootUrl = _configuration[$"OpenIddict:Applications:{name}:RootUrl"]?.TrimEnd('/');
            var redirectUris = _configuration.GetMicroserverRedirectUrls();
            redirectUris.Insert(0, $"{webGatewaySwaggerRootUrl}/swagger/oauth2-redirect.html");

            await CreateApplicationAsync(
                name: swaggerClientId!,
                type:  OpenIddictConstants.ClientTypes.Public,
                consentType: OpenIddictConstants.ConsentTypes.Implicit,
                displayName: "Swagger Client",
                secret: null,
                grantTypes: new List<string>
                {
                    OpenIddictConstants.GrantTypes.AuthorizationCode,
                },
                scopes: commonScopes.Union(scopes).ToList(),
                redirectUris: redirectUris
            );
        }
    }

    private async Task CreateScopesAsync(string name)
    {
        if (await _openIddictScopeRepository.FindByNameAsync(name) == null)
                {
            await _scopeManager.CreateAsync(new OpenIddictScopeDescriptor {
                Name = name, DisplayName = name + " API", Resources = { name }
            });
        }
    }

    private async Task CreateClientsAsync()
    {
        var commonScopes = new List<string>
        {
            OpenIddictConstants.Permissions.Scopes.Address,
            OpenIddictConstants.Permissions.Scopes.Email,
            OpenIddictConstants.Permissions.Scopes.Phone,
            OpenIddictConstants.Permissions.Scopes.Profile,
            OpenIddictConstants.Permissions.Scopes.Roles
        };

        //Web Client, this client could be deleted
        var webClientRootUrl = _configuration["OpenIddict:Applications:Web:RootUrl"]!.EnsureEndsWith('/');
        await CreateApplicationAsync(
            name: "Web",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Web Client",
            secret: "1q2w3e*",
            grantTypes: new List<string> //Hybrid flow
            {
                OpenIddictConstants.GrantTypes.AuthorizationCode, OpenIddictConstants.GrantTypes.Implicit
            },
            scopes: commonScopes.Union(ServiceConstants.AllServices).ToList(),
            redirectUris: new List<string> { $"{webClientRootUrl}signin-oidc" },
            postLogoutRedirectUris: new List<string>() { $"{webClientRootUrl}signout-callback-oidc" },
            clientUri: webClientRootUrl
            //logoUri: "/images/clients/aspnetcore.svg"
        );


        //Public Web Client
        var publicWebClientRootUrl = _configuration["OpenIddict:Applications:PublicWeb:RootUrl"]!.EnsureEndsWith('/');
        await CreateApplicationAsync(
            name: "PublicWeb",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Public Web Client",
            secret: "1q2w3e*",
            grantTypes: new List<string> //Hybrid flow
            {
                OpenIddictConstants.GrantTypes.AuthorizationCode,
                OpenIddictConstants.GrantTypes.Implicit
            },
            scopes: commonScopes.Union(ServiceConstants.AllPublicServices).ToList(),
            redirectUris: new List<string> { $"{publicWebClientRootUrl}signin-oidc" },
            postLogoutRedirectUris: new List<string> { $"{publicWebClientRootUrl}signout-callback-oidc" },
            clientUri: publicWebClientRootUrl
            //logoUri: "/images/clients/aspnetcore.svg"
        );

        //Angular Client
        var angularClientRootUrl = _configuration["OpenIddict:Applications:Angular:RootUrl"]?.TrimEnd('/');
        await CreateApplicationAsync(
            name: "Angular",
            type: OpenIddictConstants.ClientTypes.Public,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Angular Client",
            secret: null,
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.AuthorizationCode,
                OpenIddictConstants.GrantTypes.RefreshToken,
                OpenIddictConstants.GrantTypes.Password,
                "LinkLogin",
                "Impersonation"
            },
            scopes: commonScopes.Union(ServiceConstants.AngularClientServices).ToList(),
            redirectUris: new List<string> { $"{angularClientRootUrl}" },
            postLogoutRedirectUris: new List<string> { $"{angularClientRootUrl}" },
            clientUri: angularClientRootUrl
        );

        //Administration Service Client
        await CreateApplicationAsync(
            name: "AdministrationService",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Administration Service Client",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.ClientCredentials
            },
            scopes: commonScopes.Union(ServiceConstants.AdministrationClientScopes.Keys).ToList(),
            permissions: new List<string> { IdentityPermissions.Users.Default }
        );

       //Economic Substance Service Client
        await CreateApplicationAsync(
            name: "EconomicSubstanceService",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "EconomicSubstanceService API",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.ClientCredentials
            },
            scopes: commonScopes.Union(ServiceConstants.EconomicSubstanceClientScopes.Keys).ToList(),
            permissions: new List<string> { IdentityPermissions.Users.Default, IdentityPermissions.Roles.Default }
        );

        //Corporate Entity Service Client
        await CreateApplicationAsync(
            name: "CorporateEntityService",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Corporate Entity Service Client",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.ClientCredentials
            },
            scopes: commonScopes.Union(ServiceConstants.CorporateEntityClientScopes.Keys).ToList(),
            permissions: new List<string> { SaasHostPermissions.Tenants.Default, SaasHostPermissions.Tenants.Create, 
                SaasHostPermissions.Editions.Default, SaasHostPermissions.Editions.Create, SaasHostPermissions.Tenants.ManageConnectionStrings,}
        );

        //Search Service Client
        await CreateApplicationAsync(
            name: "SearchService",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Search ervice Client",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.ClientCredentials
            },
            scopes: commonScopes.Union(ServiceConstants.SearchClientScopes.Keys).ToList(),
            permissions: new List<string> { SaasHostPermissions.Tenants.Default, SaasHostPermissions.Editions.Default }   //Search service need to visit Saas to get available edition
        );

        //Azure Function Client
        await CreateApplicationAsync(
            name: "AzureFunction",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Azure Function Client",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.Password,
                OpenIddictConstants.GrantTypes.ClientCredentials,
            },
            scopes: commonScopes.Union(new[] { ServiceConstants.EconomicSubstanceService, ServiceConstants.CorporateEntityService }).ToList()
        );

        //Nightly Job Client
        await CreateApplicationAsync(
            name: "NightlyJob",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Nightly Job Client",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.Password,
                OpenIddictConstants.GrantTypes.ClientCredentials,
            },
            scopes: commonScopes.Union(ServiceConstants.NightlyJobClientScopes.Keys).ToList(),
            permissions: new List<string> { RefreshStatusPermissions.Default }
        );

		await CreateApplicationAsync(
			name: "RefreshCertificatesJob",
			type: OpenIddictConstants.ClientTypes.Confidential,
			consentType: OpenIddictConstants.ConsentTypes.Implicit,
			displayName: "Refresh Certificates Job",
			secret: "1q2w3e*",
			grantTypes: new List<string>
			{
				OpenIddictConstants.GrantTypes.Password,
				OpenIddictConstants.GrantTypes.ClientCredentials,
			},
			scopes: commonScopes.Union(ServiceConstants.RefreshCertificatesJobScopes.Keys).ToList()
		);

		await CreateApplicationAsync(
			name: "RefreshTransmissionStatusJob",
			type: OpenIddictConstants.ClientTypes.Confidential,
			consentType: OpenIddictConstants.ConsentTypes.Implicit,
			displayName: "Refresh Transmission Status Job",
			secret: "1q2w3e*",
			grantTypes: new List<string>
			{
				OpenIddictConstants.GrantTypes.Password,
				OpenIddictConstants.GrantTypes.ClientCredentials,
			},
			scopes: commonScopes.Union(ServiceConstants.RefreshTransmissionStatusJobScopes.Keys).ToList(),
            permissions: new List<string> { RefreshStatusPermissions.Default }
        );

		//Dashboard Service
		List<string> dashboardServicePermissions = new List<string> { ESServicePermissions.InformationExchangeDetail.Default, ESServicePermissions.InformationExchangeDetail.Create,
                ESServicePermissions.InformationExchangeDetail.Edit, ESServicePermissions.InformationExchangeDetail.ViewHistory,
            ESServicePermissions.InformationExchangeDetail.StatusAction,SaasHostPermissions.Tenants.Default,
                SaasHostPermissions.Editions.Default, SaasHostPermissions.Tenants.ManageConnectionStrings};

        dashboardServicePermissions.AddRange(CorporateEntityServicePermissions.GetAll());

        await CreateApplicationAsync(
            name: "DashboardService",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Dashboard Service API",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.ClientCredentials
            },
            scopes: commonScopes.Union(ServiceConstants.DashboardClientScopes.Keys).ToList(),
            permissions: dashboardServicePermissions  
        );


        //Audit Service Client.
        await CreateApplicationAsync(
            name: "AuditService",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Audit Service API",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.ClientCredentials
            },
            scopes: commonScopes.Union(ServiceConstants.AuditClientScopes.Keys).ToList(),
            //
            //Note: In Audit Service, it needs to get users list for the audit log, so it needs to have the permission to get users from Identity Service
            //
            permissions: new List<string> { IdentityPermissions.Users.Default, IdentityPermissions.Roles.Default, SaasHostPermissions.Tenants.Default,
                SaasHostPermissions.Editions.Default }
        );

        //Cts Integration Service Client.
        await CreateApplicationAsync(
            name: "CtsIntegrationService",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Cts Integration Service API",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.ClientCredentials
            },
            scopes: commonScopes.Union(ServiceConstants.CtsIntegrationClientScopes.Keys).ToList(),
			permissions: new List<string> { IdentityPermissions.Users.Default, IdentityPermissions.Roles.Default,
            ESServicePermissions.InformationExchangeDetail.Default }
		);

        //Azure Function Client
        await CreateApplicationAsync(
            name: "SearchReindexerJob",
            type: OpenIddictConstants.ClientTypes.Confidential,
            consentType: OpenIddictConstants.ConsentTypes.Implicit,
            displayName: "Search Reindexer Job Client",
            secret: "1q2w3e*",
            grantTypes: new List<string>
            {
                OpenIddictConstants.GrantTypes.Password,
                OpenIddictConstants.GrantTypes.ClientCredentials,
            },
            scopes: commonScopes.Union(new[] { ServiceConstants.EconomicSubstanceService, ServiceConstants.CorporateEntityService }).ToList()
        );
        //Search Service, Identity Service and Lookup Service do not use any microservice, no setting needed
    }

    private async Task UpdateApplicationRedirectUris(string name, List<string>? redirectUris = null,
        List<string>? postLogoutRedirectUris = null)
    {
        if (name == "Angular")
        {
            var breakpoint = 0;
        }
        var app = await _applicationManager.FindByClientIdAsync(name);
        var application = app as OpenIddictApplicationModel;
        if (application == null) { return; }
        if (redirectUris == null) redirectUris = new List<string>();
        if (postLogoutRedirectUris == null) postLogoutRedirectUris = new List<string>();
        if (!redirectUris.Any() && !postLogoutRedirectUris.Any()) { return; }

        var validUrls = new List<string>();

        foreach (var redirectUri in redirectUris)
        {
            if (!Uri.TryCreate(redirectUri, UriKind.Absolute, out var uri) || !uri.IsWellFormedOriginalString())
            {
                throw new BusinessException(L["InvalidRedirectUri", redirectUri]);
            }
            
            validUrls.Add(uri.ToString());
        }
        
        var validLogoutUrls = new List<string>();
        foreach (var postLogoutRedirectUri in postLogoutRedirectUris)
        {
            if (!Uri.TryCreate(postLogoutRedirectUri, UriKind.Absolute, out var uri) || !uri.IsWellFormedOriginalString())
            {
                throw new BusinessException(L["InvalidPostLogoutRedirectUri", postLogoutRedirectUri]);
            }
            validLogoutUrls.Add(uri.ToString());
        }
        if (validUrls.Any())
        {
            application.RedirectUris = JsonSerializer.Serialize(validUrls);
        }
        if (validLogoutUrls.Any())
        {
            application.PostLogoutRedirectUris = JsonSerializer.Serialize(validLogoutUrls);
        }
        await _applicationManager.UpdateAsync(application);
        
    }

    private async Task UpdateApplicationScopesAndPermissions(string name, string type,
        List<string> grantTypes, List<string> scopes,
        List<string>? redirectUris = null,
        List<string>? postLogoutRedirectUris = null,
        List<string>? permissions = null)
    {
        var app = await _applicationManager.FindByClientIdAsync(name);
        var application = app as OpenIddictApplicationModel;
        if (application == null) { return; }

        var applicationDesc = new OpenIddictApplicationDescriptor
        {
            ClientId = name,
            ClientType = type,
        };

        
        var calcedPermissions = ProcessAppliationPermissions(applicationDesc, name, type, grantTypes, scopes, redirectUris, postLogoutRedirectUris, permissions);
        if (calcedPermissions.Any())
        {
            application.Permissions = JsonSerializer.Serialize(calcedPermissions);
        }
        if (permissions != null)
        {
            await _permissionDataSeeder.SeedAsync(
                ClientPermissionValueProvider.ProviderName,
                name,
                permissions,
                null
            );
        }
        await _applicationManager.UpdateAsync(application);
    }

    private HashSet<string> ProcessAppliationPermissions(OpenIddictApplicationDescriptor application,
        string name, string type, 
        List<string> grantTypes, List<string> scopes, 
        List<string>? redirectUris = null,
        List<string>? postLogoutRedirectUris = null,
        List<string>? permissions = null)
    {
        if (new[] { OpenIddictConstants.GrantTypes.AuthorizationCode, OpenIddictConstants.GrantTypes.Implicit }.All(grantTypes.Contains))
        {
            application.Permissions.Add(OpenIddictConstants.Permissions.ResponseTypes.CodeIdToken);

            if (string.Equals(type, OpenIddictConstants.ClientTypes.Public, StringComparison.OrdinalIgnoreCase))
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.ResponseTypes.CodeIdTokenToken);
                application.Permissions.Add(OpenIddictConstants.Permissions.ResponseTypes.CodeToken);
            }
        }

        if (!redirectUris.IsNullOrEmpty() || !postLogoutRedirectUris.IsNullOrEmpty())
        {
            application.Permissions.Add(OpenIddictConstants.Permissions.Endpoints.Logout);
        }

        var buildInGrantTypes = new[]
        {
                OpenIddictConstants.GrantTypes.Implicit,
                OpenIddictConstants.GrantTypes.Password,
                OpenIddictConstants.GrantTypes.AuthorizationCode,
                OpenIddictConstants.GrantTypes.ClientCredentials,
                OpenIddictConstants.GrantTypes.DeviceCode,
                OpenIddictConstants.GrantTypes.RefreshToken
            };

        foreach (var grantType in grantTypes)
        {
            if (grantType == OpenIddictConstants.GrantTypes.AuthorizationCode)
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.GrantTypes.AuthorizationCode);
                application.Permissions.Add(OpenIddictConstants.Permissions.ResponseTypes.Code);
            }

            if (grantType == OpenIddictConstants.GrantTypes.AuthorizationCode || grantType == OpenIddictConstants.GrantTypes.Implicit)
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.Endpoints.Authorization);
            }

            if (grantType == OpenIddictConstants.GrantTypes.AuthorizationCode ||
                grantType == OpenIddictConstants.GrantTypes.ClientCredentials ||
                grantType == OpenIddictConstants.GrantTypes.Password ||
                grantType == OpenIddictConstants.GrantTypes.RefreshToken ||
                grantType == OpenIddictConstants.GrantTypes.DeviceCode)
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.Endpoints.Token);
                application.Permissions.Add(OpenIddictConstants.Permissions.Endpoints.Revocation);
                application.Permissions.Add(OpenIddictConstants.Permissions.Endpoints.Introspection);
            }

            if (grantType == OpenIddictConstants.GrantTypes.ClientCredentials)
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.GrantTypes.ClientCredentials);
            }

            if (grantType == OpenIddictConstants.GrantTypes.Implicit)
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.GrantTypes.Implicit);
            }

            if (grantType == OpenIddictConstants.GrantTypes.Password)
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.GrantTypes.Password);
            }

            if (grantType == OpenIddictConstants.GrantTypes.RefreshToken)
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.GrantTypes.RefreshToken);
            }

            if (grantType == OpenIddictConstants.GrantTypes.DeviceCode)
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.GrantTypes.DeviceCode);
                application.Permissions.Add(OpenIddictConstants.Permissions.Endpoints.Device);
            }

            if (grantType == OpenIddictConstants.GrantTypes.Implicit)
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.ResponseTypes.IdToken);
                if (string.Equals(type, OpenIddictConstants.ClientTypes.Public, StringComparison.OrdinalIgnoreCase))
                {
                    application.Permissions.Add(OpenIddictConstants.Permissions.ResponseTypes.IdTokenToken);
                    application.Permissions.Add(OpenIddictConstants.Permissions.ResponseTypes.Token);
                }
            }

            if (!buildInGrantTypes.Contains(grantType))
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.Prefixes.GrantType + grantType);
            }
        }

        var buildInScopes = new[]
        {
                OpenIddictConstants.Permissions.Scopes.Address,
                OpenIddictConstants.Permissions.Scopes.Email,
                OpenIddictConstants.Permissions.Scopes.Phone,
                OpenIddictConstants.Permissions.Scopes.Profile,
                OpenIddictConstants.Permissions.Scopes.Roles
            };

        foreach (var scope in scopes)
        {
            if (buildInScopes.Contains(scope))
            {
                application.Permissions.Add(scope);
            }
            else
            {
                application.Permissions.Add(OpenIddictConstants.Permissions.Prefixes.Scope + scope);
            }
        }
        return application.Permissions;
    }
    private async Task CreateApplicationAsync(
        [NotNull] string name,
        [NotNull] string type,
        [NotNull] string consentType,
        string displayName,
        string? secret,
        List<string> grantTypes,
        List<string> scopes,
        List<string>? redirectUris = null,
        List<string>? postLogoutRedirectUris = null,
        List<string>? permissions = null,
        string? clientUri = null,
        string? logoUri = null)
    {
        if (!string.IsNullOrEmpty(secret) && string.Equals(type, OpenIddictConstants.ClientTypes.Public, StringComparison.OrdinalIgnoreCase))
        {
            throw new BusinessException(L["NoClientSecretCanBeSetForPublicApplications"]);
        }

        if (string.IsNullOrEmpty(secret) && string.Equals(type, OpenIddictConstants.ClientTypes.Confidential, StringComparison.OrdinalIgnoreCase))
        {
            throw new BusinessException(L["TheClientSecretIsRequiredForConfidentialApplications"]);
        }

        //Abp 8.2 version code has update code
        /*if (!string.IsNullOrEmpty(name) && await _applicationManager.FindByClientIdAsync(name) != null)
        {
            await UpdateApplicationRedirectUris(name, redirectUris, postLogoutRedirectUris);
            await UpdateApplicationScopesAndPermissions(name, type, grantTypes, scopes, redirectUris, postLogoutRedirectUris, permissions);
            return;
        }
        */

        var client = await _openIddictApplicationRepository.FindByClientIdAsync(name);

        //if (client == null)
        {
            var application = new AbpApplicationDescriptor
            {
                ClientId = name,
                ClientType = type,
                ClientSecret = secret,
                ConsentType = consentType,
                DisplayName = displayName,
                ClientUri = clientUri,
                LogoUri = logoUri
            };

            Check.NotNullOrEmpty(grantTypes, nameof(grantTypes));
            Check.NotNullOrEmpty(scopes, nameof(scopes));

            ProcessAppliationPermissions(application,name,type,grantTypes,scopes,redirectUris,postLogoutRedirectUris,permissions);

            if (!redirectUris.IsNullOrEmpty())
            {
                foreach (var redirectUri in redirectUris!)
                {
                    _logger.LogInformation($"=== {redirectUri} ===");
                    if (!Uri.TryCreate(redirectUri, UriKind.Absolute, out var uri) || !uri.IsWellFormedOriginalString())
                    {
                        throw new BusinessException(L["InvalidRedirectUri", redirectUri]);
                    }

                    if (application.RedirectUris.All(x => x != uri))
                    {
                        application.RedirectUris.Add(uri);
                    }
                }
            }

            if (!postLogoutRedirectUris.IsNullOrEmpty())
            {
                foreach (var postLogoutRedirectUri in postLogoutRedirectUris!)
                {
                    if (!Uri.TryCreate(postLogoutRedirectUri, UriKind.Absolute, out var uri) || !uri.IsWellFormedOriginalString())
                    {
                        throw new BusinessException(L["InvalidPostLogoutRedirectUri", postLogoutRedirectUri]);
                    }

                    if (application.PostLogoutRedirectUris.All(x => x != uri))
                    {
                        application.PostLogoutRedirectUris.Add(uri);
                    }
                }
            }

            if (permissions != null)
            {
                await _permissionDataSeeder.SeedAsync(
                    ClientPermissionValueProvider.ProviderName,
                    name,
                    permissions,
                    null
                );
            }

            if (client == null)
            {
                await _applicationManager.CreateAsync(application);
                return;
            }

            if (!HasSameRedirectUris(client, application))
            {
                //Custom Code, ESS existing settings always has end /
                //client.RedirectUris = JsonSerializer.Serialize(application.RedirectUris.Select(q => q.ToString().TrimEnd('/')));
                //client.PostLogoutRedirectUris = JsonSerializer.Serialize(application.PostLogoutRedirectUris.Select(q => q.ToString().TrimEnd('/')));
                client.RedirectUris = JsonSerializer.Serialize(application.RedirectUris.Select(q => q.ToString()));
                client.PostLogoutRedirectUris = JsonSerializer.Serialize(application.PostLogoutRedirectUris.Select(q => q.ToString()));

                await _applicationManager.UpdateAsync(client.ToModel());
            }

            if (!HasSameScopes(client, application))
            {
                client.Permissions = JsonSerializer.Serialize(application.Permissions.Select(q => q.ToString()));
                await _applicationManager.UpdateAsync(client.ToModel());
            }
        }

    }
    private bool HasSameRedirectUris(OpenIddictApplication existingClient, AbpApplicationDescriptor application)
    {
        return existingClient.RedirectUris == JsonSerializer.Serialize(application.RedirectUris.Select(q => q.ToString().TrimEnd('/')));
    }

    private bool HasSameScopes(OpenIddictApplication existingClient, AbpApplicationDescriptor application)
    {
        return existingClient.Permissions == JsonSerializer.Serialize(application.Permissions.Select(q => q.ToString().TrimEnd('/')));
    }
}
