{"ast": null, "code": "import { eLayoutType } from '@abp/ng.core';\nimport { LOCAL_STORAGE_KEYS } from './shared/constants';\nimport { BdoSettingsComponent, BreadcrumbComponent } from './shared/components';\nimport { BdoToolbarContainerComponent } from './shared/components/toolbar/toolbar-container';\nimport { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nimport * as i2 from \"./shared/services\";\nimport * as i3 from \"angular-oauth2-oidc\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"@abp/ng.theme.shared\";\nexport class AppComponent {\n  constructor(replaceableComponents, configService, routes, appConfigService, oauthService, titleService) {\n    this.replaceableComponents = replaceableComponents;\n    this.configService = configService;\n    this.routes = routes;\n    this.appConfigService = appConfigService;\n    this.oauthService = oauthService;\n    this.titleService = titleService;\n    this.title = 'ESS';\n    this.version = '';\n    this.year = new Date();\n    this.timeOutSecond = environment['timeOutSecond'];\n    this.replaceAbpComponents();\n    // this.oauthService.loadDiscoveryDocumentAndLogin();\n  }\n  ngOnInit() {\n    this.config = this.appConfigService.getEnvConfig();\n    this.version = `${this.config.version.number}.${this.config.version.build}.${this.config.version.buildId ?? ''}`;\n    //Case 1: User close browser without logout, the access token is still valid, open site again, user can visit home page without login, the idle continue previous counting\n    //Case 2: User close browser without logout, the access token expired, open site again\n    //        code below force to login page, otherwise refresh token would automatically get a new access token\n    //Case 3: User leave the browser tab, the Idle time code in header component will force logout and clear last action time\n    //Case 4: User sign out, the header component will clear tokens and last action time\n    const lastActivityTime = localStorage.getItem(LOCAL_STORAGE_KEYS.UserLastActivityTime);\n    if (lastActivityTime) {\n      if (Date.now() - parseInt(lastActivityTime) > this.timeOutSecond * 1000) {\n        //revoke existing token and logout from application\n        localStorage.removeItem(LOCAL_STORAGE_KEYS.UserLastActivityTime);\n        this.oauthService.revokeTokenAndLogout();\n      }\n    }\n    if (!this.oauthService.hasValidAccessToken() || !this.oauthService.hasValidIdToken()) {\n      if (localStorage) {\n        localStorage.removeItem(LOCAL_STORAGE_KEYS.UserLastActivityTime);\n      }\n      this.oauthService.revokeTokenAndLogout();\n    }\n    this.getFeatures();\n    //\n    // Note: Dynamic loading \"Information Exchange\", \"Information Exchange Import\", \"Dashboard\" menus to side bar if current tenant is CA portal.\n    //\n    var isCa = this.configService.getFeature('SearchService.CASearch') == 'true';\n    if (isCa) {\n      this.titleService.setTitle('Economic Substance Competent Authority Portal');\n      this.routes.add([{\n        path: 'es-info-exchange',\n        name: \"Information Exchange\" /* ComponentNames.ES_INFORMATION_EXCHANGE */,\n        iconClass: 'fas fa-sync',\n        order: 3,\n        layout: \"application\" /* eLayoutType.application */,\n        requiredPolicy: \"DashboardService.Dashboard.InformationExchange\" /* Permissions.DASHBOARD_INFORMATION_EXCHANGE */\n      }, /** Information Exchange File Import for CA portal.  */\n      {\n        path: 'es-info-exchange/information-exchange-import',\n        name: \"Info Exchange Import\" /* ComponentNames.ES_INFORMATION_EXCHANGE_IMPORT */,\n        iconClass: 'fas fa-file',\n        order: 4,\n        layout: \"application\" /* eLayoutType.application */,\n        requiredPolicy: \"CAPortal.InformationExchangeImportFile\" /* Permissions.INFORMATION_EXCHANGE_IMPORT_FIle */\n      }, /* Dashboard for CA portal. */\n      {\n        path: 'dashboard',\n        name: \"Dashboard\" /* ComponentNames.ES_DASHBOARD */,\n        iconClass: 'fas fa-chart-line',\n        order: 5,\n        layout: \"application\" /* eLayoutType.application */,\n        requiredPolicy: \"DashboardService.Dashboard.MonitoringDashboard\" /* Permissions.DASHBOARD_MONITORING_DASHBOARD */\n      }, /* ES assessment list for CA portal. */\n      {\n        path: 'es-assessment-list',\n        name: \"ES Assessment List\" /* ComponentNames.ES_ASSESSMENT_LIST */,\n        iconClass: 'fas fa-list-alt',\n        order: 5,\n        layout: \"application\" /* eLayoutType.application */,\n        requiredPolicy: \"CAPortal.AssignmentList.ViewAssignmentList\" /* Permissions.CAPORTAL_ASSIGNMENT_LIST_VIEW */\n      }, /* Red Flag Settings for CA portal. */\n      {\n        path: 'redflags',\n        name: \"Red Flags Settings\" /* ComponentNames.ES_REDFLAGS */,\n        iconClass: 'fas fa-flag',\n        order: 5,\n        layout: \"application\" /* eLayoutType.application */,\n        requiredPolicy: \"CAPortal.RedFlag.View\" /* Permissions.CAPORTAL_REDFLAG_VIEW */\n      }, /* Audit Trail for CA portal. */\n      {\n        path: 'audit-trail',\n        name: \"Audit Trail\" /* ComponentNames.ES_AUDIT_TRAIL */,\n        iconClass: 'fas fa-clipboard-list',\n        order: 5,\n        layout: \"application\" /* eLayoutType.application */,\n        requiredPolicy: \"AuditService.AuditTrail\" /* Permissions.AUDIT_TRAIL_DEFAULT */\n      }]);\n    } else {\n      //Only show ES import for RA Portal\n      this.titleService.setTitle('Economic Substance Registered Agent Portal');\n      this.routes.add([{\n        path: 'es-import',\n        name: \"ES Import\" /* ComponentNames.ES_IMPORT */,\n        iconClass: 'fas fa-file',\n        order: 3,\n        layout: \"application\" /* eLayoutType.application */,\n        requiredPolicy: \"EsService.DeclarationImportFile\" /* Permissions.DECLARATION_IMPORT */\n      }, {\n        path: '/compliance-email',\n        name: \"Compliance Emails\" /* ComponentNames.ES_COMPLIANCE_EMAILS */,\n        parentName: '::Administration',\n        layout: \"application\" /* eLayoutType.application */,\n        requiredPolicy: \"AbpIdentity.Users\" /* Permissions.ABPIDENTITY_USERS */\n      }, /* Audit Trail for RA portal. Note: Hide it before deliver RA portals to production. */\n      {\n        path: 'audit-trail',\n        name: \"Audit Trail\" /* ComponentNames.ES_AUDIT_TRAIL */,\n        iconClass: 'fas fa-clipboard-list',\n        order: 5,\n        layout: \"application\" /* eLayoutType.application */,\n        requiredPolicy: \"AuditService.AuditTrail\" /* Permissions.AUDIT_TRAIL_DEFAULT */\n      }]);\n    }\n  }\n  replaceAbpComponents() {\n    /** Header component */\n    this.replaceableComponents.add({\n      component: BreadcrumbComponent,\n      key: \"Theme.Breadcrumb\" /* eThemeLeptonXComponents.Breadcrumb */\n    });\n    /** Right sidebar > user */\n    this.replaceableComponents.add({\n      component: BdoToolbarContainerComponent,\n      key: \"Theme.ToolbarComponent\" /* eThemeLeptonXComponents.Toolbar */\n    });\n    /** Right sidebar > settings (empty component) */\n    this.replaceableComponents.add({\n      component: BdoSettingsComponent,\n      key: \"Theme.SettingsComponent\" /* eThemeLeptonXComponents.Settings */\n    });\n  }\n  getFeatures() {\n    if (this.configService.getFeature(\"EsService.Templates\" /* Features.ES_SERVICE_TEMPLATES */) === 'false') {\n      this.routes.remove([\"Declaration Templates\" /* ComponentNames.ES_DECLARATION_TEMPLATES */]);\n    }\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.ReplaceableComponentsService), i0.ɵɵdirectiveInject(i1.ConfigStateService), i0.ɵɵdirectiveInject(i1.RoutesService), i0.ɵɵdirectiveInject(i2.AppConfigService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.Title));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 2,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"abp-loader-bar\")(1, \"abp-dynamic-layout\");\n        }\n      },\n      dependencies: [i5.LoaderBarComponent, i1.DynamicLayoutComponent],\n      styles: [\"\\n\\n\\n\\nhtml[_ngcontent-%COMP%], body[_ngcontent-%COMP%] {\\n  height: 100%;\\n  min-height: 100%;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  padding: 0;\\n  margin: 0;\\n  \\n\\n  font-family: \\\"Trebuchet MS\\\", \\\"Source Sans Pro\\\", \\\"sans-serif\\\";\\n}\\nbody[_ngcontent-%COMP%]   *[_ngcontent-%COMP%] {\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.header[_ngcontent-%COMP%], .footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n}\\n\\n.bdo-header[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  margin-top: 1em;\\n}\\n.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: normal;\\n}\\n.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\na[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.dev-hint[_ngcontent-%COMP%] {\\n  font-size: 0.8em;\\n  color: #008000 !important;\\n}\\n\\n.swal2-styled[_ngcontent-%COMP%] {\\n  margin: 0.3125em !important;\\n}\\n\\n.colored-toast.swal2-icon-success[_ngcontent-%COMP%] {\\n  background-color: #a5dc86 !important;\\n}\\n\\n.colored-toast.swal2-icon-error[_ngcontent-%COMP%] {\\n  background-color: #f27474 !important;\\n}\\n\\n.colored-toast.swal2-icon-warning[_ngcontent-%COMP%] {\\n  background-color: #f8bb86 !important;\\n}\\n\\n.colored-toast.swal2-icon-info[_ngcontent-%COMP%] {\\n  background-color: #3fc3ee !important;\\n}\\n\\n.colored-toast.swal2-icon-question[_ngcontent-%COMP%] {\\n  background-color: #87adbd !important;\\n}\\n\\n.colored-toast[_ngcontent-%COMP%]   .swal2-title[_ngcontent-%COMP%] {\\n  color: #fffefe;\\n}\\n\\n.colored-toast[_ngcontent-%COMP%]   .swal2-close[_ngcontent-%COMP%] {\\n  color: #fffefe;\\n}\\n\\n.colored-toast[_ngcontent-%COMP%]   .swal2-html-container[_ngcontent-%COMP%] {\\n  color: #fffefe;\\n}\\n\\n.font-dialog-title[_ngcontent-%COMP%] {\\n  font-size: 1.25em;\\n}\\n\\n.font-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.875em !important;\\n}\\n\\n.button-margin[_ngcontent-%COMP%] {\\n  margin: 0.625em;\\n}\\n\\n.button-right[_ngcontent-%COMP%] {\\n  float: right;\\n}\\n\\n.margin-10[_ngcontent-%COMP%] {\\n  margin: 0.625em;\\n}\\n\\n.margin-l-5[_ngcontent-%COMP%] {\\n  margin-left: 0.3125em;\\n}\\n\\n.display-flex-space[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.fill-extra-space[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.ui-button[_ngcontent-%COMP%] {\\n  background-color: #000000 !important;\\n  color: #fff !important;\\n}\\n\\n.ui-button[disabled][_ngcontent-%COMP%] {\\n  background-color: #77C8ED !important;\\n  color: #fff !important;\\n}\\n\\n.ui-button-red[_ngcontent-%COMP%] {\\n  background-color: red !important;\\n  color: #fff !important;\\n}\\n\\n.legend-pink[_ngcontent-%COMP%] {\\n  color: white;\\n  background: lightcoral;\\n  padding: 3px 10px;\\n  border-radius: 1em;\\n  display: inline-block;\\n  max-width: 90%;\\n}\\n\\n.legend-navy[_ngcontent-%COMP%] {\\n  color: white;\\n  background: navy;\\n  padding: 3px 10px;\\n  border-radius: 1em;\\n  display: inline-block;\\n  max-width: 90%;\\n}\\n\\n.legend-linethrough[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n}\\n\\n.legend-orange[_ngcontent-%COMP%] {\\n  color: white;\\n  background: orange;\\n  padding: 3px 10px;\\n  border-radius: 1em;\\n  display: inline-block;\\n  max-width: 90%;\\n}\\n\\n.legend-black[_ngcontent-%COMP%] {\\n  color: white;\\n  background: black;\\n  padding: 3px 10px;\\n  border-radius: 1em;\\n  display: inline-block;\\n  max-width: 90%;\\n}\\n\\n.legend-green[_ngcontent-%COMP%] {\\n  color: white;\\n  background: green;\\n  padding: 3px 10px;\\n  border-radius: 1em;\\n  display: inline-block;\\n  max-width: 90%;\\n}\\n\\n.legend-red[_ngcontent-%COMP%] {\\n  color: white;\\n  background: red;\\n  padding: 3px 10px;\\n  border-radius: 1em;\\n  display: inline-block;\\n  max-width: 90%;\\n}\\n\\n.legend-yellow[_ngcontent-%COMP%] {\\n  color: black;\\n  background: yellow;\\n  padding: 3px 10px;\\n  border-radius: 1em;\\n  display: inline-block;\\n  max-width: 90%;\\n}\\n\\n.legend-grey[_ngcontent-%COMP%] {\\n  color: white;\\n  background: grey;\\n  padding: 3px 10px;\\n  border-radius: 1em;\\n  display: inline-block;\\n  max-width: 90%;\\n}\\n\\n.form-field[_ngcontent-%COMP%] {\\n  width: 30em;\\n  margin: 0 auto;\\n  display: block;\\n}\\n\\n.form-field-reduce-length[_ngcontent-%COMP%] {\\n  width: 20em;\\n  height: 60px;\\n}\\n\\n.form[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.card-class[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 60em;\\n  margin: 0 auto;\\n}\\n\\n.checkBox[_ngcontent-%COMP%] {\\n  margin-right: 5em;\\n}\\n\\nmat-row[_ngcontent-%COMP%]:nth-child(odd) {\\n  background-color: #eaf0f3;\\n}\\n\\nmat-row[_ngcontent-%COMP%]:nth-child(even) {\\n  background-color: #ffffff;\\n}\\n\\n.title-color-font[_ngcontent-%COMP%] {\\n  color: #00779b !important;\\n  font-size: 2em !important;\\n}\\n\\n.confirmation[_ngcontent-%COMP%]   .confirmation-dialog[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%] {\\n  flex-direction: row;\\n}\\n\\n.mat-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.icon[_ngcontent-%COMP%] {\\n  top: 18%;\\n  left: 27%;\\n  margin-left: -0.5em !important;\\n  width: 1em !important;\\n  height: 1em !important;\\n}\\n\\n.confirmation[_ngcontent-%COMP%]   .confirmation-dialog[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  margin-top: 0em;\\n  margin-left: 0em !important;\\n  width: 100px !important;\\n  height: 100px !important;\\n}\\n\\n.line[_ngcontent-%COMP%] {\\n  height: 1px !important;\\n  background-color: #929292 !important;\\n  margin-top: 5em !important;\\n  z-index: 999;\\n  position: relative;\\n}\\n\\n.info-exchange-header[_ngcontent-%COMP%] {\\n  text-align: left !important;\\n}\\n\\n.cts-data-packet-dashboard-header[_ngcontent-%COMP%] {\\n  text-align: center !important;\\n}\\n\\n.cts-data-packet-dashboard-value[_ngcontent-%COMP%] {\\n  text-align: center !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n}\\n.cts-data-packet-dashboard-value[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  justify-content: center !important;\\n  align-items: center !important;\\n  margin: 0 auto !important;\\n}\\n.cts-data-packet-dashboard-value[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-layout[_ngcontent-%COMP%] {\\n  justify-content: center !important;\\n}\\n.cts-data-packet-dashboard-value[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%] {\\n  text-align: center !important;\\n}\\n\\n.sv-dropdown_select-wrapper[_ngcontent-%COMP%] {\\n  text-wrap: wrap;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["eLayoutType", "LOCAL_STORAGE_KEYS", "BdoSettingsComponent", "BreadcrumbComponent", "BdoToolbarContainerComponent", "environment", "AppComponent", "constructor", "replaceableComponents", "configService", "routes", "appConfigService", "oauthService", "titleService", "title", "version", "year", "Date", "timeOutSecond", "replaceAbpComponents", "ngOnInit", "config", "getEnvConfig", "number", "build", "buildId", "lastActivityTime", "localStorage", "getItem", "UserLastActivityTime", "now", "parseInt", "removeItem", "revokeTokenAndLogout", "hasValidAccessToken", "hasValidIdToken", "getFeatures", "isCa", "getFeature", "setTitle", "add", "path", "name", "iconClass", "order", "layout", "requiredPolicy", "parentName", "component", "key", "remove", "i0", "ɵɵdirectiveInject", "i1", "ReplaceableComponentsService", "ConfigStateService", "RoutesService", "i2", "AppConfigService", "i3", "OAuthService", "i4", "Title", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\app.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\nimport { IAppConfig } from './shared/interfaces';\r\nimport { AppConfigService } from './shared/services';\r\nimport {\r\n  RoutesService,\r\n  ConfigStateService,\r\n  ReplaceableComponentsService,\r\n  eLayoutType,\r\n  ABP,\r\n} from '@abp/ng.core';\r\nimport { Features, ComponentNames, Permissions, LOCAL_STORAGE_KEYS } from './shared/constants';\r\nimport { eThemeLeptonXComponents } from '@volosoft/abp.ng.theme.lepton-x';\r\nimport { BdoSettingsComponent, BreadcrumbComponent } from './shared/components';\r\nimport { BdoToolbarContainerComponent } from './shared/components/toolbar/toolbar-container';\r\nimport { OAuthService } from 'angular-oauth2-oidc';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { environment } from '@environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss'],\r\n})\r\nexport class AppComponent implements OnInit {\r\n  public title = 'ESS';\r\n  public version = '';\r\n  public year = new Date();\r\n  readonly timeOutSecond: number = environment['timeOutSecond'];\r\n  protected config!: IAppConfig;\r\n\r\n  constructor(\r\n    private readonly replaceableComponents: ReplaceableComponentsService,\r\n    private readonly configService: ConfigStateService,\r\n    private readonly routes: RoutesService,\r\n    private readonly appConfigService: AppConfigService,\r\n    private readonly oauthService: OAuthService,\r\n    private readonly titleService: Title\r\n  ) {\r\n    this.replaceAbpComponents();\r\n    // this.oauthService.loadDiscoveryDocumentAndLogin();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.config = this.appConfigService.getEnvConfig();\r\n\r\n    this.version = `${this.config.version.number}.${\r\n      this.config.version.build\r\n    }.${this.config.version.buildId ?? ''}`;\r\n    \r\n    //Case 1: User close browser without logout, the access token is still valid, open site again, user can visit home page without login, the idle continue previous counting\r\n    //Case 2: User close browser without logout, the access token expired, open site again\r\n    //        code below force to login page, otherwise refresh token would automatically get a new access token\r\n    //Case 3: User leave the browser tab, the Idle time code in header component will force logout and clear last action time\r\n    //Case 4: User sign out, the header component will clear tokens and last action time\r\n    const lastActivityTime = localStorage.getItem(LOCAL_STORAGE_KEYS.UserLastActivityTime);\r\n    if (lastActivityTime) {\r\n      if (Date.now() - parseInt(lastActivityTime) > this.timeOutSecond * 1000) {\r\n        //revoke existing token and logout from application\r\n        localStorage.removeItem(LOCAL_STORAGE_KEYS.UserLastActivityTime);\r\n        this.oauthService.revokeTokenAndLogout();\r\n      }\r\n    }\r\n    if (!this.oauthService.hasValidAccessToken() || !this.oauthService.hasValidIdToken()) {\r\n      if (localStorage) {\r\n        localStorage.removeItem(LOCAL_STORAGE_KEYS.UserLastActivityTime);\r\n      }\r\n      this.oauthService.revokeTokenAndLogout();\r\n    }\r\n    \r\n    this.getFeatures();\r\n    //\r\n    // Note: Dynamic loading \"Information Exchange\", \"Information Exchange Import\", \"Dashboard\" menus to side bar if current tenant is CA portal.\r\n    //\r\n    var isCa =\r\n      this.configService.getFeature('SearchService.CASearch') == 'true';\r\n\r\n    if (isCa) {\r\n      this.titleService.setTitle(\r\n        'Economic Substance Competent Authority Portal'\r\n      );\r\n      this.routes.add([\r\n        {\r\n          path: 'es-info-exchange',\r\n          name: ComponentNames.ES_INFORMATION_EXCHANGE,\r\n          iconClass: 'fas fa-sync',\r\n          order: 3,\r\n          layout: eLayoutType.application,\r\n          requiredPolicy: Permissions.DASHBOARD_INFORMATION_EXCHANGE,\r\n        } as ABP.Route,\r\n        /** Information Exchange File Import for CA portal.  */\r\n        {\r\n          path: 'es-info-exchange/information-exchange-import',\r\n          name: ComponentNames.ES_INFORMATION_EXCHANGE_IMPORT,\r\n          iconClass: 'fas fa-file',\r\n          order: 4,\r\n          layout: eLayoutType.application,\r\n          requiredPolicy: Permissions.INFORMATION_EXCHANGE_IMPORT_FIle,\r\n        } as ABP.Route,\r\n\r\n        /* Dashboard for CA portal. */\r\n        {\r\n          path: 'dashboard',\r\n          name: ComponentNames.ES_DASHBOARD,\r\n          iconClass: 'fas fa-chart-line',\r\n          order: 5,\r\n          layout: eLayoutType.application,\r\n          requiredPolicy: Permissions.DASHBOARD_MONITORING_DASHBOARD,\r\n        } as ABP.Route,\r\n\r\n        /* ES assessment list for CA portal. */\r\n        {\r\n          path: 'es-assessment-list',\r\n          name: ComponentNames.ES_ASSESSMENT_LIST,\r\n          iconClass: 'fas fa-list-alt',\r\n          order: 5,\r\n          layout: eLayoutType.application,\r\n          requiredPolicy: Permissions.CAPORTAL_ASSIGNMENT_LIST_VIEW,\r\n        } as ABP.Route,\r\n\r\n        /* Red Flag Settings for CA portal. */\r\n        {\r\n          path: 'redflags',\r\n          name: ComponentNames.ES_REDFLAGS,\r\n          iconClass: 'fas fa-flag',\r\n          order: 5,\r\n          layout: eLayoutType.application,\r\n          requiredPolicy: Permissions.CAPORTAL_REDFLAG_VIEW,\r\n        } as ABP.Route,\r\n\r\n        /* Audit Trail for CA portal. */\r\n        {\r\n          path: 'audit-trail',\r\n          name: ComponentNames.ES_AUDIT_TRAIL,\r\n          iconClass: 'fas fa-clipboard-list',\r\n          \r\n          order: 5,\r\n          layout: eLayoutType.application,\r\n          requiredPolicy: Permissions.AUDIT_TRAIL_DEFAULT,\r\n        } as ABP.Route,\r\n      ]);\r\n    } else {\r\n      //Only show ES import for RA Portal\r\n      this.titleService.setTitle('Economic Substance Registered Agent Portal');\r\n      this.routes.add([\r\n        {\r\n          path: 'es-import',\r\n          name: ComponentNames.ES_IMPORT,\r\n          iconClass: 'fas fa-file',\r\n          order: 3,\r\n          layout: eLayoutType.application,\r\n          requiredPolicy: Permissions.DECLARATION_IMPORT,\r\n        } as ABP.Route,\r\n        {\r\n          path: '/compliance-email',\r\n          name: ComponentNames.ES_COMPLIANCE_EMAILS,\r\n          parentName: '::Administration',\r\n          layout: eLayoutType.application,\r\n          requiredPolicy: Permissions.ABPIDENTITY_USERS,\r\n        } as ABP.Route,\r\n        /* Audit Trail for RA portal. Note: Hide it before deliver RA portals to production. */\r\n        {\r\n          path: 'audit-trail',\r\n          name: ComponentNames.ES_AUDIT_TRAIL,\r\n          iconClass: 'fas fa-clipboard-list',\r\n          order: 5,\r\n          layout: eLayoutType.application,\r\n          requiredPolicy: Permissions.AUDIT_TRAIL_DEFAULT,\r\n        } as ABP.Route,\r\n      ]);\r\n    }\r\n  }\r\n\r\n  private replaceAbpComponents(): void {\r\n    /** Header component */\r\n    this.replaceableComponents.add({\r\n      component: BreadcrumbComponent,\r\n      key: eThemeLeptonXComponents.Breadcrumb,\r\n    });\r\n\r\n    /** Right sidebar > user */\r\n    this.replaceableComponents.add({\r\n      component: BdoToolbarContainerComponent,\r\n      key: eThemeLeptonXComponents.Toolbar,\r\n    });\r\n\r\n    /** Right sidebar > settings (empty component) */\r\n    this.replaceableComponents.add({\r\n      component: BdoSettingsComponent,\r\n      key: eThemeLeptonXComponents.Settings,\r\n    });\r\n  }\r\n\r\n  private getFeatures(): void {\r\n    if (\r\n      this.configService.getFeature(Features.ES_SERVICE_TEMPLATES) === 'false'\r\n    ) {\r\n      this.routes.remove([ComponentNames.ES_DECLARATION_TEMPLATES]);\r\n    }\r\n  }\r\n}\r\n", "<!--\r\nTODO: NEED TO MOVE THE MATERIAL NAVIGATION TO A SEPARATE LAYOUT COMPONENT AND USE ABP'S REPLACE COMPONENT FEATURE\r\n\r\n<app-header class=\"header\"></app-header>\r\n\r\n<mat-sidenav-container>\r\n\r\n    <mat-sidenav #sidenav mode=\"side\" opened class=\"app-sidenav\">\r\n\r\n        <app-side-content></app-side-content>\r\n\r\n    </mat-sidenav>\r\n\r\n    <mat-sidenav-content class=\"main\">\r\n\r\n        <div class=\"container-wrapper\">\r\n            <div class=\"container\">\r\n                <router-outlet></router-outlet>\r\n            </div>\r\n        </div>\r\n\r\n        <footer class=\"footer\">\r\n            <p class=\"footer-content\">Copyright &copy; {{ year | date : 'yyyy' }} <a href=\"#\" target=\"_blank\">BDO Canada.</a> All rights reserved. Version: {{ version }}</p>\r\n        </footer>\r\n\r\n    </mat-sidenav-content>\r\n\r\n</mat-sidenav-container> \r\n-->\r\n<abp-loader-bar></abp-loader-bar>\r\n<abp-dynamic-layout></abp-dynamic-layout>\r\n"], "mappings": "AAIA,SAIEA,WAAW,QAEN,cAAc;AACrB,SAAgDC,kBAAkB,QAAQ,oBAAoB;AAE9F,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,qBAAqB;AAC/E,SAASC,4BAA4B,QAAQ,+CAA+C;AAG5F,SAASC,WAAW,QAAQ,2BAA2B;;;;;;;AAOvD,OAAM,MAAOC,YAAY;EAOvBC,YACmBC,qBAAmD,EACnDC,aAAiC,EACjCC,MAAqB,EACrBC,gBAAkC,EAClCC,YAA0B,EAC1BC,YAAmB;IALnB,KAAAL,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IAZxB,KAAAC,KAAK,GAAG,KAAK;IACb,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,IAAI,GAAG,IAAIC,IAAI,EAAE;IACf,KAAAC,aAAa,GAAWb,WAAW,CAAC,eAAe,CAAC;IAW3D,IAAI,CAACc,oBAAoB,EAAE;IAC3B;EACF;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,MAAM,GAAG,IAAI,CAACV,gBAAgB,CAACW,YAAY,EAAE;IAElD,IAAI,CAACP,OAAO,GAAG,GAAG,IAAI,CAACM,MAAM,CAACN,OAAO,CAACQ,MAAM,IAC1C,IAAI,CAACF,MAAM,CAACN,OAAO,CAACS,KACtB,IAAI,IAAI,CAACH,MAAM,CAACN,OAAO,CAACU,OAAO,IAAI,EAAE,EAAE;IAEvC;IACA;IACA;IACA;IACA;IACA,MAAMC,gBAAgB,GAAGC,YAAY,CAACC,OAAO,CAAC3B,kBAAkB,CAAC4B,oBAAoB,CAAC;IACtF,IAAIH,gBAAgB,EAAE;MACpB,IAAIT,IAAI,CAACa,GAAG,EAAE,GAAGC,QAAQ,CAACL,gBAAgB,CAAC,GAAG,IAAI,CAACR,aAAa,GAAG,IAAI,EAAE;QACvE;QACAS,YAAY,CAACK,UAAU,CAAC/B,kBAAkB,CAAC4B,oBAAoB,CAAC;QAChE,IAAI,CAACjB,YAAY,CAACqB,oBAAoB,EAAE;MAC1C;IACF;IACA,IAAI,CAAC,IAAI,CAACrB,YAAY,CAACsB,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAACtB,YAAY,CAACuB,eAAe,EAAE,EAAE;MACpF,IAAIR,YAAY,EAAE;QAChBA,YAAY,CAACK,UAAU,CAAC/B,kBAAkB,CAAC4B,oBAAoB,CAAC;MAClE;MACA,IAAI,CAACjB,YAAY,CAACqB,oBAAoB,EAAE;IAC1C;IAEA,IAAI,CAACG,WAAW,EAAE;IAClB;IACA;IACA;IACA,IAAIC,IAAI,GACN,IAAI,CAAC5B,aAAa,CAAC6B,UAAU,CAAC,wBAAwB,CAAC,IAAI,MAAM;IAEnE,IAAID,IAAI,EAAE;MACR,IAAI,CAACxB,YAAY,CAAC0B,QAAQ,CACxB,+CAA+C,CAChD;MACD,IAAI,CAAC7B,MAAM,CAAC8B,GAAG,CAAC,CACd;QACEC,IAAI,EAAE,kBAAkB;QACxBC,IAAI;QACJC,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,CAAC;QACRC,MAAM;QACNC,cAAc;OACF,EACd;MACA;QACEL,IAAI,EAAE,8CAA8C;QACpDC,IAAI;QACJC,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,CAAC;QACRC,MAAM;QACNC,cAAc;OACF,EAEd;MACA;QACEL,IAAI,EAAE,WAAW;QACjBC,IAAI;QACJC,SAAS,EAAE,mBAAmB;QAC9BC,KAAK,EAAE,CAAC;QACRC,MAAM;QACNC,cAAc;OACF,EAEd;MACA;QACEL,IAAI,EAAE,oBAAoB;QAC1BC,IAAI;QACJC,SAAS,EAAE,iBAAiB;QAC5BC,KAAK,EAAE,CAAC;QACRC,MAAM;QACNC,cAAc;OACF,EAEd;MACA;QACEL,IAAI,EAAE,UAAU;QAChBC,IAAI;QACJC,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,CAAC;QACRC,MAAM;QACNC,cAAc;OACF,EAEd;MACA;QACEL,IAAI,EAAE,aAAa;QACnBC,IAAI;QACJC,SAAS,EAAE,uBAAuB;QAElCC,KAAK,EAAE,CAAC;QACRC,MAAM;QACNC,cAAc;OACF,CACf,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACjC,YAAY,CAAC0B,QAAQ,CAAC,4CAA4C,CAAC;MACxE,IAAI,CAAC7B,MAAM,CAAC8B,GAAG,CAAC,CACd;QACEC,IAAI,EAAE,WAAW;QACjBC,IAAI;QACJC,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,CAAC;QACRC,MAAM;QACNC,cAAc;OACF,EACd;QACEL,IAAI,EAAE,mBAAmB;QACzBC,IAAI;QACJK,UAAU,EAAE,kBAAkB;QAC9BF,MAAM;QACNC,cAAc;OACF,EACd;MACA;QACEL,IAAI,EAAE,aAAa;QACnBC,IAAI;QACJC,SAAS,EAAE,uBAAuB;QAClCC,KAAK,EAAE,CAAC;QACRC,MAAM;QACNC,cAAc;OACF,CACf,CAAC;IACJ;EACF;EAEQ3B,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAACX,qBAAqB,CAACgC,GAAG,CAAC;MAC7BQ,SAAS,EAAE7C,mBAAmB;MAC9B8C,GAAG;KACJ,CAAC;IAEF;IACA,IAAI,CAACzC,qBAAqB,CAACgC,GAAG,CAAC;MAC7BQ,SAAS,EAAE5C,4BAA4B;MACvC6C,GAAG;KACJ,CAAC;IAEF;IACA,IAAI,CAACzC,qBAAqB,CAACgC,GAAG,CAAC;MAC7BQ,SAAS,EAAE9C,oBAAoB;MAC/B+C,GAAG;KACJ,CAAC;EACJ;EAEQb,WAAWA,CAAA;IACjB,IACE,IAAI,CAAC3B,aAAa,CAAC6B,UAAU,2DAA+B,KAAK,OAAO,EACxE;MACA,IAAI,CAAC5B,MAAM,CAACwC,MAAM,CAAC,uEAAyC,CAAC;IAC/D;EACF;;;uBA/KW5C,YAAY,EAAA6C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,4BAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAE,kBAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAG,aAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,KAAA;IAAA;EAAA;;;YAAZxD,YAAY;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCMzBjB,EADA,CAAAmB,SAAA,qBAAiC,yBACQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}