<div class="bdo-table-container table-responsive">
    <mat-table
        #bdotable
        class="row-hoverable"
        [class]="id"
        [dataSource]="rows"
        (scroll)="onTableScroll($event)"
        matSort
        [matSortActive]="defaultSortColumnId"
        [matSortDirection]="defaultSortOrder === 'desc' ? 'desc' : 'asc'"
        matSortDisableClear
        multiTemplateDataRows
    >
        <ng-container matColumnDef="expand" sticky>
            <mat-header-cell *matHeaderCellDef aria-label="row actions" [style.max-width]="'60px'" [style.min-width]="'60px'">&nbsp;</mat-header-cell>
            <mat-cell *matCellDef="let element" [style.max-width]="'60px'" [style.min-width]="'60px'">
                <button
                    *ngIf="!element.hideExpand && expandedElement !== element"
                    pButton
                    pRipple
                    type="button"
                    icon="rm-icon rm-icon-angle-right"
                    class="p-button-rounded p-button-text"
                    (click)="expandedElement = expandedElement === element ? null : element; toggleExpandRow(element); $event.stopPropagation()"
                ></button>
                <button
                    *ngIf="!element.hideExpand && expandedElement === element"
                    pButton
                    pRipple
                    type="button"
                    icon="rm-icon rm-icon-angle-down"
                    class="p-button-rounded p-button-text"
                    (click)="expandedElement = expandedElement === element ? null : element; $event.stopPropagation()"
                ></button>
            </mat-cell>
            <mat-footer-cell *matFooterCellDef [style.max-width]="'60px'" [style.min-width]="'60px'"></mat-footer-cell>
        </ng-container>

        <!-- Checkbox Column -->
        <ng-container matColumnDef="select" [sticky]="true">
            <mat-header-cell *matHeaderCellDef [style.max-width]="'60px'" [style.min-width]="'60px'">
                <mat-checkbox *ngIf="hasCheckboxAll" class="checkBox form-field" [(ngModel)]="isAllSelected" (change)="selectAll($event)"></mat-checkbox>
            </mat-header-cell>
            <mat-cell *matCellDef="let row" [style.max-width]="'60px'" [style.min-width]="'60px'">
                <mat-checkbox *ngIf="!row.cannotCheck" class="checkBox form-field" [(ngModel)]="row.checked" (change)="onCheckRow(row, $event)"></mat-checkbox>
            </mat-cell>
            <mat-footer-cell *matFooterCellDef [style.max-width]="'60px'" [style.min-width]="'60px'"></mat-footer-cell>
        </ng-container>

        <!-- Expanded Content Column - The detail row is made up of this one column that spans across all columns -->
        <ng-container matColumnDef="expandedDetail">
            <mat-cell *matCellDef="let element" [attr.colspan]="displayedColumns.length">
                <div class="bdo-table-expanded-element-detail" [ngClass]="element === expandedElement ? 'bdo-table-row-expanded' : 'bdo-table-row-collapsed'">
                    <ng-template
                        *ngIf="rowDetailTemplate"
                        [ngTemplateOutlet]="rowDetailTemplate.template"
                        [ngTemplateOutletContext]="{
                            row: element
                        }"
                    >
                    </ng-template>
                </div>
            </mat-cell>
        </ng-container>

        <ng-container
            *ngFor="let col of columns"
            [matColumnDef]="col.columnId"
            [sticky]="col.frozenLeft"
            [stickyEnd]="col.frozenRight || col.columnId === 'actions'"
        >
            <mat-header-cell
                *matHeaderCellDef
                [mat-sort-header]="col.columnId != 'actions' && col.isSortable ? col.sortColumnId : null"
                [disabled]="col.columnId === 'actions' || !col.isSortable ? true : false"
                [style.width]="col.width ? col.width + 'px' : 'auto'"
                [style.min-width]="col.minWidth ? col.minWidth + 'px' : '100px'"
                [style.max-width]="col.maxWidth ? col.maxWidth + 'px' : 'auto'"
                [class]="col.matHeaderClass"
                ><div class="multi-line" [class]="col.class" [innerHtml]="col.columnName"></div
            ></mat-header-cell>
            <mat-cell
                *matCellDef="let row"
                [class]="getCell(row, col.columnId)?.class"
                [style.width]="col.width ? col.width + 'px' : 'auto'"
                [style.min-width]="col.minWidth ? col.minWidth + 'px' : '100px'"
                [style.max-width]="col.maxWidth ? col.maxWidth + 'px' : 'auto'"
            >
                <!--String display-->
                <div *ngIf="col.type === BdoTableColumnType.String" [ngClass]="getStatusClass(row, col.columnId)" >{{ getCell(row, col.columnId).value }}</div>

                <!--Number display-->
                <div *ngIf="col.type === BdoTableColumnType.Number">{{ getCell(row, col.columnId).value | number }}</div>

                <!--Currency display-->
                <div *ngIf="col.type === BdoTableColumnType.Currency">
                    {{ getCell(row, col.columnId).value }}
                </div>

                <!--Boolean display-->
                <div *ngIf="col.type === BdoTableColumnType.Boolean">
                    {{ getCell(row, col.columnId).value ? ('::General:Yes' | abpLocalization) : ('::General:No' | abpLocalization) }}
                </div>

                <!--Yes/No icon display-->
                <div *ngIf="col.type === BdoTableColumnType.YesNo">
                    <span *ngIf="!getCell(row, col.columnId).value" class="cross-icon rm-icon rm-icon-close"></span>
                    <span *ngIf="getCell(row, col.columnId).value" class="tick-icon rm-icon rm-icon-check"></span>
                </div>

                <!--Date display-->
                <div *ngIf="col.type === BdoTableColumnType.Date" [ngClass]="getStatusClass(row, col.columnId)">{{ getCell(row, col.columnId)?.value | date:'dd/MM/yyyy':'local' }}</div>

                <!--Date/Time display-->
                <div *ngIf="col.type === BdoTableColumnType.DateTime" [ngClass]="getStatusClass(row, col.columnId)">{{ getCell(row, col.columnId)?.value | date:'dd/MM/yyyy HH:mm':'local' }}</div>

                <!--File Size display-->
                <div *ngIf="col.type === BdoTableColumnType.FileSize">
                    {{ getCell(row, col.columnId).value }}
                </div>

                <!--Link display-->
                <div *ngIf="col.type === BdoTableColumnType.Link" (click)="$event.stopPropagation()">
                    <a
                        class="bdo-table-cell-link"
                        (click)="linkClick(row, col.columnId, $event)"
                        target="_blank"
                        [innerHTML]="getCell(row, col.columnId).value"
                        *ngIf="!getCell(row, col.columnId).isTextInsteadOfLink"
                    ></a>
                    <div *ngIf="col.type === BdoTableColumnType.Link && getCell(row, col.columnId).isTextInsteadOfLink">{{ getCell(row, col.columnId).value }}</div>
                </div>

                <!-- Link Array -->
                <div *ngIf="col.type === BdoTableColumnType.LinkArray"  (click)="$event.stopPropagation()">
                    <div *ngFor="let child of getCell(row, col.columnId).value; index as i">
                        <a
                            class="bdo-table-cell-link"
                            (click)="linkClick(row,col.columnId, $event,i)"
                            target="_blank"
                            *ngIf="!getCell(row, col.columnId).isTextInsteadOfLink"
                        >{{ child }}</a>   
                    </div>
                </div>

                <!--List display-->
                <div *ngIf="col.type === BdoTableColumnType.Array">
                    <div *ngFor="let child of getCell(row, col.columnId).value">{{ child }}</div>
                </div>

                <!-- Checkbox display -->
                <div *ngIf="col.type === BdoTableColumnType.Checkbox && !getCell(row, col.columnId).hide" (click)="$event.stopPropagation()">
                    <mat-checkbox class="checkBox form-field" [(ngModel)]="getCell(row, col.columnId).value" (change)="onCheckRow(row, $event)"></mat-checkbox>
                </div>

                <!-- HTML display -->
                <div *ngIf="col.type === BdoTableColumnType.Html" [innerHTML]="getCell(row, col.columnId).value"></div>

                <!-- Actions List -->
                <div *ngIf="col.type === BdoTableColumnType.Actions && getCell(row, col.columnId).value" (click)="$event.stopPropagation()">
                    <a
                        *ngFor="let action of getCell(row, col.columnId).value"
                        class="bdo-table-cell-action-link mr-2"
                        [title]="action.tooltip"
                        (click)="actionClick(action.actionType, row.id, row, $event)"
                    >
                        <span class="{{ action.icon }}"></span>
                    </a>
                </div>

                <!-- Single Action button -->
                <div *ngIf="col.type === BdoTableColumnType.SingleActionButton && getCell(row, col.columnId).value" (click)="$event.stopPropagation()">
                    <button mat-raised-button class="bdo-table-single-action-button" 
                    (click)="actionClick(getCell(row, col.columnId).value.actionType, row.id, row, null)"
                    matTooltip= "{{getCell(row, col.columnId).value.tooltip}}"
                    >
                        <mat-icon class="bdo-table-single-action-button-icon">{{getCell(row, col.columnId).value.icon}}</mat-icon>
                    </button>
                </div>

                <!-- 3 dot action button -->
                <div *ngIf="col.type === BdoTableColumnType.ThreeDotActions && getCell(row, col.columnId).value" (click)="$event.stopPropagation()">                   
                    <button mat-icon-button [matMenuTriggerFor]="menu">
                        <!-- <span class="rm-icon rm-icon-more-horiz"></span> -->
                        <mat-icon class="rm-icon rm-icon-more-horiz">more_vert</mat-icon>
                    </button>
                    <mat-menu #menu="matMenu">
                        <div *ngFor="let item of getCell(row, col.columnId).value">
                            <button *ngIf = "hideActionButton(item, row)" mat-menu-item (click)="actionClick(item.actionType, row.id, row, null)">
                                {{item.displayName}}
                                <mat-icon>{{item.icon}}</mat-icon>
                            </button>
                        </div>
                    </mat-menu>
                </div>
            </mat-cell>

            <mat-footer-cell
                *matFooterCellDef
                [style.width]="col.width ? col.width + 'px' : 'auto'"
                [style.min-width]="col.minWidth ? col.minWidth + 'px' : '100px'"
                [style.max-width]="col.maxWidth ? col.maxWidth + 'px' : 'auto'"
            >
                <div [innerHTML]="getFooterCell(col.columnId)?.value"></div>
            </mat-footer-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></mat-header-row>

        <mat-row
            *matRowDef="let element; columns: displayedColumns"
            class="bdo-table-expanded-element-row"
            (click)="rowClick(element)"
            [class]="element.class"
            [class.bdo-table-row-selectable]="rowSelectable"
            [class.bdo-table-row-selected]="selectedRow === element"
            [class.example-expanded-row]="expandedElement === element"
            (click)="expandedElement = expandedElement === element ? null : element"
        ></mat-row>
        <mat-row *matRowDef="let row; columns: ['expandedDetail']" class="bdo-table-expanded-detail-row"></mat-row>

        <mat-footer-row
            *matFooterRowDef="displayedColumns; sticky: true"
            [style.display]="footerCells && footerCells.length > 0 ? 'flex' : 'none'"
        ></mat-footer-row>
        <ng-template *ngIf="footerTemplate" [ngTemplateOutlet]="footerTemplate.template" [ngTemplateOutletContext]="{}"> </ng-template>
    </mat-table>
    <div *ngIf="isVirtualScroll" class="bdo-table-virtual-scroll-paginator-footer p-1 pb-0 ml-0 mr-0 grid">
        <!-- TODO: Figure out what the commented code is for and fix the localization, right now it just displays ::ShowingOfTotalEntriesWithParameters -->
        <!-- <div class="col pt-2 text-right">
            {{
                '::ShowingOfTotalEntriesWithParameters'
                    | abpLocalization : (totalRecords > 0 ? 1 : 0).toString() : rows.length.toString() : (totalRecords || 0).toString()
            }}
        </div> -->
    </div>
    <mat-paginator
        [length]="totalRecords"
        [pageSize]="pageSize"
        class="mat-paginator-sticky"
        [pageSizeOptions]="pageSizeOptions"
        [disabled]="isVirtualScroll || hidePagination"
        [hidePageSize]="isVirtualScroll || hidePagination"
        [showFirstLastButtons]="!hidePagination"
        [pageIndex]="pageIndex"
        [style.display]="footerHeight === 0 || hidePagination || isVirtualScroll ? 'none' : 'block'"
        sticky
    ></mat-paginator>
</div>
