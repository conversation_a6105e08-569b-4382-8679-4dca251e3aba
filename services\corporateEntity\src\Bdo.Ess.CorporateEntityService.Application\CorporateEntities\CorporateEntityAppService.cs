﻿using Bdo.Ess.CorporateEntityService.Encryption;
using Bdo.Ess.CorporateEntityService.Integration;
using Bdo.Ess.DashboardService;
using Bdo.Ess.EconomicSubstanceService.Declarations;
using Bdo.Ess.SaasService.Tenants;
using Bdo.Ess.Shared.Constants.Blob;
using Bdo.Ess.Shared.Constants.Saas;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.CorporateEntity;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.DataSync;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Tenants;
using Bdo.Ess.Shared.Utility.Exensions;
using Bdo.Ess.Shared.Utility.Extensions;

using KellermanSoftware.CompareNetObjects;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

using Volo.Abp;
using Volo.Abp.BlobStoring;
using Volo.Abp.Content;
using Volo.Abp.Data;
using Volo.Abp.DistributedLocking;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Uow;
using Volo.Saas.Host;

namespace Bdo.Ess.CorporateEntityService.CorporateEntities
{
	public class CorporateEntityAppService : CorporateEntityServiceAppService, ICorporateEntityAppService
	{
		private readonly ICorporateEntityRepository _corporateEntityRepository;
		private readonly IDataFilter _dataFilter;
		private readonly IBlobContainer<CorporateEntityContainer> _fileContainer;
		private readonly ILookupDataProvider _lookupDataProvider;
		private readonly IDeclarationAppService _declarationAppService;
		private readonly IEssTenantAppService _tenantAppService;
		private readonly IUnitOfWorkManager _unitOfWorkManager;
		private readonly IDistributedEventBus _distributedEventBus;
		private readonly ILogger<CorporateEntityAppService> _logger;
		private readonly IAbpDistributedLock _distributedLock;
        private readonly ICorporateEntityEncryptionManager _entityEncryptionManager;
        private readonly ITenantAppService _abpTenantAppService;
		private readonly IDashboardAppService _dashboardAppService;
		private static readonly TimeSpan SingleLockWait = TimeSpan.FromMinutes(1);

		public CorporateEntityAppService(
			ICorporateEntityRepository corporateEntityRepository,
			IDataFilter dataFilter,
			IBlobContainer<CorporateEntityContainer> fileContainer,
			ILookupDataProvider lookupDataProvider,
			IUnitOfWorkManager unitOfWorkManager,
			IDistributedEventBus distributedEventBus,
			IEssTenantAppService tenantAppService,
			ITenantAppService abpTenantAppService,
			IDashboardAppService dashboardAppService,
			IDeclarationAppService declarationAppService,
			IAbpDistributedLock distributedLock,
            ICorporateEntityEncryptionManager entityEncryptionManager,
            ILogger<CorporateEntityAppService> logger)
		{
			_corporateEntityRepository = corporateEntityRepository;
			_dataFilter = dataFilter;
			_fileContainer = fileContainer;
			_lookupDataProvider = lookupDataProvider;
			_tenantAppService = tenantAppService;
			_unitOfWorkManager = unitOfWorkManager;
			_distributedEventBus = distributedEventBus;
			_abpTenantAppService = abpTenantAppService;
			_dashboardAppService = dashboardAppService;
			_distributedLock = distributedLock;
            _entityEncryptionManager = entityEncryptionManager;
            _declarationAppService = declarationAppService;
			_logger = logger;
		}

		public async Task<bool> DeleteByUniqueIdAsync(string uniqueId, string raName)
		{
			Guid? tenantId = await GetTenantId(raName);
			var entity = await GetEntityByUniqueIdAsync(uniqueId, tenantId, false);
			//Already soft deleted or not found
			if (entity == null)
			{
				return false;
			}
			await _corporateEntityRepository.DeleteAsync(entity, true);
			return true;
		}

		public async Task<CorporateEntityDto> GetAsync(Guid id, bool includeSoftDelete = false)
		{
			CorporateEntity entity = await GetEntityAsync(id, includeSoftDelete);

			return ObjectMapper.Map<CorporateEntity, CorporateEntityDto>(entity);
		}

		[UnitOfWork(IsDisabled = true)]
		public async Task<List<BlobResultDto>> GetCABusinessLicenseList(Guid EntityId)
		{
			//Get List do not fetch file content
			using (_dataFilter.Disable<IMultiTenant>())
			{
				return await GetBusinessLicenseList(EntityId);
			}
		}

		[UnitOfWork(IsDisabled = true)]
		public async Task<BlobResultDto> GetCABusinessLicense(Guid EntityId, string fileName)
		{
			var dto = new BlobResultDto();
			//Get List do not fetch file content
			using (_dataFilter.Disable<IMultiTenant>())
			{
				var result = await GetBusinessLicenseUrls(EntityId);
				var urls = result.Urls;
				var tenantId = result.TenantId;
				if (urls == null || urls.Length == 0) return dto;

				if (tenantId.HasValue)
				{
					using (CurrentTenant.Change(tenantId.Value))
					{
						return await GetBusinessLicenseContent(urls, EntityId, fileName);
					}
				}
				return dto;
			}
		}

		[UnitOfWork(IsDisabled = true)]
		public async Task DeleteBusinessLicense(Guid EntityId, string fileName)
		{
			var urlToDelete = "";
			using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
			{
				var entity = await _corporateEntityRepository.GetAsync(EntityId);
				if (entity == null)
				{
					return;
				}
				fileName = fileName.NormalizeBlobName();
				fileName = Path.GetFileName(fileName);
				var urls = entity.UploadedBusinessLicenseUrl;
				if (urls == null || urls.Length == 0) return;

				urlToDelete = FindBusinessLicenseFileUrl(urls, fileName);
				entity.UploadedBusinessLicenseUrl = urls.Where(x => x != urlToDelete).ToArray();
				await _corporateEntityRepository.UpdateAsync(entity, true);
				await uow.CompleteAsync();
			}
			if (!string.IsNullOrWhiteSpace(urlToDelete))
			{
				await _fileContainer.DeleteAsync(urlToDelete);
			}
		}

		private static string FindBusinessLicenseFileUrl(string[] urls, string fileName)
		{
			//fileName is full path
			if (fileName.Contains('/') || fileName.Contains('\\')) return fileName;

			fileName = fileName.NormalizeBlobName();
			fileName = Path.GetFileName(fileName);
			foreach (var url in urls)
			{
				var fullName = Path.GetFileName(url);
				if (fullName.Equals(fileName, StringComparison.InvariantCultureIgnoreCase))
				{
					return url;
				}
			}
			return "";
		}

		[UnitOfWork(IsDisabled = true)]
		public async Task<BlobResultDto> GetBusinessLicense(Guid EntityId, string fileName)
		{
			var dto = new BlobResultDto();
			string[]? urls = (await GetBusinessLicenseUrls(EntityId)).Urls;
			if (urls == null || urls.Length == 0) return dto;

			var fileUrl = FindBusinessLicenseFileUrl(urls, fileName);
			return await GetBusinessLicenseContent(urls, EntityId, fileName);
		}

		[UnitOfWork(IsDisabled = true)]
		public async Task<List<BlobResultDto>> GetBusinessLicenseList(Guid EntityId)
		{
			throw new UserFriendlyException("User Friendly message: Please use GetCABusinessLicenseList instead");
            var dtoList = new List<BlobResultDto>();
			string[]? urls = (await GetBusinessLicenseUrls(EntityId)).Urls;
			if (urls == null || urls.Length == 0) return dtoList;

			foreach (var url in urls)
			{
				var fullName = url.Replace('/', '_').Replace('\\', '_');

				dtoList.Add(new BlobResultDto
				{
					EntityId = EntityId,
					Name = $"{fullName}",
					//Content = base64Content
				});
			}
			return dtoList;
		}

		private async Task<BlobResultDto> GetBusinessLicenseContent(string[]? urls, Guid entityId, string fileName)
		{
			var dto = new BlobResultDto();
			if (urls == null || urls.Length == 0) return dto;

			var fileUrl = FindBusinessLicenseFileUrl(urls, fileName);
			if (string.IsNullOrWhiteSpace(fileUrl)) return dto;

			//Azure Blob guid is lower case
			var blob = await _fileContainer.GetAllBytesAsync(fileUrl);
			var base64Content = Convert.ToBase64String(blob);

			return new BlobResultDto
			{
				EntityId = entityId,
				Name = $"{fileUrl.Replace('/', '_').Replace('\\', '_')}",
				Content = base64Content
			};
		}

		private async Task<(string[]? Urls, Guid? TenantId)> GetBusinessLicenseUrls(Guid entityId)
		{
			string[]? urls = null;
			Guid? tenantId = null;
			using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
			{
				var entity = await _corporateEntityRepository.GetAsync(entityId);
				if (entity == null)
				{
					return (null, null);
				}
				urls = entity.UploadedBusinessLicenseUrl;
				tenantId = entity.TenantId;
				await uow.CompleteAsync();
			}
			return (urls, tenantId);
		}

		public async Task<bool> HardDeleteByUniqueIdAsync(string uniqueId, string raName)
		{
			//TODO: BOSS message should contain a RA Name
			Guid? tenantId = await GetTenantId(raName);
			var entity = await GetEntityByUniqueIdAsync(uniqueId, null, true);
			if (entity == null)
			{
				return false;
			}
			await _corporateEntityRepository.HardDeleteAsync(entity, true);
			//Since Eto has no BO, there is no need to decrypt entity for Eto
            var deleted = ObjectMapper.Map<CorporateEntity, CorporateEntityDeletedEto>(entity);
			await _distributedEventBus.PublishAsync(deleted);

			return true;
		}

		//This is the API for developer insert data directly to database
		public async Task<Guid> SaveByEntityUniqueIdAsync(CorporateEntityDto input)
		{
			var entity = await GetEntityByUniqueIdAsync(input.EntityUniqueId, input.TenantId, true);
			using (CurrentTenant.Change(input.TenantId))
			{
				if (entity == null)
				{
					//The Mapping should ignore Id mapping
					entity = ObjectMapper.Map<CorporateEntityDto, CorporateEntity>(input);
					entity = _entityEncryptionManager.EncryptEntity(entity);
					//Repositry code will trigger sync
                    entity = await _corporateEntityRepository.InsertAsync(entity, true);
				}
				else
				{
					var existingData = entity.Clone();
					ObjectMapper.Map<CorporateEntityDto, CorporateEntity>(input, entity);
					
                    var compareLogic = GetCompareLogic();
					var compareResult = compareLogic.Compare(existingData, entity);
					if (!compareResult.AreEqual)
					{
                        entity = _entityEncryptionManager.EncryptEntity(entity);
                        entity = await _corporateEntityRepository.UpdateAsync(entity, true);
                        if (existingData!.IsIndexRelatedDataChanged(entity))
						{
							await SyncToSearchService(entity);
						}
					}
				}
			}
			return entity.Id;
		}

		private async Task SyncToSearchService(CorporateEntity entity)
		{
			var updated = ObjectMapper.Map<CorporateEntity, CorporateEntityUpdatedEto>(entity);
			await _distributedEventBus.PublishAsync(updated);

			var toSync = ObjectMapper.Map<CorporateEntity, CorporateEntityDataSyncDto>(entity);
			await _distributedEventBus.PublishAsync(toSync, onUnitOfWorkComplete: true, useOutbox: true);
		}

		private static void SetDateFieldsTimeZone(ref CorporateEntity entity)
		{
			if (entity.IncorporationDate.HasValue)
			{
				entity.IncorporationDate = entity.IncorporationDate.Value.LocalTimeToUtc(TenantConstants.TenantTimeZone);
			}

			if (entity.DissolvedDate.HasValue)
			{
				entity.DissolvedDate = entity.DissolvedDate.Value.LocalTimeToUtc(TenantConstants.TenantTimeZone);
			}

			if (entity.MergedDate.HasValue)
			{
				entity.MergedDate = entity.MergedDate.Value.LocalTimeToUtc(TenantConstants.TenantTimeZone);
			}

			if (entity.StatusDate.HasValue)
			{
				entity.StatusDate = entity.StatusDate.Value.LocalTimeToUtc(TenantConstants.TenantTimeZone);
			}

			if (entity.ContinuationOutDate.HasValue)
			{
				entity.ContinuationOutDate = entity.ContinuationOutDate.Value.LocalTimeToUtc(TenantConstants.TenantTimeZone);
			}

			if (entity.ResignedDate.HasValue)
			{
				entity.ResignedDate = entity.ResignedDate.Value.LocalTimeToUtc(TenantConstants.TenantTimeZone);
			}

			if (entity.LicenseDate.HasValue)
			{
				entity.LicenseDate = entity.LicenseDate.Value.LocalTimeToUtc(TenantConstants.TenantTimeZone);
			}
		}

        public async Task<bool> CreateTenantAsync(CreateTenantEto input)
        {
            try
            {
                if (input.Users.Count is < 1 or > 2)
                {
                    _logger.LogError("Failed to Create Tenant for {NormalizedName}", input.NormalizedName);
                    return false;
                }

                var isSaasTenantExists = (await _tenantAppService.GetAllTenantsForDashboard()).Any(s => s.TenantName == input.RACode);
                var isRACodeExists = await _dashboardAppService.isRACodeexists(input.RACode);

                if (isRACodeExists || isSaasTenantExists)
                {
                    _logger.LogError("Failed to Create Tenant for {NormalizedName}, Reason already exists", input.NormalizedName);
                    return false;
                }

                // creates a new saas tenant, abp text templates, users, roles, permissions, etc.
                var tenantId = await _tenantAppService.CreateNewTenantPackage(input.RACode, true, input);
                var saasTenant = await _tenantAppService.GetTenantById(tenantId);

                _logger.LogInformation("Creating Tenant for {NormalizedName} in dashboard", input.NormalizedName);
                await _dashboardAppService.InsertTenant(tenantId, input.RACode, input.NormalizedName, saasTenant.Edition);

                _logger.LogInformation("Succeeded in creating tenant, users and text templates {NormalizedName}", input.NormalizedName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while creating tenant for {NormalizedName}", input.NormalizedName);
                _logger.LogError("Failed to Create Tenant for {NormalizedName}", input.NormalizedName);
                return false;
            }
        }

		private static CompareLogic GetCompareLogic()
		{
			CompareLogic compare = new CompareLogic();

			//Clone method can not copy Id, since Id is read only
			compare.Config.IgnoreProperty<CorporateEntity>(x => x.Id);

			compare.Config.IgnoreProperty<CorporateEntity>(x => x.CreationTime);
			compare.Config.IgnoreProperty<CorporateEntity>(x => x.CreatorId);
			compare.Config.IgnoreProperty<CorporateEntity>(x => x.LastModificationTime);
			compare.Config.IgnoreProperty<CorporateEntity>(x => x.LastModifierId);
			//When entity type is 'N/A', then type Id is null, but existing has Guid.Default
			compare.Config.IgnoreProperty<CorporateEntity>(x => x.EntityTypeId);
			compare.Config.TreatStringEmptyAndNullTheSame = true;
			//Return false when see first difference
			compare.Config.MaxDifferences = 1;
			compare.Config.IgnoreCollectionOrder = true;
			return compare;
		}

		public async Task<int> SaveFromBossAsync(CorporateEntityBossListDto inputList)
		{
			var count = 0;
			if (inputList.Entities == null) return 0;

			Guid? tenantId = await GetTenantId(inputList.RaName);
			if (tenantId == null) return 0;
			inputList.TenantId = tenantId;

			var compareLogic = GetCompareLogic();

			using (CurrentTenant.Change(tenantId))
			{
				var allStatuses = await _lookupDataProvider.GetEntityStatusListAsync();
				var allTypes = await _lookupDataProvider.GetEntityTypeListAsync();

				foreach (var input in inputList.Entities)
				{
					try
					{
						await using var handle = await _distributedLock.TryAcquireAsync($"SingleEntity{input.EntityUniqueId}{tenantId}", SingleLockWait);
						if (handle == null)
						{
                            _logger.LogError("Aquire Lock Failed - Entity Sync from BOSS {EntityUniqueId}{TenantId}", input.EntityUniqueId, tenantId);
                            continue;
						}
						var isNew = false;
						CorporateEntity? entity = null;
						var addressChanged = false;
						Address newAddress = null;
						CorporateEntity existingData = null;
						if (!string.IsNullOrEmpty(input.OldEntityUniqueId) && input.OldEntityUniqueId != input.EntityUniqueId)
						{
							entity = await GetEntityByUniqueIdAsync(input.OldEntityUniqueId, tenantId, true);
						}
						else
						{
							entity = await GetEntityByUniqueIdAsync(input.EntityUniqueId, tenantId, true);
						}

						if (entity == null)
						{
							//The Mapping should ignore Id mapping
							entity = ObjectMapper.Map<CorporateEntityBossDto, CorporateEntity>(input);
							isNew = true;
						}
						else
						{
							existingData = entity.Clone();
							if (entity.RegisteredOfficeAddress != null && input?.RegisteredOfficeAddress != null)
							{
								if (!(entity.RegisteredOfficeAddress.AddressLine1 == input.RegisteredOfficeAddress.AddressLine1 &&
									entity.RegisteredOfficeAddress.AddressLine2 == input.RegisteredOfficeAddress.AddressLine2 &&
									entity.RegisteredOfficeAddress.Country == input.RegisteredOfficeAddress.Country))
								{
									addressChanged = true;
									newAddress = new Address();
									newAddress.AddressLine1 = input.RegisteredOfficeAddress.AddressLine1;
									newAddress.AddressLine2 = input.RegisteredOfficeAddress.AddressLine2;
									var targetCountry = await _lookupDataProvider.FindCountryByNameAsyc(input.RegisteredOfficeAddress.Country);
									if (targetCountry != null)
									{
										newAddress.Country = targetCountry.Id.ToString();
									}
								}
							}
							_ = ObjectMapper.Map<CorporateEntityBossDto, CorporateEntity>(input, entity);
						}

						var statusId = allStatuses.FirstOrDefault(x => x.Name == input.EntityStatus);
						if (statusId != null)
						{
							entity.StatusId = statusId.Id;
						}

						var typeId = allTypes.FirstOrDefault(x => x.Name == input.EntityType);
						if (typeId != null)
						{
							entity.EntityTypeId = typeId.Id;
						}

						entity.TenantId = tenantId;
						entity.IsDeleted = input.IsDeleted; //Soft delete is via Flag

						if (entity != null) { SetDateFieldsTimeZone(ref entity); }

						if (isNew)
						{
							entity = _entityEncryptionManager.EncryptEntity(entity);
                            await _corporateEntityRepository.InsertAsync(entity, true);
						}
						else
						{
                            var compareResult = compareLogic.Compare(existingData, entity);
							if (!compareResult.AreEqual)
							{
                                entity = _entityEncryptionManager.EncryptEntity(entity);
                                entity = await _corporateEntityRepository.UpdateAsync(entity, true);

								if (addressChanged && newAddress != null)
								{
									var result = await _declarationAppService.UpdateDeclarationByEntityId(entity.Id, newAddress);
								}

                                if (existingData!.IsIndexRelatedDataChanged(entity))
								{
									await SyncToSearchService(entity);
								}
							}
						}

						_logger.LogInformation($"Saved Corporate Entity: RA: {inputList.RaName}, Entity Name: {entity.Name}, LicenseDate: {entity.LicenseDate}");

						count++;
					}
					catch (Exception ex)
					{
						_logger.LogError(ex, $"Failed to Sync From Boss EntityUnqiueId {input.EntityUniqueId}");
						if (inputList.Entities.Count <= 1)
						{
							throw; //Only throw exception if there is only one entity, if it's a batch update, update as many as possible
						}
					}
				}
			}

			return count;
		}

		[UnitOfWork(IsDisabled = true)]
		public async Task UploadBusinessLicense(IFormFile file, Guid entityId)
		{
			var fullName = Path.Combine(BlobContainerConsts.BusinessLicenseFolderName, entityId.ToString().ToLower(), file.FileName).NormalizeBlobName();

			using (var ms = new MemoryStream())
			{
				file.CopyTo(ms);
				ms.Position = 0;
				await _fileContainer.SaveAsync(fullName, ms, true);
			}
			await AppendNewBusinessUrl(entityId, fullName);

		}

		[UnitOfWork(IsDisabled = true)]
		public async Task UploadBusinessLicense(IRemoteStreamContent file, Guid entityId)
		{
			var fullName = Path.Combine(BlobContainerConsts.BusinessLicenseFolderName, entityId.ToString().ToLower(), file.FileName ?? "").NormalizeBlobName();
			await _fileContainer.SaveAsync(fullName, file.GetStream(), true);

			await AppendNewBusinessUrl(entityId, fullName);
		}

		private async Task AppendNewBusinessUrl(Guid entityId, string fileNameWithPath)
		{
			using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
			{
				var entity = await GetEntityAsync(entityId, true, decryptData:false);
				if (entity.UploadedBusinessLicenseUrl == null || entity.UploadedBusinessLicenseUrl.Length == 0)
				{
					entity.UploadedBusinessLicenseUrl = new string[1] { fileNameWithPath };
				}
				else
				{
					bool appendNew = true;
					foreach (var url in entity.UploadedBusinessLicenseUrl)
					{
						var existingName = Path.GetFileName(url);
						if (existingName.Equals(Path.GetFileName(fileNameWithPath), StringComparison.InvariantCultureIgnoreCase))
						{
							appendNew = false;
							break;
						}
					}
					if (appendNew)
						entity.UploadedBusinessLicenseUrl = entity.UploadedBusinessLicenseUrl.Append(fileNameWithPath).ToArray();
				}

				await _corporateEntityRepository.UpdateAsync(entity, true);
				await uow.CompleteAsync();
			}
		}
		private async Task<CorporateEntity> GetEntityAsync(Guid id, bool includeSoftdelete = false, bool decryptData=true)
		{
			if (!includeSoftdelete)
			{
				if (decryptData)
				{
					return await DecryptEntity(id);
				}
				return await _corporateEntityRepository.GetAsync(id);
            }
			using (_dataFilter.Disable<ISoftDelete>())
			{
				if (decryptData)
                {
                    return await DecryptEntity(id);
                }
                return await _corporateEntityRepository.GetAsync(id);
            }
		}

		private async Task<CorporateEntity> DecryptEntity(Guid id)
        {
            var entity = await _corporateEntityRepository.GetAsync(id);
            entity = _entityEncryptionManager.DecryptEntity(entity);
            
            return entity;
        }

        public async Task<CorporateEntityDto?> GetEntityByUniqueIdAsync(string uniqueId, Guid? tenantId)
		{
			var entity = await GetEntityByUniqueIdAsync(uniqueId, tenantId, true);
			if (entity == null) return null;
			return ObjectMapper.Map<CorporateEntity, CorporateEntityDto>(entity);
		}

		private async Task<CorporateEntity?> GetEntityByUniqueIdAsync(string uniqueId, Guid? tenantId = null, bool includeSoftDelete = false)
		{
			if (string.IsNullOrWhiteSpace(uniqueId)) return null;
			using (_dataFilter.Disable<IMultiTenant>())
			{
				if (!includeSoftDelete)
				{

					var entity =  await _corporateEntityRepository.FirstOrDefaultAsync(x => x.EntityUniqueId.ToLower() == uniqueId.ToLower() && x.TenantId == tenantId);
					if (entity == null) return null;
                    entity = _entityEncryptionManager.DecryptEntity(entity);
					return entity;
                }
				using (_dataFilter.Disable<ISoftDelete>())
				{
					var entity = await _corporateEntityRepository.FirstOrDefaultAsync(x => x.EntityUniqueId.ToLower() == uniqueId.ToLower() && x.TenantId == tenantId);
                    if (entity == null) return null;
                    entity = _entityEncryptionManager.DecryptEntity(entity);
                    return entity;
                }
			}
		}

		private async Task<Guid?> GetTenantId(string raName)
		{
			try
			{
				if (!string.IsNullOrWhiteSpace(raName))
				{
					return await _tenantAppService.CreateNewTenantPackage(raName);
				}
				return null;
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, $"Failed to Create Tenant for {raName}");
				throw;
			}
		}

		public async Task<CorporateEntityDto?> GetCAEntityByName(string entityName)
		{
			//Alica says on Prod if the entityName is same across all RAs, actually it's the same entity, so found latest update one
			using (_dataFilter.Disable<IMultiTenant>())
			{
				var latestOne = (await _corporateEntityRepository.GetListAsync(x => x.Name.ToUpper() == entityName.ToUpper() && !x.IsDeleted))
					 ?.OrderByDescending(x => x.CreationTime)?.FirstOrDefault();
				if (latestOne == null) return null;
                latestOne = _entityEncryptionManager.DecryptEntity(latestOne);
                return ObjectMapper.Map<CorporateEntity, CorporateEntityDto>(latestOne);
			}
		}

		public async Task<CorporateEntityDto> GetCAEntity(Guid entityId)
		{
			return await GetCAEntityDb(entityId, true);
		}

		public async Task<CorporateEntityDto> GetCAEntityWithoutTenant(Guid entityId)
		{
			return await GetCAEntityDb(entityId, false);
		}

		private async Task<CorporateEntityDto> GetCAEntityDb(Guid entityId, bool fetchTenantName = true)
		{
			using (_dataFilter.Disable<IMultiTenant>())
			{
				using (_dataFilter.Disable<ISoftDelete>())
				{
					CorporateEntity entity = await GetEntityAsync(entityId, true);

					var dto = ObjectMapper.Map<CorporateEntity, CorporateEntityDto>(entity);
					if (entity.TenantId.HasValue && fetchTenantName)
					{
						var tenant = await _abpTenantAppService.GetAsync(entity.TenantId.Value);
						if (tenant != null)
						{
							dto.RaName = tenant.Name;
						}
					}

					return dto;
				}
			}
		}
	}
}