﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Application.Services;

namespace Bdo.Ess.CtsIntegration.Certificate
{
	public interface ICountryCertificateAppService : IApplicationService
	{
		Task<List<CountryCertificateDto>> GetAllAsync();
		//Could only update records via Cts Call
		Task<CountryCertificateDto> InsertOrUpdateAsync(IFormFile pubCertFile, string countryCode, string opsKey);
		Task InsertOrUpdateAsync(string base64Cert, string countryCode);

        Task<bool> IsEnrolledAsync(string countryCode);
		Task<CountryCertificateDto?> GetByCountryCode2Async(string countryCode);
		Task<CountryCertificateDto?> GetByCountryCode2ByCreationTimeAsync(string countryCode, DateTime creationTime);

        Task RefreshCertificates();
	}

}
