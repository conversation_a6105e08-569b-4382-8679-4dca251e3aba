<Project Sdk="Microsoft.NET.Sdk.Web">

    <Import Project="..\..\..\..\common.props" />

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <RootNamespace>Bdo.Ess.CtsIntegration</RootNamespace>
    </PropertyGroup>
	
    <ItemGroup>
        <Compile Remove="Logs\**" />
        <Content Remove="Logs\**" />
        <EmbeddedResource Remove="Logs\**" />
        <None Remove="Logs\**" />
    </ItemGroup>
	
	<ItemGroup>
      <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.1" />
      <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
      <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
      <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
      <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
      <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
      <PackageReference Include="Volo.Abp.Http.Client.IdentityModel" Version="8.2.3" />
	  <PackageReference Include="Azure.Extensions.AspNetCore.DataProtection.Blobs" Version="1.5.0" />
      <PackageReference Include="Azure.Extensions.AspNetCore.DataProtection.Keys" Version="1.5.0" />
	</ItemGroup>
	
	<ItemGroup>		
		<ProjectReference Include="..\Bdo.Ess.CtsIntegration.Application\Bdo.Ess.CtsIntegration.Application.csproj" />
		<ProjectReference Include="..\Bdo.Ess.CtsIntegration.EntityFrameworkCore\Bdo.Ess.CtsIntegration.EntityFrameworkCore.csproj" />
		<ProjectReference Include="..\Bdo.Ess.CtsIntegration.HttpApi\Bdo.Ess.CtsIntegration.HttpApi.csproj" />
		<ProjectReference Include="..\..\..\..\shared\Bdo.Ess.Shared.Hosting.Microservices\Bdo.Ess.Shared.Hosting.Microservices.csproj" />
		<ProjectReference Include="..\..\..\..\shared\Bdo.Ess.Shared.HttpApi\Bdo.Ess.Shared.HttpApi.csproj" />
	</ItemGroup>
</Project>
