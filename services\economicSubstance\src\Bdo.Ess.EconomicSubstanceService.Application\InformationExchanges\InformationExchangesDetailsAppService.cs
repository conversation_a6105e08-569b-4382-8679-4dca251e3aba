﻿using Bdo.Ess.Dtos.NJT;
using Bdo.Ess.EconomicSubstanceService.CtsIntegration;
using Bdo.Ess.EconomicSubstanceService.Encryption;
using Bdo.Ess.EconomicSubstanceService.InformationExchanges.Dtos;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Constants.CtsIntegration;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.InformationExchange;
using Bdo.Ess.Shared.Utility.Utils;
using ICSharpCode.SharpZipLib.Zip;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Users;

namespace Bdo.Ess.EconomicSubstanceService.InformationExchanges
{
    public partial class InformationExchangesDetailsAppService : EconomicSubstanceServiceAppService, IInformationExchangesDetailsAppService
    {
        private readonly IInformationExchangeDetailsRepository _informationExchangeDetailsRepository;
        private readonly IEssInformationExchangeXMLRepository _informationExchangeXMLRepository;

        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IInfoExchangeEncryptionManager _infoExchangeEncryptionManager;
        private readonly ICtsIntegrationBlobAppService _ctsIntegrationBlobAppService;
        private readonly ICurrentTenant _currentTenant;
        private readonly IDataFilter _dataFilter;
        public static readonly string BHA_ISO_CODE = "BS";
        private string StandardFormMonitoringYear = "";

        /// <summary>
        ///  Note: _auditWebInfo is scoped dependency instance, 
        ///  so, it will be shared between HttpApi and AppService
        ///  Work for Auditing purpose to get client IP address.
        /// </summary>
        private readonly IAuditWebInfo _auditWebInfo;
        private readonly IConfiguration _configuration;

        public InformationExchangesDetailsAppService(
            IInformationExchangeDetailsRepository informationExchangeDetailsRepository,
            IEssInformationExchangeXMLRepository informationExchangeXMLRepository,
            IDataFilter dataFilter,
            IDistributedEventBus distributedEventBus,
            IInfoExchangeEncryptionManager infoExchangeEncryptionManager,
            ICtsIntegrationBlobAppService ctsIntegrationBlobAppService,
            ICurrentTenant currentTenant,
            IAuditWebInfo auditWebInfo,
            IConfiguration configuration)
        {
            _configuration = configuration;
            StandardFormMonitoringYear = _configuration["InfoExchange:StandardMonitoringFromYear"] ?? "2023";
            _informationExchangeDetailsRepository = informationExchangeDetailsRepository;
            _informationExchangeXMLRepository = informationExchangeXMLRepository;
            _dataFilter = dataFilter;
            _distributedEventBus = distributedEventBus;
            _infoExchangeEncryptionManager = infoExchangeEncryptionManager;
            _ctsIntegrationBlobAppService = ctsIntegrationBlobAppService;
            _currentTenant = currentTenant;
            _auditWebInfo = auditWebInfo;
        }

        public Task<string> StandardMonitoringFromYear()
        {
            return Task.FromResult(StandardFormMonitoringYear);
        }

        public async Task<bool> UpdateInformationExchangeDetails(InformationExchangeDetailDto informationExchangeDetails)
        {
            bool result = true;
            
            try
            {
                using (_dataFilter.Disable<IMultiTenant>())
                {
                    if (informationExchangeDetails.Id != Guid.Empty)
                    {
                        InformationExchangeDetails trackedEntity = await _informationExchangeDetailsRepository.GetAsync(informationExchangeDetails.Id);
                                                                      
                        ObjectMapper.Map(informationExchangeDetails, trackedEntity);

                        // the following fields when it is null the mapper is not mapping properly we need to do it manually
                        if (informationExchangeDetails.TaxIdentificationCountry == null)
                            trackedEntity.TaxIdentificationCountry = null;

                        if (informationExchangeDetails.OtherIdentificationCountry == null)
                            trackedEntity.OtherIdentificationCountry = null;

                        trackedEntity = _infoExchangeEncryptionManager.EncryptInformationExchangeDetails(trackedEntity);
                        await _informationExchangeDetailsRepository.UpdateAsync(trackedEntity, true);                        
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
            }
            return result;
        }
              

        public async Task<Guid> AddInformationExchangeDetails(InformationExchangeDetailDto input)
        {
            // need to create a mapper
            var entity = ObjectMapper.Map<InformationExchangeDetailDto, InformationExchangeDetails>(input);
            try
            {
                using (_dataFilter.Disable<IMultiTenant>())
                {
                    if (input.Id == Guid.Empty)
                    {
                        entity = _infoExchangeEncryptionManager.EncryptInformationExchangeDetails(entity);
                        await _informationExchangeDetailsRepository.InsertAsync(entity, true);                      
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
            }
            return entity.Id;
        }

        public async Task<InformationExchangeDetailDto> GetInformationExchangeDetails(Guid id)
        {
            InformationExchangeDetailDto mappedEntity = new();
            try
            {
                using (_dataFilter.Disable<IMultiTenant>())
                {
                    var entity = await _informationExchangeDetailsRepository.GetAsync(id);
                    entity = _infoExchangeEncryptionManager.DecryptInformationExchangeDetails(entity);

                    mappedEntity = ObjectMapper.Map<InformationExchangeDetails, InformationExchangeDetailDto>(entity);
                    int.TryParse(StandardFormMonitoringYear, out int year);
                    mappedEntity.IsFullyForm = mappedEntity.FiscalEndDate.Year > year;
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
            }
            return mappedEntity;
        }

         private async Task DoReTriggerXMLFlow(Guid tenantId, Guid userId, Guid essXmlId)
        {
            using (_dataFilter.Disable<IMultiTenant>())
            {
                var essXml = await _informationExchangeXMLRepository.GetAsync(essXmlId);
                var detailIds = essXml.EssIds.Split(',').Select(Guid.Parse).ToList();
                var detailId = detailIds.FirstOrDefault();
                Check.NotNull(essXml, $"EssInformationExchangeXML with ID {essXmlId} not found.");
                var detail = await _informationExchangeDetailsRepository.GetAsync(detailId);
                
                var plainXml = _infoExchangeEncryptionManager.DecryptXml(essXml.XMLString);
                var messageRefId = XmlHelper.CheckElement(plainXml, CtsConstants.NodeNameMessageRefId);
                await SendCtsMessage(essXml, detail, tenantId, userId, messageRefId);
            }
        }

        private async Task SendCtsMessage(EssInformationExchangeXML essXml, InformationExchangeDetails details, Guid tenantId, Guid userId, string messageRefId)
        {
            //CTS doesn't allows Send XML from Bahamas to Bahamas, so we skip this
            if (essXml.ReceivingCountryCode.Equals(CtsConstants.CtsBahamsSenderCode, StringComparison.InvariantCultureIgnoreCase))
            {
                return;
            }
            var detailIds = essXml.EssIds.Split(',').Select(Guid.Parse).ToList();
 

            var packageRuestId = GuidGenerator.Create();
            //Put Encrypted XML to storage account
            //Ensure it is encrypted
            essXml.XMLString = _infoExchangeEncryptionManager.EncryptXml(essXml.XMLString);
            var xmlBytes = Encoding.UTF8.GetBytes(essXml.XMLString);

            var storageFileUrl = "";
            //This make sure file is uploaded to proper tenant's folder
            using (_currentTenant.Change(tenantId))
            {
                //Has to put the XML file to storage account, since Integration service handler cannot have access token
                //To access the EssInformationExchangeXML table API
                storageFileUrl = await _ctsIntegrationBlobAppService.UploadFile(packageRuestId.ToString().ToLower(), essXml.FileName, xmlBytes);
            }
            await _distributedEventBus.PublishAsync(new InformationExchangeXmlCreatedEto
            {
                EssInfoXmlId = essXml.Id,
                CtsPackageRequestId = packageRuestId,
                ExchangeReason = details.ExchangeReason,
                FiscalYear = details.FiscalEndDate.Year,
                ReceivingCountryCode = essXml.ReceivingCountryCode,
                InformationExchangeDetailIds = detailIds,
                TenantId = tenantId,
                UserId = userId,
                XmlFileLocationUrl = storageFileUrl,
                HasExchangeRecords = true,
                MessageRefId = messageRefId
            }, true);
        }

        public async Task ReTriggerXMLGenerationFlowAsync(Guid tenantId, Guid userId, Guid essXmlId)
        {
            await DoReTriggerXMLFlow(tenantId, userId, essXmlId);
        }

        //XML file location: ct-integrations/Tenants/TantGuid(CA tenantId)/PacakgeRequestId/...xml
        public async Task<InformationExchangeDetailsListEto> MapDetailsToXml(List<Guid> exchangesIds, Guid userId)
        {
            using (_dataFilter.Disable<IMultiTenant>())
            {
                var exchanges = await _informationExchangeDetailsRepository.GetAllByGuid(exchangesIds);
                if (exchanges == null || exchanges.Count == 0)
                    return new InformationExchangeDetailsListEto();
                foreach (var exchange in exchanges)
                {
                    _infoExchangeEncryptionManager.DecryptInformationExchangeDetails(exchange);
                }
                List<Guid> failedExchangesList = new List<Guid>();
                List<Guid> ids = new List<Guid>();
                var transmittingCountry = new Country { Code = "BHS", Code2 = "BS", Name = "Bahamas" };
                var oECDESSIdMap = new Dictionary<NTJ_OECD, List<Guid>>();
                var initalizeResultTuple = InitialAllOECD(exchanges, exchanges[0].FiscalEndDate.Year, transmittingCountry.Code2);
                var resultOECDs = initalizeResultTuple.Item1;
                var sequenceNumberMap = initalizeResultTuple.Item2;
                var bodies = new Dictionary<string, List<CorrectableNtjBody_Type>>();
                foreach (var exchange in exchanges)
                {
                    try
                    {
                        int.TryParse(StandardFormMonitoringYear, out int year);
                        bool isFullyForm = exchange.FiscalEndDate.Year > year;
                        var recipientGroups = exchange.RecipientDetails.GroupBy(r => r.JurisdictionResidence).ToDictionary(g => g.Key, g => g.ToList());
                        if (exchange.ExchangeReason == ExchangeReason.NonResidence)
                        {
                            AppendEntityNexusToCountryOECD(exchange, transmittingCountry, resultOECDs, bodies, oECDESSIdMap, sequenceNumberMap, isFullyForm);
                        }

                        foreach (var recipients in recipientGroups)
                        {
                            AppendToCountryOECD(exchange, recipients, resultOECDs, bodies, oECDESSIdMap, sequenceNumberMap, isFullyForm);
                        }
                    }
                    catch (Exception ex)
                    {
                        failedExchangesList.Add(exchange.Id);
                        Logger.LogError(ex, "Error when export exchange [{EntityName}](ID: {ExchangeId} )", exchange.EntityName, exchange.Id);
                    }
                }

                // need to update the Information Exchange Detalis status the one that pass to generate the XML
                var exchangesToUpdate = exchanges.Where(e => !failedExchangesList.Contains(e.Id)).ToList();
                exchangesToUpdate?.ForEach(e => e.Status = InformationExchangeStatus.InformationExchanged);

                if (exchangesToUpdate != null && exchangesToUpdate.Count > 0)
                {
                    foreach (var item in exchangesToUpdate)
                    {
                        _infoExchangeEncryptionManager.EncryptInformationExchangeDetails(item);
                        await _informationExchangeDetailsRepository.UpdateAsync(item, true);
                    }

                    var list = exchangesToUpdate.Select(x => new InformationExchangeDetailsEto { InformationExchangeDetailsId = x.Id, Status = x.Status }).ToList();

                    //Don't need to notify Dashboard instead direct updating the status after remove call
                    //await _distributedEventBus.PublishAsync(new InformationExchangeDetailsListEto { DetailsList = list }, true);

                    foreach (var oecd in resultOECDs)
                    {
                        // assign bodies to each OECD
                        oecd.Value.NtjBody = bodies[oecd.Key.Code2].ToArray();
                        // serial oecd object to xml then save to file
                        var xml = GetXMLString(oecd.Value, NAMESPACE);

                        var messageRefId = XmlHelper.CheckElement(xml, CtsConstants.NodeNameMessageRefId);
                        // add EssInformationExchangeXML will save to DB
                        var exchnageXmlResult = GetEssInformationExchangeXMLRecord(oecd, transmittingCountry, sequenceNumberMap, oECDESSIdMap, xml);
                        exchnageXmlResult.XMLString = _infoExchangeEncryptionManager.EncryptXml(exchnageXmlResult.XMLString);
                        await _informationExchangeXMLRepository.InsertAsync(exchnageXmlResult, true);
                        ids.Add(exchnageXmlResult.Id);

                        await SendCtsMessage(exchnageXmlResult, exchangesToUpdate[0], _currentTenant.Id!.Value, userId, messageRefId);
                    }

                    if (ids.Count > 0)
                    {
                        var xmlContent = await CreateZipAsync(ids);
                        return new InformationExchangeDetailsListEto 
                        { 
                            DetailsList = list,
                            XmlContent = xmlContent
                        };
                    }
                }
                return new InformationExchangeDetailsListEto();
            }
        }

        public async Task<byte[]> CreateZipAsync(IEnumerable<Guid> ids)
        {
            using MemoryStream stream = new MemoryStream();
            using (var zipStream = new ZipOutputStream(stream))
            {
                zipStream.SetLevel(3); // Compression level 0-9 (0 = no compression, 9 = max compression)

                foreach (var id in ids)
                {
                    var entry = await _informationExchangeXMLRepository.GetAsync(id);
                    entry.XMLString = _infoExchangeEncryptionManager.DecryptXml(entry.XMLString);
                    Encoding encoding = Encoding.UTF8;
                    byte[] byteArray = encoding.GetBytes(entry.XMLString);

                    var zipEntry = new ZipEntry(entry.FileName)
                    {
                        DateTime = DateTime.Now,  // Set the modification date
                        Size = byteArray.Length   // Set the uncompressed size
                    };

                    zipStream.PutNextEntry(zipEntry);
                    zipStream.Write(byteArray, 0, byteArray.Length);
                    zipStream.CloseEntry();
                }

                zipStream.IsStreamOwner = false; // Leave stream open after closing zipStream
                zipStream.Finish(); // Finalizes the archive
            }

            return stream.ToArray();
        }
        //This API is called by Dashboard Service when status change is initiate from Dashboard Service
        public async Task UpdateInformationExchangeDetailStatus(Guid id, InformationExchangeStatus status)
        {
            using (_dataFilter.Disable<IMultiTenant>())
            {
                var detail = await _informationExchangeDetailsRepository.FindAsync(id);
                if (detail == null)
                {
                    return;
                }
                detail.Status = status;
                await _informationExchangeDetailsRepository.UpdateAsync(detail);
            }
        }

        public async Task<EssInformationExchangeXMLDto> GetEssInformationXml(Guid essInfoXmlId)
        {
            var essInformationExchangeXML = await _informationExchangeXMLRepository.GetAsync(essInfoXmlId);
            return ObjectMapper.Map<EssInformationExchangeXML, EssInformationExchangeXMLDto>(essInformationExchangeXML);
        }

        private EssInformationExchangeXML GetEssInformationExchangeXMLRecord(KeyValuePair<Country, NTJ_OECD> oecd, Country transmittingCountry, Dictionary<string, int> sequenceNumberMap, Dictionary<NTJ_OECD, List<Guid>> oECDESSIdMap, string xml)
        {
            return new EssInformationExchangeXML()
            {
                CreatedAt = DateTime.UtcNow,
                EssIds = String.Join(",", oECDESSIdMap[oecd.Value].Distinct()),
                TransmittingCountryCode = transmittingCountry.Code2,
                ReceivingCountryCode = oecd.Key.Code2,
                SequenceNumber = sequenceNumberMap[oecd.Key.Code2],
                XMLString = xml,
                FileName = $"{DateTime.UtcNow.ToString("yyyy-MM-dd-HH-mm-ss")}-{BHA_ISO_CODE}-{oecd.Key.Code}.xml"
            };
        }


        public Task<List<Guid>> GetXMLFilesBasedOnExType(ExchangeReason reason)
        {
            throw new NotImplementedException();
        }
    }
}