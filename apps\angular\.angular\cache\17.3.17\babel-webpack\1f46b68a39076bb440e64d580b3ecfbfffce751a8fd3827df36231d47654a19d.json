{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DateHelper } from '@app/shared/utils/date-helper';\nimport { AppComponentBase } from '@app/app-component-base';\nimport { themeJson } from '@app/features/es-declaration/containers/es-declaration/survey-theme-json';\nimport { viewDeclarationEmpty, viewRelevantActivites } from '@app/shared/declaration-jsons/view-declaration-json';\nimport { environment } from '@environments/environment';\nimport { finalize, forkJoin } from 'rxjs';\nimport { ComponentCollection, Model, Serializer } from \"survey-core\";\nimport { RelevantActivityType } from 'proxies/economic-service/lib/proxy/bdo/ess/shared/hosting/microservices/eto/declaration/base-classes';\nimport Swal from 'sweetalert2';\nimport { Converter } from \"showdown\";\nimport { AngularComponentFactory } from 'survey-angular-ui';\nimport { SurveyFilePreviewComponent } from '@app/shared/components/survey-file-preview/survey-file-preview.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declarations\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration\";\nimport * as i5 from \"@app/shared/services/composite-question.service\";\nimport * as i6 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates\";\nimport * as i7 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/assessment\";\nimport * as i8 from \"@app/shared/services/sweetalert.service\";\nimport * as i9 from \"@abp/ng.theme.shared\";\nimport * as i10 from \"@abp/ng.core\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"survey-angular-ui\";\nimport * as i13 from \"../../../es-declaration/containers/declaration-entity-details/declaration-entity-details.component\";\nimport * as i14 from \"../../../es-declaration/containers/assessment-action-view/assessment-action-view.component\";\nimport * as i15 from \"../assesment-selection/assesment-selection.component\";\nfunction CaActionPageComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"mode\", \"indeterminate\")(\"diameter\", 100);\n  }\n}\nfunction CaActionPageComponent_app_assesment_selection_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-assesment-selection\", 6);\n    i0.ɵɵlistener(\"refreshData\", function CaActionPageComponent_app_assesment_selection_1_Template_app_assesment_selection_refreshData_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refresh($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"activityActions\", ctx_r1.activityActions)(\"activityCount\", ctx_r1.activityCount)(\"declarationData\", ctx_r1.declarationData)(\"from\", ctx_r1.from);\n  }\n}\nfunction CaActionPageComponent_app_assessment_action_view_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-assessment-action-view\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"declarationData\", ctx_r1.declarationData)(\"declarationId\", ctx_r1.declarationData == null ? null : ctx_r1.declarationData.id);\n  }\n}\nfunction CaActionPageComponent_div_3_survey_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"survey\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"model\", ctx_r1.survey);\n  }\n}\nfunction CaActionPageComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"app-declaration-entity-details\", 9);\n    i0.ɵɵtemplate(2, CaActionPageComponent_div_3_survey_2_Template, 1, 1, \"survey\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"entityId\", ctx_r1.entityId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.declarationData);\n  }\n}\nAngularComponentFactory.Instance.registerComponent(\"sv-file-preview\", SurveyFilePreviewComponent);\nexport let CaActionPageComponent = /*#__PURE__*/(() => {\n  class CaActionPageComponent extends AppComponentBase {\n    constructor(injector, declarationService, route, datePipe, activityLookup, compositeQuestionService, service, cigaLookup, assessmentService, sweetAlert, toasterService, config) {\n      super(injector);\n      this.declarationService = declarationService;\n      this.route = route;\n      this.datePipe = datePipe;\n      this.activityLookup = activityLookup;\n      this.compositeQuestionService = compositeQuestionService;\n      this.service = service;\n      this.cigaLookup = cigaLookup;\n      this.assessmentService = assessmentService;\n      this.sweetAlert = sweetAlert;\n      this.toasterService = toasterService;\n      this.config = config;\n      this.compositeKeys = [\"intellectualPropertyBusiness\", \"bankingQuestions\", \"distributionQuestions\", \"financeQuestions\", \"fundManagmentQuestions\", \"headquartersQuestions\", \"holdingBusinessQuestions\", \"insuranceQuestions\", \"outsourcingIntellectualPropertyBusiness\", \"shippingQuestions\"];\n      this.templateListDto = {\n        sorting: \"id\",\n        skipCount: 0,\n        maxResultCount: 100\n      }; // TODO: Get all results not just first 10\n      this.loading = false;\n      this.relevantActivites = [];\n      this.activites = [];\n      this.cigaOtherId = {\n        holding: \"\",\n        distribution: \"\",\n        ip: \"\",\n        shipping: \"\",\n        headquarters: \"\",\n        finance: \"\",\n        funds: \"\",\n        insurance: \"\",\n        banking: \"\"\n      };\n      this.activityMappings = {\n        holding: \"\",\n        distribution: \"\",\n        ip: \"\",\n        shipping: \"\",\n        headquarters: \"\",\n        finance: \"\",\n        funds: \"\",\n        insurance: \"\",\n        banking: \"\"\n      };\n      this.activityActions = [];\n      this.relevantActivityNames = [];\n      this.activityCount = 0;\n      this.assumedLocalZone = 'America/New_York'; //'local'\n      /**\n       *  Work for redirect back to information-exchange dashboard, if the request came from there.\n       *  value = 'info-exchange', redirect back to /es-info-exchange\n       *  value = 'es-search', redirect back to '/es-search\n      */\n      this.from = '';\n      this.route.queryParams.subscribe(params => {\n        this.declarationId = params['declarationid'] ? params['declarationid'] : null;\n        this.entityId = params['entityid'] ? params['entityid'] : null;\n        // Assign value to assessment-selection.component.ts\n        // work for define redirection path\n        this.from = params['from'] ? params['from'] : 'es-search';\n      });\n      //this.compositeQuestionService.setCompositeQuestions();\n    }\n    ngOnInit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.loading = true;\n        _this.currentUser = _this.config.getOne(\"currentUser\");\n        yield _this.intialConfigAndSetup();\n      })();\n    }\n    setSurvey() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        _this2.viewTemplate = viewDeclarationEmpty;\n        _this2.viewRelevantTemplate = viewRelevantActivites;\n        _this2.assignedUserController();\n        _this2.mapSurveyJson();\n        const converter = new Converter();\n        _this2.survey.onTextMarkdown.add(function (survey, options) {\n          // Convert Markdown to HTML\n          let str = converter.makeHtml(options.text);\n          // Remove root paragraphs <p></p>\n          str = str.substring(3);\n          str = str.substring(0, str.length - 4);\n          // Set HTML markup to render\n          options.html = str;\n        });\n        _this2.setSurveyData();\n      })();\n    }\n    assignedUserController() {\n      const readonlyStatuses = ['Fail', 'Pass'];\n      if (this.declarationData.assessmentDetails.assignedTo === this.currentUser.id && !readonlyStatuses.includes(this.declarationData.assessmentStatus)) {\n        this.relevantActivityReadonly = false;\n      } else {\n        this.relevantActivityReadonly = true;\n      }\n    }\n    refresh(event) {\n      this.declarationData = event;\n      this.setSurvey();\n    }\n    hardRefresh(name) {\n      this.setSurvey();\n      this.survey.getQuestionByName(name).focus();\n    }\n    formatDate(dateStr) {\n      return DateHelper.formatEstUtcDate(dateStr, 'yyyy-MM-dd');\n    }\n    setSurveyData() {\n      if (this.declarationId) {\n        // if declaration ID then go get that and set survey\n        for (const key in this.declarationData.surveyData) {\n          if (this.declarationData.surveyData[key]) {\n            // dates need to be set up by trimming the extra stuff off\n            if (key === \"financialPeriodStartDate\") {\n              this.declarationData.surveyData[key] = this.formatDate(this.declarationData.surveyData[key]);\n            }\n            if (key === \"financialPeriodEndDate\") {\n              this.declarationData.surveyData[key] = this.formatDate(this.declarationData.surveyData[key]);\n            }\n            if (this.compositeKeys.includes(key)) {\n              if (key === \"holdingBusinessQuestions\") {\n                this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"] = this.formatDate(this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"]);\n                this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"] = this.formatDate(this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"]);\n              }\n              if (key === \"intellectualPropertyBusiness\") {\n                this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"] = this.formatDate(this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"]);\n                this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"] = this.formatDate(this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"]);\n              }\n              if (key !== \"intellectualPropertyBusiness\" && key !== \"holdingBusinessQuestions\") {\n                // Other activities\n                this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"] = this.formatDate(this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"]);\n                this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"] = this.formatDate(this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"]);\n              }\n            }\n          }\n        }\n        this.survey.data = JSON.parse(JSON.stringify(this.declarationData.surveyData));\n        this.mapData();\n      }\n    }\n    onDownloadFile(options) {\n      if (options != null && options.fileValue) {\n        var count = 1;\n        var fileList = [];\n        // for(const f of options.fileValue) {\n        var f = options.fileValue;\n        var docType = options.question.name;\n        var header = this.GetHeader(options.fileValue);\n        let isImport = false;\n        if (docType) {\n          const fileUploadLocation = options?.name;\n          const spinnerName = fileUploadLocation + \"Spinner\";\n          this.declarationService.downloadCADeclarationDocument(this.declarationId, f.name, docType, isImport).pipe(finalize(() => {})).subscribe(result => {\n            var t = result;\n            options.callback(\"success\", header + result);\n          });\n        }\n        // }\n      }\n    }\n    GetHeader(fileValue) {\n      var fileName = fileValue.name;\n      var split = fileName.split('.');\n      var extension = split[split.length - 1];\n      switch (extension.toLowerCase()) {\n        case \"png\":\n          {\n            return \"data:image/png;base64,\";\n          }\n        case \"bmp\":\n          {\n            return \"data:image/bmp;base64,\";\n          }\n        case \"jpg\":\n          {\n            return \"data:image/jpeg;base64,\";\n          }\n        case \"jpeg\":\n          {\n            return \"data:image/jpeg;base64,\";\n          }\n        case \"pdf\":\n          {\n            return \"data:application/pdf;base64,\";\n          }\n      }\n      return \"\";\n    }\n    isValidFileType(fileType) {\n      switch (fileType.toLowerCase()) {\n        case \"png\":\n          {\n            return true;\n          }\n        case \"bmp\":\n          {\n            return true;\n          }\n        case \"jpg\":\n          {\n            return true;\n          }\n        case \"jpeg\":\n          {\n            return true;\n          }\n        case \"pdf\":\n          {\n            return true;\n          }\n      }\n      return false;\n    }\n    mapSurveyJson() {\n      const relevantActivitesFormated = this.relevantActivites.map(activity => activity.toLowerCase());\n      this.viewTemplate.pages[0].elements.forEach(element => {\n        this.activeTemplate?.pages.forEach(page => {\n          if (element.name === page.name) {\n            if (page.name === 'activityDetail') {\n              let tempPages = [];\n              page.elements[0].elements.forEach(e => {\n                e.width = '100%';\n                if (relevantActivitesFormated.includes(e.title.toLowerCase())) {\n                  this.relevantActivityNames.push(e.name + 'dropdown');\n                  tempPages.push({\n                    type: \"dropdown\",\n                    name: e.name + 'dropdown',\n                    title: e.title + ' Assessment Action',\n                    choices: [\"Pass\", \"Fail\"],\n                    elements: null,\n                    columns: null,\n                    choicesByUrl: null,\n                    options: null,\n                    validators: null,\n                    readOnly: this.relevantActivityReadonly,\n                    hideNumber: true,\n                    startWithNewLine: false\n                  });\n                }\n                tempPages.push(e);\n              });\n              element.elements = tempPages;\n            } else {\n              element.elements = page.elements;\n            }\n          }\n        });\n      });\n      this.activityCount = this.relevantActivityNames.length;\n      //console.log(this.activityCount)\n      if (this.viewTemplate && this.viewTemplate.pages && this.viewTemplate.pages.length > 0) {\n        this.viewTemplate.pages[0].title = 'ES DECLARATION SUCCESSFULLY SUBMITTED AT ' + this.datePipe.transform(this.declarationData.submittedDate, 'yyyy-MM-dd, h:mm:ss a', 'local');\n        //disable all elements\n        if (this.viewTemplate.pages[0].elements && this.viewTemplate.pages[0].elements.length > 0) {\n          this.viewTemplate.pages[0].elements.forEach(e1 => {\n            if (e1.elements && e1.elements.length > 0) {\n              e1.elements.forEach(e2 => {\n                if (e2.type != 'dropdown') e2.enableIf = 'false';\n              });\n            }\n          });\n        }\n      }\n      Serializer.addProperty(\"question\", {\n        name: \"inputWidth\",\n        type: \"string\"\n      });\n      this.survey = new Model(this.viewTemplate);\n      this.survey.applyTheme(themeJson);\n      //this.compositeQuestionService.setupCustomSurveyFields(this.survey);\n      this.survey.showNavigationButtons = false;\n      this.survey.onAfterRenderSurvey.add((sender, options) => {\n        this.saveRelevantActivityDecision(sender, options);\n      });\n      this.survey.onAfterRenderQuestionInput.add(function (sender, options) {\n        if (!options.question.inputWidth) return;\n        options.htmlElement.style.width = options.question.inputWidth;\n      });\n      this.survey.onValueChanged.add((sender, options) => {\n        this.survey.getQuestionByName(options.name).enableIf = 'false';\n        if (this.relevantActivityNames.includes(options.name) && options.value) {\n          //confirmation dialog\n          Swal.fire({\n            title: 'Relevant Activity Decision',\n            text: \"Are you sure you want to save this decision?\",\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#3085d6',\n            cancelButtonColor: '#d33'\n          }).then(result => {\n            if (result.isConfirmed) {\n              var decision = {\n                relevantActivity: this.getActivityEnum(options.name),\n                pass: options.value.toLowerCase() == \"pass\"\n              };\n              this.assessmentService.relevantActivityDecisionByDeclarationIdAndDecision(this.declarationId, decision).subscribe(result => {\n                if (result) {\n                  this.declarationData = result;\n                  this.saveRelevantActivityDecision(sender, options);\n                  this.toasterService.success('Relevant Activity Decision saved');\n                  this.hardRefresh(options.name);\n                }\n              });\n            } else {\n              options.question.clearValue();\n              this.hardRefresh(options.name);\n              //after the clear, the dropdown sometimes hangs around the top left of the page for some reason.\n            }\n          });\n        } else {\n          this.survey.getQuestionByName(options.name).enableIf = 'true';\n          this.saveRelevantActivityDecision(sender, options);\n        }\n      });\n      this.survey.onDownloadFile.add((_, options) => {\n        this.onDownloadFile(options);\n      });\n    }\n    saveRelevantActivityDecision(sender, options) {\n      let tempActivtyActions = [];\n      this.relevantActivityNames.forEach(name => {\n        sender.data[name] && tempActivtyActions.push({\n          name,\n          value: sender.data[name]\n        });\n      });\n      if (!options.name || options.name != 'relevantActRelevantActivities') {\n        this.activityActions = tempActivtyActions;\n      }\n    }\n    mapData() {\n      // mapping from id to name\n      if (this.activites) {\n        if (this.survey.data['relevantActRelevantActivities']) {\n          if (this.relevantActivites && this.relevantActivites.length > 0) {\n            this.survey.setValue('relevantActRelevantActivities', this.relevantActivites, undefined, false);\n          } else {\n            this.survey.setValue('relevantActRelevantActivities', this.activites, undefined, false);\n          }\n        }\n      }\n      if (this.declarationData && this.declarationData.assessmentDetails && this.declarationData.assessmentDetails.relevantActivityDecisions) {\n        this.declarationData.assessmentDetails.relevantActivityDecisions.forEach(ra => {\n          var activityName = this.getActivityString(ra.relevantActivity);\n          this.survey.setValue(activityName, ra.pass ? \"Pass\" : \"Fail\", undefined, false);\n        });\n      }\n      this.loading = false;\n    }\n    preProcessCigaDropdown() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        ComponentCollection.Instance.items.forEach(Component => {\n          if (Component['json']['name'] === \"IntellectualPropertyBusiness\") {\n            Component['json']['elementsJSON'].forEach(elements => {\n              for (const element in elements) {\n                // properties of each question\n                if (element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivity') {\n                  elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.ip;\n                }\n                if (element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivityComment') {\n                  elements['visibleIf'] = \"{IntellectualPropertyBusiness.intelPropBusCIGAInBahamasForRelevantActivity} anyof ['\" + _this3.cigaOtherId.ip + \"']\";\n                }\n              }\n            });\n          }\n          if (Component['json']['name'] === \"bankingOtherRelevantActivities\") {\n            Component['json']['elementsJSON'].forEach(elements => {\n              for (const element in elements) {\n                // properties of each question\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                  elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.banking;\n                }\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                  elements['visibleIf'] = \"{bankingQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.banking + \"']\";\n                }\n              }\n            });\n          }\n          if (Component['json']['name'] === \"distributionOtherRelevantActivities\") {\n            Component['json']['elementsJSON'].forEach(elements => {\n              for (const element in elements) {\n                // properties of each question\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                  elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.distribution;\n                }\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                  elements['visibleIf'] = \"{distributionQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.distribution + \"']\";\n                }\n              }\n            });\n          }\n          if (Component['json']['name'] === \"financeOtherRelevantActivities\") {\n            Component['json']['elementsJSON'].forEach(elements => {\n              for (const element in elements) {\n                // properties of each question\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                  elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.finance;\n                }\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                  elements['visibleIf'] = \"{financeQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.finance + \"']\";\n                }\n              }\n            });\n          }\n          if (Component['json']['name'] === \"fundManagmentOtherRelevantActivities\") {\n            Component['json']['elementsJSON'].forEach(elements => {\n              for (const element in elements) {\n                // properties of each question\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                  elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.funds;\n                }\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                  elements['visibleIf'] = \"{fundManagmentQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.funds + \"']\";\n                }\n              }\n            });\n          }\n          if (Component['json']['name'] === \"headquartersOtherRelevantActivities\") {\n            Component['json']['elementsJSON'].forEach(elements => {\n              for (const element in elements) {\n                // properties of each question\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                  elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.headquarters;\n                }\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                  elements['visibleIf'] = \"{headquartersQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.headquarters + \"']\";\n                }\n              }\n            });\n          }\n          if (Component['json']['name'] === \"insuranceOtherRelevantActivities\") {\n            Component['json']['elementsJSON'].forEach(elements => {\n              for (const element in elements) {\n                // properties of each question\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                  elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.insurance;\n                }\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                  elements['visibleIf'] = \"{insuranceQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.insurance + \"']\";\n                }\n              }\n            });\n          }\n          if (Component['json']['name'] === \"shippingOtherRelevantActivities\") {\n            Component['json']['elementsJSON'].forEach(elements => {\n              for (const element in elements) {\n                // properties of each question\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                  elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.shipping;\n                }\n                if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                  elements['visibleIf'] = \"{shippingQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.shipping + \"']\";\n                }\n              }\n            });\n          }\n        });\n      })();\n    }\n    getNeededData() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        _this4.activites = _this4.declarationData.surveyData[\"relevantActRelevantActivities\"];\n        if (_this4.activites) {\n          _this4.relevantActivityList.items.forEach(element => {\n            _this4.activites.forEach(activity => {\n              if (element.id === activity) {\n                _this4.relevantActivites.push(element.name);\n              }\n            });\n          });\n        }\n        _this4.setSurvey();\n      })();\n    }\n    intialConfigAndSetup() {\n      const configData = this.configState.getAll();\n      if (configData.currentTenant.isAvailable) {\n        this.tenantName = configData.currentTenant.name;\n      }\n      let cigaObservable = this.cigaLookup.getList({\n        maxResultCount: 100\n      });\n      let templateObservable = this.service.getList(this.templateListDto);\n      let activityObservable = this.activityLookup.getList({\n        maxResultCount: 100\n      });\n      let declarationObservable = this.declarationService.getCADeclarationByDeclarationId(this.declarationId);\n      let observableList = [cigaObservable, templateObservable, activityObservable, declarationObservable];\n      forkJoin(observableList).subscribe(result => {\n        const cigaData = result[0].items;\n        const templateData = result[1].items;\n        this.relevantActivityList = result[2];\n        this.declarationData = result[3];\n        //console.log('declarationData', this.declarationData);\n        this.relevantActivityList.items.forEach(element => {\n          if (element.name === \"Holding business\") this.activityMappings.holding = element.id;\n          if (element.name === \"Distribution and service centre business\") this.activityMappings.distribution = element.id;\n          if (element.name === \"Intellectual property business\") this.activityMappings.ip = element.id;\n          if (element.name === \"Shipping business\") this.activityMappings.shipping = element.id;\n          if (element.name === \"Headquarters business\") this.activityMappings.headquarters = element.id;\n          if (element.name === \"Finance and leasing business\") this.activityMappings.finance = element.id;\n          if (element.name === \"Fund management business\") this.activityMappings.funds = element.id;\n          if (element.name === \"Insurance business\") this.activityMappings.insurance = element.id;\n          if (element.name === \"Banking business\") this.activityMappings.banking = element.id;\n        });\n        cigaData.forEach(item => {\n          if (item.name.includes('(please specify)')) {\n            if (item.relevantActivityId === this.activityMappings.holding) this.cigaOtherId.holding = item.id;\n            if (item.relevantActivityId === this.activityMappings.distribution) this.cigaOtherId.distribution = item.id;\n            if (item.relevantActivityId === this.activityMappings.ip) this.cigaOtherId.ip = item.id;\n            if (item.relevantActivityId === this.activityMappings.shipping) this.cigaOtherId.shipping = item.id;\n            if (item.relevantActivityId === this.activityMappings.headquarters) this.cigaOtherId.headquarters = item.id;\n            if (item.relevantActivityId === this.activityMappings.finance) this.cigaOtherId.finance = item.id;\n            if (item.relevantActivityId === this.activityMappings.funds) this.cigaOtherId.funds = item.id;\n            if (item.relevantActivityId === this.activityMappings.insurance) this.cigaOtherId.insurance = item.id;\n            if (item.relevantActivityId === this.activityMappings.banking) this.cigaOtherId.banking = item.id;\n          }\n        });\n        var activeTemplate;\n        if (this.declarationData && this.declarationData.declarationTemplateId) {\n          activeTemplate = templateData.find(x => x.id == this.declarationData.declarationTemplateId);\n        } else {\n          activeTemplate = templateData.find(element => element.isActive);\n        }\n        if (activeTemplate) {\n          this.activeTemplate = activeTemplate.survey;\n          this.templateId = activeTemplate.id;\n        }\n        this.compositeQuestionService.setCompositeQuestions(activeTemplate);\n        this.setUrls(this.activeTemplate);\n        this.preProcessCigaDropdown();\n        this.getNeededData();\n      }, error => {\n        this.loading = false;\n        console.log(\"intialConfigAndSetup Error:\", error);\n      });\n    }\n    setUrls(SurveyTemplate) {\n      for (const key in SurveyTemplate) {\n        for (const key2 in SurveyTemplate[key]) {\n          for (const key3 in SurveyTemplate[key][key2]) {\n            for (const key4 in SurveyTemplate[key][key2][key3]) {\n              for (const key5 in SurveyTemplate[key][key2][key3][key4]) {\n                for (const key6 in SurveyTemplate[key][key2][key3][key4][key5]) {\n                  for (const key7 in SurveyTemplate[key][key2][key3][key4][key5][key6]) {\n                    if (key7 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7] !== null) {\n                      // choices by url is on the question level example is relevant activity drop down\n                      SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url.replace(\"{0}\", this.tenantName);\n                      console.log(key7, SurveyTemplate[key][key2][key3][key4][key5][key6][key7]);\n                    }\n                    for (const key8 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7]) {\n                      for (const key9 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8]) {\n                        if (key9 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9] !== null) {\n                          // choices by url is on a column level example entity details differnt business address country drop down\n                          SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url.replace(\"{0}\", this.tenantName);\n                          console.log(key9, SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9]);\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    getActivityEnum(name) {\n      switch (name) {\n        case \"intellectualPropertyBusinessdropdown\":\n          return RelevantActivityType.IntellectualProperty;\n        case \"holdingBusinessQuestionsdropdown\":\n          return RelevantActivityType.Holding;\n        case \"bankingQuestionsdropdown\":\n          return RelevantActivityType.Banking;\n        case \"insuranceQuestionsdropdown\":\n          return RelevantActivityType.Insurance;\n        case \"fundManagmentQuestionsdropdown\":\n          return RelevantActivityType.FundManagement;\n        case \"financeQuestionsdropdown\":\n          return RelevantActivityType.FinanceAndLeasing;\n        case \"headquartersQuestionsdropdown\":\n          return RelevantActivityType.Headquarter;\n        case \"shippingQuestionsdropdown\":\n          return RelevantActivityType.Shipping;\n        case \"distributionQuestionsdropdown\":\n          return RelevantActivityType.Distribution;\n        default:\n          return RelevantActivityType.NA;\n      }\n    }\n    getActivityString(activity) {\n      switch (activity) {\n        case RelevantActivityType.IntellectualProperty:\n          return \"intellectualPropertyBusinessdropdown\";\n        case RelevantActivityType.Holding:\n          return \"holdingBusinessQuestionsdropdown\";\n        case RelevantActivityType.Banking:\n          return \"bankingQuestionsdropdown\";\n        case RelevantActivityType.Insurance:\n          return \"insuranceQuestionsdropdown\";\n        case RelevantActivityType.FundManagement:\n          return \"fundManagmentQuestionsdropdown\";\n        case RelevantActivityType.FinanceAndLeasing:\n          return \"financeQuestionsdropdown\";\n        case RelevantActivityType.Headquarter:\n          return \"headquartersQuestionsdropdown\";\n        case RelevantActivityType.Shipping:\n          return \"shippingQuestionsdropdown\";\n        case RelevantActivityType.Distribution:\n          return \"distributionQuestionsdropdown\";\n        default:\n          return \"\";\n      }\n    }\n    static {\n      this.ɵfac = function CaActionPageComponent_Factory(t) {\n        return new (t || CaActionPageComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.DeclarationService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.DatePipe), i0.ɵɵdirectiveInject(i4.RelevantActivityService), i0.ɵɵdirectiveInject(i5.CompositeQuestionService), i0.ɵɵdirectiveInject(i6.TemplateService), i0.ɵɵdirectiveInject(i4.CigaService), i0.ɵɵdirectiveInject(i7.AssessmentService), i0.ɵɵdirectiveInject(i8.SweetAlertService), i0.ɵɵdirectiveInject(i9.ToasterService), i0.ɵɵdirectiveInject(i10.ConfigStateService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CaActionPageComponent,\n        selectors: [[\"app-ca-action-page\"]],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 4,\n        vars: 4,\n        consts: [[\"class\", \"spinner\", 4, \"ngIf\"], [3, \"activityActions\", \"activityCount\", \"declarationData\", \"from\", \"refreshData\", 4, \"ngIf\"], [\"type\", \"CA\", 3, \"declarationData\", \"declarationId\", 4, \"ngIf\"], [\"id\", \"survey\", 4, \"ngIf\"], [1, \"spinner\"], [1, \"mat-spinner-color\", 3, \"mode\", \"diameter\"], [3, \"refreshData\", \"activityActions\", \"activityCount\", \"declarationData\", \"from\"], [\"type\", \"CA\", 3, \"declarationData\", \"declarationId\"], [\"id\", \"survey\"], [\"type\", \"CA\", \"isViewMode\", \"true\", 3, \"entityId\"], [3, \"model\", 4, \"ngIf\"], [3, \"model\"]],\n        template: function CaActionPageComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, CaActionPageComponent_div_0_Template, 2, 2, \"div\", 0)(1, CaActionPageComponent_app_assesment_selection_1_Template, 1, 4, \"app-assesment-selection\", 1)(2, CaActionPageComponent_app_assessment_action_view_2_Template, 1, 2, \"app-assessment-action-view\", 2)(3, CaActionPageComponent_div_3_Template, 3, 2, \"div\", 3);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.declarationData);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.declarationData);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.entityId && ctx.declarationData);\n          }\n        },\n        dependencies: [i11.MatProgressSpinner, i3.NgIf, i12.SurveyComponent, i13.DeclarationEntityDetailsComponent, i14.AssessmentActionViewComponent, i15.AssesmentSelectionComponent],\n        styles: [\".spinner[_ngcontent-%COMP%]{position:relative;left:45%;top:6em}\"]\n      });\n    }\n  }\n  return CaActionPageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}