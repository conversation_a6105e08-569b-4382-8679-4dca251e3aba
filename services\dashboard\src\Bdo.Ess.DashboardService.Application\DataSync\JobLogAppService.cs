﻿using Bdo.Ess.DashboardService.Entities;
using Bdo.Ess.Shared.Constants.DataSync;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Volo.Abp.Uow;

namespace Bdo.Ess.DashboardService.DataSync
{
    public class JobLogAppService : DashboardServiceAppService, IJobLogAppService
    {
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly IJobLogRepository _jobLogRepository;

        public JobLogAppService(
            IUnitOfWorkManager unitOfWorkManager
            , IJobLogRepository jobLogRepository
            )
        {
            _unitOfWorkManager = unitOfWorkManager;
            _jobLogRepository = jobLogRepository;
        }

        public async Task CleanOldLogsAsync()
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
            DateTime twoMonths = DateTime.Now.AddMonths(-2);
            // Delete logs older than 2 months, with extra condition when data grow big, the performance would be very bad
            var oldLogs = await _jobLogRepository.GetListAsync(x => x.CreationTime < twoMonths
                //&& x.Exception == null
                //&& !(x.LogInfo ?? "").StartsWith("Error")
            );
            if (oldLogs != null && oldLogs.Count > 0)
            {
                await _jobLogRepository.DeleteManyAsync(oldLogs);
            }
            await uow.SaveChangesAsync();
            await uow.CompleteAsync();
        }

        public async Task WriteLogAsync(JobLogEto eventData)
        {
            try
            {
                using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
                var jobLog = new JobLog();

                ObjectMapper.Map<JobLogEto, JobLog>(eventData, jobLog);
                await _jobLogRepository.InsertAsync(jobLog);
                await uow.SaveChangesAsync();
                await uow.CompleteAsync();
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
            }
        }

        public async Task WriteBatchLogAsync(string contentType, int? skipCount = null, int? batchSize = null, string info = "", string? exception = null)
        {
            try
            {
                var eto = new JobLogEto() { ContentType = contentType, LogInfo = info, BatchSkip = skipCount, BatchSize = batchSize, Exception = exception };
                await WriteLogAsync(eto);
            }
            catch (Exception e)
            {
                Logger.LogException(e);
            }
        }

        public async Task WriteSingleLogAsync(string contentType, Guid? id, string info = "", string? exception = null)
        {
            try
            {
                var eto = new JobLogEto() { ContentType = contentType, KeyValue = id, LogInfo = info, Exception = exception };
                await WriteLogAsync(eto);
            }
            catch (Exception e)
            {
                Logger.LogException(e);
            }
        }
    }
}