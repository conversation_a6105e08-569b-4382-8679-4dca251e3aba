using Bdo.Ess.CtsIntegration.Encryption;
using Bdo.Ess.CtsIntegration.Entities;
using Bdo.Ess.CtsIntegration.Localization;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Audit;
using Bdo.Ess.Shared.Utility.Exensions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Asn1.Pkcs;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Uow;

namespace Bdo.Ess.CtsIntegration.BahamasCtsSettings;

public class BahamasCtsSettingAppService : CtsIntegrationAppService, IBahamasCtsSettingAppService
{
    private readonly IRepository<BahamasCtsSetting, Guid> _repository;
    private readonly ICtsEncryptionManager _ctsEncryptionManager;
    private readonly IDistributedEventBus _auditEventBus;
    private readonly IAuditWebInfo _auditWebInfo;
    private readonly ILogger<BahamasCtsSettingAppService> _logger;

    public BahamasCtsSettingAppService(
        IRepository<BahamasCtsSetting, Guid> repository,
        ICtsEncryptionManager ctsEncryptionManager,
        IDistributedEventBus auditEventBus,
        IAuditWebInfo auditWebInfo,
        ILogger<BahamasCtsSettingAppService> logger)
    {
        _repository = repository;
        _ctsEncryptionManager = ctsEncryptionManager;
        _auditEventBus = auditEventBus;
        _auditWebInfo = auditWebInfo;
        _logger = logger;
        LocalizationResource = typeof(CtsIntegrationResource);
    }

    public async Task<BahamasCtsSettingDto> CreateAsync(string sshKey, string ctsCert, CreateBahamasCtsSettingDto input)
    {
        var currentSettings = await GetCurrentSettingsAsync();
        if (currentSettings != null)
        {
            // If current settings exist, throw an exception
            throw new UserFriendlyException("A Bahamas CTS setting already exists. Only one setting can be created.");
        }

        var entity = ObjectMapper.Map<CreateBahamasCtsSettingDto, Entities.BahamasCtsSetting>(input);

        entity.SftpSSHKey = sshKey;
        entity.CtsPublicCertificate = ctsCert;
        // Encrypt sensitive data before saving
        _ctsEncryptionManager.EncryptBahamasCtsSetting(entity);

        var dtNow = Clock.Now;
        entity.SystemUserPasswordUpdatedAt = dtNow;
        if (!string.IsNullOrWhiteSpace(sshKey))
        {
            entity.SftpSSHKeyUpdatedAt = dtNow;
        }
        if (!string.IsNullOrWhiteSpace(ctsCert))
        {
            entity.CtsPublicCertificateUpdatedAt = dtNow;
        }
        entity = await _repository.InsertAsync(entity, autoSave: true);

        // Decrypt for returning to client
        var cloned = _ctsEncryptionManager.DecryptBahamasCtsSetting(entity!, true);
        // Log the creation of the Bahamas CTS setting
        await AddAuditLogForCtsSettingAsync(true, entity.SftpSSHKeyUpdatedAt.HasValue, entity.CtsPublicCertificateUpdatedAt.HasValue);
        return ObjectMapper.Map<Entities.BahamasCtsSetting, BahamasCtsSettingDto>(cloned);
    }

    public async Task<BahamasCtsSettingDto> UpdateAsync(string sshKey, string ctsCert, UpdateBahamasCtsSettingDto input)
    {
        var entity = await _repository.FirstOrDefaultAsync(x => x.Id == input.Id) ?? throw new UserFriendlyException("Bahamas CTS setting not found.");
        entity = _ctsEncryptionManager.DecryptBahamasCtsSetting(entity);

        // Changed Value Use For Audit Trails
        var dbPasswordForDisplay = GetPasswordForDisplay(entity.SystemUserPassword);
        var systemUserPasswordChanged = !input.SystemUserPassword.StartsWith("****") && !input.SystemUserPassword.Equals(dbPasswordForDisplay, StringComparison.InvariantCultureIgnoreCase);
        var sftpSSHKeyChanged = false;
        if (!string.IsNullOrEmpty(sshKey))
        {
            sftpSSHKeyChanged = !sshKey.Equals(entity.SftpSSHKey, StringComparison.InvariantCultureIgnoreCase);
        }

        var ctsCertChanged = false;
        if (!string.IsNullOrEmpty(ctsCert))
        {
            ctsCertChanged = !ctsCert.Equals(entity.CtsPublicCertificate, StringComparison.InvariantCultureIgnoreCase);
        }

        if (systemUserPasswordChanged)
        {
            // If the password is changed, update the timestamp
            entity.SystemUserPasswordUpdatedAt = Clock.Now;
        }
        
        var dbPassword = entity.SystemUserPassword;
        // Map input to entity, since UI has masked password, we need to retain the original password if not changed
        ObjectMapper.Map(input, entity);
        if (!systemUserPasswordChanged)
        {
            // If the password is not changed, retain the original password
            entity.SystemUserPassword = dbPassword;
        }
        if (sftpSSHKeyChanged)
        {
            entity.SftpSSHKeyUpdatedAt = Clock.Now;
            entity.SftpSSHKey = sshKey;
        }
        if (ctsCertChanged)
        {
            entity.CtsPublicCertificate = ctsCert;
            entity.CtsPublicCertificateUpdatedAt = Clock.Now;
        }
        // Encrypt sensitive data before saving
        _ctsEncryptionManager.EncryptBahamasCtsSetting(entity);

        await _repository.UpdateAsync(entity, autoSave: true);

        //Ensure the unit of work is completed, otherwise the decrypt code would make plain text saved to Db
        // Decrypt for returning to client
        var cloned = _ctsEncryptionManager.DecryptBahamasCtsSetting(entity, true);

        // Log the update of the Bahamas CTS setting
        await AddAuditLogForCtsSettingAsync(systemUserPasswordChanged, sftpSSHKeyChanged, ctsCertChanged);
        var dto = ObjectMapper.Map<BahamasCtsSetting, BahamasCtsSettingDto>(cloned);
        return dto;
    }

    [RemoteService(false)]
    public async Task<BahamasCtsSettingDto> GetAsync(Guid id)
    {
        var entity = await _repository.GetAsync(id);

        // Decrypt sensitive data before returning
        _ctsEncryptionManager.DecryptBahamasCtsSetting(entity);

        return ObjectMapper.Map<BahamasCtsSetting, BahamasCtsSettingDto>(entity);
    }

    [RemoteService(false)]
    public async Task<BahamasCtsSettingDto?> GetCurrentSettingsAsync()
    {
        var query = await _repository.GetQueryableAsync();
        var entity = await AsyncExecuter.FirstOrDefaultAsync(query.OrderByDescending(x => x.CreationTime));

        if (entity == null)
            return null;

        // Decrypt sensitive data before returning
        entity = _ctsEncryptionManager.DecryptBahamasCtsSetting(entity, true);

        return ObjectMapper.Map<BahamasCtsSetting, BahamasCtsSettingDto>(entity);
    }

    public async Task<BahamasCtsSettingDto?> GetCurrentSettingsForDisplayAsync()
    {
        var currentSettings = await GetCurrentSettingsAsync();

        if (currentSettings == null)
            return null;

        // Create a copy to avoid modifying the original
        var displayDto = new BahamasCtsSettingDto
        {
            Id = currentSettings.Id,
            SystemUserName = currentSettings.SystemUserName,
            SftpUserName = currentSettings.SftpUserName,
            CtsPublicCertificateUpdatedAt = currentSettings.CtsPublicCertificateUpdatedAt,
            SystemUserPasswordUpdatedAt = currentSettings.SystemUserPasswordUpdatedAt,
            SftpSSHKeyUpdatedAt = currentSettings.SftpSSHKeyUpdatedAt
        };

        // Mask sensitive data for display
        displayDto.SftpSSHKey = GetCertficateForDisplay(currentSettings.SftpSSHKey);

        // CtsPublicCertificate: only first 30 characters
        displayDto.CtsPublicCertificate = GetCertficateForDisplay(displayDto.CtsPublicCertificate);

        displayDto.SystemUserPassword = GetPasswordForDisplay(currentSettings.SystemUserPassword);
        return displayDto;
    }

    public static string GetCertficateForDisplay(string cert)
    {
        if (string.IsNullOrEmpty(cert))
            return cert;
        // CtsPublicCertificate: only first 30 characters
        if (cert.Length > 30)
        {
            return cert.Substring(0, 30) + "...";
        }
        else
        {
            return cert;
        }
    }
    public static string GetPasswordForDisplay(string password)
    {
        if (string.IsNullOrEmpty(password))
            return password;

        // SystemUserPassword: 10 asterisks + last 4 characters
        if (password.Length >= 4)
        {
            var lastFour = password.Substring(password.Length - 4);
            return "**********" + lastFour;
        }
        else
        {
            return "**********";
        }
    }
    public async Task<bool> IsValidSSHKeyFile(IFormFile? file)
    {
        if (file == null || file.Length == 0)
            return true;

        var allowedExtensions = new HashSet<string> { ".ppk" };
        var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(fileExtension))
            throw new UserFriendlyException($"Unsupported SSH key file extension: {fileExtension}, it must a valid .ppk file");

        var isValid = await IsValidPpkFileAsync(file);
        if (!isValid)
        {
            throw new UserFriendlyException($"Unsupported SSH key file content.");
        }

        return true;
    }

    private static async Task<bool> IsValidPpkFileAsync(IFormFile formFile)
    {
        const string PPK_HEADER_PREFIX = "PuTTY-User-Key-File-";

        try
        {
            using var stream = formFile.OpenReadStream();
            using var reader = new StreamReader(stream, Encoding.UTF8);

            // Read only the first line
            string firstLine = await reader.ReadLineAsync() ?? "";

            return !string.IsNullOrEmpty(firstLine) &&
                   firstLine.StartsWith(PPK_HEADER_PREFIX, StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> IsValidCtsPublicCertificate(IFormFile? file)
    {
        if (file == null || file.Length == 0)
            return true;

        var allowedExtensions = new HashSet<string> { ".cer", ".crt", ".txt" };
        var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(fileExtension))
            throw new UserFriendlyException($"Unsupported CTS certificate file extension: {fileExtension}, it must be either .cer or .crt or .txt");

        try
        {
            await using var ms = new MemoryStream();
            await file.OpenReadStream().CopyToAsync(ms);
            var rawData = ms.ToArray();
            _ = new X509Certificate2(rawData);
            return true;
        }
        catch
        {
            throw new UserFriendlyException("Failed to load X.509 certificate. Ensure the CTS file is a valid certificate.");
        }
    }

    private async Task AddAuditLogForCtsSettingAsync(bool? systemUserPasswordChanged, bool? sftpSSHKeyChanged, bool? ctsCertificateChanged)
    {
        try
        {
            var eto = new AuditCtsSettingEto
            {
                UserName = CurrentUser.UserName,
                UserId = CurrentUser.Id,
                IPAddress = _auditWebInfo.IPAddress,
                Action = AuditActionEnum.CtsSetting,
                TenantId = CurrentTenant.Id,
                AuditDateTime = DateTime.UtcNow,
                NewValue = new AuditCtsSettingDto()
                {
                    EmailAddress = CurrentUser.Email,
                    UserName = CurrentUser.UserName,
                    FirstName = CurrentUser.Name,
                    LastName = CurrentUser.SurName,
                    UserId = CurrentUser.Id.ToString(),
                    SystemUserPasswordChanged = systemUserPasswordChanged ?? false,
                    SftpSSHKeyChanged = sftpSSHKeyChanged ?? false,
                    CtsCertificateChanged = ctsCertificateChanged ?? false
                }
            };
            await _auditEventBus.PublishAsync(eto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while publishing audit log for CTS setting.");
        }
    }
}