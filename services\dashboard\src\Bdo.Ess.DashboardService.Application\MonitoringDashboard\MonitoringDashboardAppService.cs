﻿using Bdo.Ess.DashboardService.DataSync;
using Bdo.Ess.Shared.Constants.DataSync;
using Bdo.Ess.Shared.Constants.Saas;
using Bdo.Ess.Shared.Utility.Extensions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Caching;
using Volo.Abp.Uow;

namespace Bdo.Ess.DashboardService.MonitoringDashboard
{
    public class MonitoringDashboardAppService : ApplicationService, IMonitoringDashboardAppService
    {
        private readonly IDashboardSettingAppService _dashboardSettingAppService;
        private readonly MonitoringDashboardManager _manager;
        private readonly IJobLogAppService _jobLogAppService;
        private readonly CaDashboardManager _caDashboardManager;
        private readonly IDistributedCache<List<int>, string> _readTimeSyncCache;
        private static readonly string KeyRealTime = "ReadTimeYears";

        public MonitoringDashboardAppService(
            IDashboardSettingAppService dashboardSettingAppService,
            IJobLogAppService jobLogAppService,
            CaDashboardManager caDashboardManager,
            MonitoringDashboardManager manager,
            IDistributedCache<List<int>, string> readTimeSyncCache
            )
        {
            _dashboardSettingAppService = dashboardSettingAppService;
            _manager = manager;
            _jobLogAppService = jobLogAppService;
            _caDashboardManager = caDashboardManager;
            _readTimeSyncCache = readTimeSyncCache;
        }

        public async Task<DashboardStatsSummaryDto> GetDashboardStatsSummary(int year)
        {
            var rtDto = new DashboardStatsSummaryDto();
            var rtList = await _caDashboardManager.GetDashboardOverviewMain(new List<int> { year });
            if (rtList != null && rtList.Any())
            {
                var found = rtList[0];
                rtDto.FiscalYear = year;
                rtDto.NumOfEntities = found.NumOfEntities_4_2;
                rtDto.NumOfEntitiesFilingOverdue = found.NumOfEntitiesFilingOverdue_4_2;
                rtDto.NumOfFilingSubmitted = found.NumOfFilingSubmitted_4_2;
                rtDto.NumOfAssessmentPending = found.NumOfAssessmentPending_4_8;
                rtDto.NumOfFiledOTAS = found.NumOfFiledOTAS_4_9;
                rtDto.NumOfESLetterPending = found.NumOfESLetterPending_6_5;
                rtDto.NumOfESLetterPublished = found.NumOfESLetterPublished_6_5;
                rtDto.NumOfESLetterGenerated = found.NumOfESLetterPending_6_5 + found.NumOfESLetterPublished_6_5;
                rtDto.NumOfAssessmentClosedBahamas = found.NumOfAssessmentClosedBahamas_6_6;
                rtDto.NumOfAssessmentNotStartedOutsideBahamas = found.NumOfAssessmentNotStartedOutsideBahamas_6_6;
                rtDto.NumOfAssessmentStartedOutsideBahamas = found.NumOfAssessmentStartedOutsideBahamas_6_6;
                rtDto.NumOfAssessmentClosedBahamas = found.NumOfAssessmentClosedBahamas_6_6;
                rtDto.TotalEvents = found.NumOfEvents_5_3;
                rtDto.TotalFilingWithEvents = found.NumOfFilingWithEvents_5_3;
                rtDto.TotalAssessmentNotStarted = found.NumOfAssessmentNotStarted_5_3;
                rtDto.PercentageOfAssessmentNotStarted = found.PercentageOfAssessmentNotStarted_5_3;
                rtDto.TotalAssessmentCompleted = found.NumOfAssessmentCompleted_5_3;
                rtDto.PercentageOfAssessmentPassed = found.PercentageOfAssessmentPassed_5_3;
                rtDto.PercentageOfAssessmentFailed = found.PercentageOfAssessmentFailed_5_3;
                rtDto.TotalAssessmentClosed = found.NumOfAssessmentClosed_5_3;
                rtDto.PercentageOfAssessmentClosed = found.PercentageOfAssessmentClosed_5_3;
            }
            return rtDto;
        }

        public async Task<List<DashboardMainDto>> GetMainOverview(int year)
        {
            var years = DashboardSettingAppService.GetPreviusThreeYears(year);
            var rtList = await _caDashboardManager.GetDashboardOverviewMain(years);
            return ObjectMapper.Map<List<DashboardMain>, List<DashboardMainDto>>(rtList);
        }

        public async Task<List<DashboardMainDto>> GetMainInformationRequestOverview(int year)
        {
            var years = DashboardSettingAppService.GetPreviusThreeYears(year);
            var threeYears = await _caDashboardManager.GetDashboardOverviewMain(years);

            var rtList = ObjectMapper.Map<List<DashboardMain>, List<DashboardMainDto>>(threeYears);
            if (DashboardSettingAppService.DashboardDataFromYear + 3 > year)
            {
                //No Dashboard data for year < 2019, add a blank row for 'Other' row
                rtList.Add(new DashboardMainDto());
                return rtList;
            }
            var earlyYears = await _caDashboardManager.GetDashboardOverviewMainEarlyThanYear(years.Min());
            if (!earlyYears.Any()) return rtList;

            var other = new DashboardMainDto();
            other.NumOfInformationRequired_4_4 = earlyYears.Sum(x => x.NumOfInformationRequired_4_4);
            other.NumOfInformationReceived_4_4 = earlyYears.Sum(x => x.NumOfInformationReceived_4_4);
            other.NumOfInformationRequestedOverDue_4_4 = earlyYears.Sum(x => x.NumOfInformationRequestedOverDue_4_4);
            rtList.Add(other);
            return rtList;
        }

        public async Task<ICollection<StatisticCountryTaxResidentDto>> GetMainCountryTaxResidents(int year)
        {
            var rtList = await _manager.GetCountryTaxResidentsMain(year);
            return ObjectMapper.Map<ICollection<StatisticCountryTaxResident>, ICollection<StatisticCountryTaxResidentDto>>(rtList);
        }

        public async Task<PagedResultDto<StatisticRADto>> GetStatisticRAs(GetStatisticRADto dto)
        {
            var duple = await _manager.GetStatisticRAs(dto);
            var result = ObjectMapper.Map<IReadOnlyList<StatisticRA>, IReadOnlyList<StatisticRADto>>(duple.Items);
            return new PagedResultDto<StatisticRADto>(duple.TotalCount, result);
        }

        public async Task<PagedResultDto<DashboardListingDto>> GetDashboardListingDetails(GetDashboardListingDto dto)
        {
            return await _caDashboardManager.GetDashboardListingDetails(dto);
        }

        public async Task<PagedResultDto<DashboardListingDto>> GetDashboardListingDetailsByActivity(GetDashboardListingByActivityDto dto)
        {
            return await _caDashboardManager.GetDashboardListingDetailsByActivity(dto);
        }

        public async Task<PagedResultDto<DashboardListingDto>> GetDashboardListingDetailsByCountry(GetDashboardListingByCountryDto dto)

        {
            return await _caDashboardManager.GetDashboardListingDetailsByCountry(dto);
        }

        public async Task<ICollection<DashboardStatisticRelevantActivityDto>> GetMainDashboardActivityOverview(int year, string sorting = "")
        {
            var activities = await _manager.GetRelevantActivitiesMain(year, sorting);
            var rtList = ObjectMapper.Map<ICollection<StatisticRelevantActivity>, ICollection<DashboardStatisticRelevantActivityDto>>(activities);
            return rtList;
        }

        public async Task<ICollection<DashboardStatisticRelevantActivityDto>> GetMainDashboardActivityOverviewExcludedEntities(int year, string sorting = "")
        {
            var activities = await _manager.GetRelevantActivitiesMain100Bahamian(year, sorting);
            var rtList = ObjectMapper.Map<ICollection<StatisticRelevantActivity>, ICollection<DashboardStatisticRelevantActivityDto>>(activities);
            return rtList;
        }

        public async Task<StatisticMainDto> GetStatisticMain(int year)
        {
            StatisticMainDto result = new StatisticMainDto();
            var main = await this._manager.GetStatisticMain(year);
            if (main != null)
            {
                result = this.ObjectMapper.Map<StatisticMain, StatisticMainDto>(main);

                //  Get RAs OCED doesn't have StatisticRAs data
                // Get Residents out of bahamans.
                var residents = await this._manager.GetCountryTaxResidents(year);

                if (residents != null)
                {
                    result.CountryTaxResidentsOutSideBahamas = this.ObjectMapper.Map<ICollection<StatisticCountryTaxResident>, ICollection<StatisticCountryTaxResidentDto>>(residents);
                }

                var relevantActivities = await this._manager.GetRelevantActivities(year);

                if (relevantActivities != null)
                {
                    result.RelevantActivities = this.ObjectMapper.Map<ICollection<StatisticRelevantActivity>, ICollection<StatisticRelevantActivityDto>>(relevantActivities);
                }

                var jurisdictions = await this._manager.GetJurisdictions(year);

                if (jurisdictions != null && jurisdictions.Count > 0)
                {
                    result.Jurisdictions = jurisdictions;
                }
            }

            return result;
        }

        public async Task<ICollection<int>> GetFiscalYears()
        {
            var result = (await _dashboardSettingAppService.GetAllFiscalYearsAsync()).OrderByDescending(x => x).ToList();
            return result;
        }

        public async Task<PagedResultDto<StatisticRedFlagEventDto>> GetStatisticRedFlagEvents(GetStatisticRedFlagEventDto dto)
        {
            var duple = await _caDashboardManager.GetStatisticRedFlagEvents(dto);
            var result = ObjectMapper.Map<IReadOnlyList<StatisticRedFlagEvent>, IReadOnlyList<StatisticRedFlagEventDto>>(duple.Items);
            return new PagedResultDto<StatisticRedFlagEventDto>(duple.TotalCount, result);
        }

        public async Task<bool> GenerateDashboardStatisticData(int fiscalYear)
        {
            var result = false;
            try
            {
                //This is for OECD Dashboard
                result = await _manager.GenerateDashboardStatisticData(fiscalYear);
            }
            catch (Exception ex)
            {
                result = false;
                Logger.LogError(ex, $"GenerateOECDDashboard failed for year: {fiscalYear}");
                // Hide exception.
                await LogInfoSync($"Error Call SP GenerateOECDDashboard for {fiscalYear}", ex);
            }

            if (!result)
            {
                Logger.LogError($"GenerateOECDDashboard failed for year: {fiscalYear}");
            }
            return result;
        }

        public async Task<bool> GenerateDashboardData(int fiscalYear)
        {
            bool result;
            try
            {
                result = await _caDashboardManager.GenerateDashboardData(fiscalYear);
            }
            catch (Exception ex)
            {
                result = false;
                Logger.LogError(ex, $"GenerateDashboardData failed for year: {fiscalYear}");
                await LogInfoSync($"Error Call SP GenerateDashboardData for {fiscalYear}", ex);
            }

            if (!result)
            {
                Logger.LogError($"GenerateDashboardData failed for year: {fiscalYear}");
            }
            return result;
        }

        private async Task<List<int>> GetYearsToUpdate()
        {
            List<int> yearList = await _readTimeSyncCache.GetAsync(KeyRealTime, true);
            await _readTimeSyncCache.RemoveAsync(KeyRealTime);
            yearList?.RemoveAll(x => x < 2019);
            return yearList ?? new List<int>();
        }

        private async Task<bool> GenerateDashboardStatisticDataYearList(List<int> years)
        {
            if (!years.Any()) return false;

            bool result = true;
            var beginYear = years.Min();
            var endYear = years.Max();
            for (int year = beginYear; year <= endYear; year++)
            {
                try
                {
                    await GenerateDashboardStatisticData(year);
                    await LogInfoSync($"Called Main Tab SP for {year}");
                    await GenerateDashboardData(year);
                    await LogInfoSync($"Called OECD Tab SP for {year}");
                }
                catch (Exception ex)
                {
                    result = false;
                    await LogInfoSync($"Error Call SP for {year}", ex);
                    //Swallow the exception for the year
                    Logger.LogError(ex, $"GenerateDashboardStatisticData failed for year: {year}");
                }
                if (!result)
                {
                    Logger.LogError($"GenerateDashboardStatisticData failed for year: {year}");
                }
            }

            return result;
        }

        public async Task<bool> GenerateDashboardStatisticDataDaily()
        {
            try
            {
                await _dashboardSettingAppService.RefreshRedFlagSettings();
                await LogInfoSync($"RefreshRedFlagSettings");
            }
            catch (Exception ex)
            {
                await LogInfoSync($"Error RefreshRedFlagSettings", ex);
            }

            var years = await GetYearsToUpdate();
            return await GenerateDashboardStatisticDataYearList(years);
        }

        //Disable method's UnitOfWork, so that each section has it's own transaction
        [UnitOfWork(IsDisabled = true)]
        public async Task<bool> GenerateDashboardStatisticData()
        {
            try
            {
                await _dashboardSettingAppService.RefreshFisicalYears();
                await LogInfoSync($"RefreshFiscalYears");
            }
            catch (Exception ex)
            {
                await LogInfoSync($"Error RefreshFiscalYears", ex);
            }
            try
            {
                await _dashboardSettingAppService.RefreshDashboardTenants();
                await LogInfoSync($"RefreshDashboardTenants");
            }
            catch (Exception ex)
            {
                await LogInfoSync($"Error RefreshDashboardTenants", ex);
            }

            try
            {
                await _dashboardSettingAppService.RefreshRedFlagSettings();
                await LogInfoSync($"RefreshRedFlagSettings");
            }
            catch (Exception ex)
            {
                await LogInfoSync($"Error RefreshRedFlagSettings", ex);
            }

            bool rt;

            //Can not wrap with UnitOfWork, otherwise there is exception about connection already has transaction
            var years = await _dashboardSettingAppService.GetAllFiscalYearsAsync();
            rt = await GenerateDashboardStatisticDataYearList(years);

            try
            {
                await _jobLogAppService.CleanOldLogsAsync();
                await LogInfoSync($"CleanOldLogsAsync");
            }
            catch (Exception ex)
            {
                await LogInfoSync($"Error CleanOldLogsAsync", ex);
            }
            return rt;
        }

        private async Task LogInfoSync(string info, Exception? ex = null)
        {
            try
            {
                var eto = new JobLogEto("Dashboard", info, ex);
                await _jobLogAppService.WriteLogAsync(eto);
            }
            catch
            {
                //Swallow exception for such log
            }
        }

        //OECD Tab's Update Time
        public async Task<DateTime?> GetLatestStatisticDateTime()
        {
            return await _manager.GetLatestStatisticDateTime();
        }

        //Main Tab's Update Time
        public async Task<DateTime?> GetLatestDashboardUpdateTime(int year)
        {
            var dt = await _caDashboardManager.GetLatestDashboardUpdateTime(year);
            if (dt == null) return dt;
            return dt.Value.UtcToLocalTime(TenantConstants.TenantTimeZone);
        }

        public async Task<List<TenantDto>> GetAllTenants()
        {
            return await _dashboardSettingAppService.GetAllTenants();
        }

        public async Task<List<TenantDto>> GetAllAdvancedSearchTenants()
        {
            return await _dashboardSettingAppService.GetAllAdvancedSearchTenants();
        }

        public async Task<int> RefreshDashboardTenants()
        {
            return await _dashboardSettingAppService.RefreshDashboardTenants();
        }

        public async Task<PagedResultDto<DashboardListingDto>> GetStatisticRedFlagEventsListing(GetStatisticRedFlagEventListingDto dto)
        {
            if (dto.DashboardListingType != DashboardListingType.NumOfAssessmentNotStarted_5_2 &&
                dto.DashboardListingType != DashboardListingType.NumOfAssessmentClosed_5_2 &&
                dto.DashboardListingType != DashboardListingType.NumOfAssessmentCompleted_5_2 &&
                dto.DashboardListingType != DashboardListingType.NumOfAssessmentFailed_5_2 &&
                dto.DashboardListingType != DashboardListingType.NumOfAssessmentPassed_5_2
                )
            {
                throw new ArgumentException("Invalid Dashboard Listing Type parameter, should between 14 to 18.");
            }
            return await _caDashboardManager.GetStatisticRedFlagEventsListing(dto);
        }
    }
}