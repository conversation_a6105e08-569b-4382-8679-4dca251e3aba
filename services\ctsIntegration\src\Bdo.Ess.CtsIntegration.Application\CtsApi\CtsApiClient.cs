﻿using Bdo.Ess.CtsIntegration.Certificate;
using Bdo.Ess.CtsIntegration.CtsApi.Models;
using Bdo.Ess.CtsIntegration.CtsPackageGeneration;
using Bdo.Ess.CtsIntegration.PackageGeneration;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using HttpMultipartParser;
using Volo.Abp;
using Volo.Abp.Caching;

using JsonSerializer = System.Text.Json.JsonSerializer;
using Bdo.Ess.CtsIntegration.BahamasCtsSettings;

namespace Bdo.Ess.CtsIntegration.CtsApi
{
    public class CtsApiClient : CtsIntegrationAppService, ICtsApiClient
    {
        //private readonly HttpClient _httpClient;
        private readonly IBahamasCtsSettingAppService _bahamasCtsSettingAppService;
        private readonly CtsApiConfiguration _configuration;
        private readonly IDistributedCache<string> _distributedCache;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly DistributedCacheEntryOptions _accessTokenCacheOptions = new()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(25), // 30 minutes expiration
        };

        private const string AccessTokenCacheKey = "CTS_ACCESS_TOKEN";

        public CtsApiClient(
            IHttpClientFactory httpClientFactory,
            IOptions<CtsApiConfiguration> configuration,
            IBahamasCtsSettingAppService bahamasCtsSettingAppService,
            IDistributedCache<string> distributedCache)
        {
            _httpClientFactory = httpClientFactory;
            _bahamasCtsSettingAppService = bahamasCtsSettingAppService;
            _configuration = configuration!.Value;
            _distributedCache = distributedCache;
        }

        /// <summary>
        /// Gets access token with caching. Token is cached for 30 minutes.
        /// </summary>
        /// <returns>JWT access token</returns>
        private async Task<string> GetAccessTokenAsync()
        {
            // Try to get cached token first
            var cachedToken = await _distributedCache.GetAsync(AccessTokenCacheKey);
            if (!string.IsNullOrEmpty(cachedToken))
            {
                return cachedToken;
            }

            // If no cached token, get a new one
            var newToken = await GetAccessTokenFromCtsAsync();

            // Cache the new token
            await _distributedCache.SetAsync(AccessTokenCacheKey, newToken, _accessTokenCacheOptions);

            return newToken;
        }

        /// <summary>
        /// Gets a new access token from CTS API (bypasses cache)
        /// </summary>
        /// <returns>JWT access token</returns>
        private async Task<string> GetAccessTokenFromCtsAsync()
        {
            //SetHttpClient();

            var ctsSettings = await _bahamasCtsSettingAppService.GetCurrentSettingsAsync();
            var publicKeyBytes = Convert.FromBase64String(ctsSettings!.CtsPublicCertificate);
            // Note: In CTS API, the "password" is actually the public key itself
            var encryptedPassword = AesManager.EncryptPasswordJwe(ctsSettings!.SystemUserPassword, publicKeyBytes);

            // Prepare the authentication request
            var authRequest = new CtsAuthRequest
            {
                UserPassword = encryptedPassword
            };

            var requestJson = JsonSerializer.Serialize(authRequest);
            var requestContent = new StringContent(requestJson, Encoding.UTF8, "application/json");

            // Build the authentication endpoint: /auth/<ISOCountryCd>_<UserID>
            var authEndpoint = $"{_configuration.BaseUrl.TrimEnd('/')}/auth/{CtsConstants.CtsBahamsSenderCode}_{ctsSettings.SystemUserName.ToLower()}";

            using var httpClient = _httpClientFactory.CreateClient("CtsClient");
            var response = await httpClient.PostAsync(authEndpoint, requestContent);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = response.StatusCode switch
                {
                    System.Net.HttpStatusCode.Forbidden => "Access denied - invalid credentials or operation blocked",
                    System.Net.HttpStatusCode.Unauthorized => "Unauthorized - invalid credentials",
                    _ => $"Authentication failed with status {response.StatusCode}"
                };

                throw new BusinessException($"CTS API authentication failed: {errorMessage}");
            }

            // Parse the JSON response to extract the access token
            try
            {
                var authResponse = JsonSerializer.Deserialize<CtsAuthResponse>(responseContent);

                if (authResponse == null || string.IsNullOrEmpty(authResponse.AccessToken))
                {
                    throw new BusinessException("Invalid or empty access token received from CTS API");
                }

                return authResponse.AccessToken;
            }
            catch (System.Text.Json.JsonException ex)
            {
                throw new BusinessException($"Failed to parse CTS API authentication response: {ex.Message}");
            }
        }

        /// <summary>
        /// Clears the cached access token to force refresh on next request
        /// </summary>
        public async Task ClearAccessTokenCacheAsync()
        {
            await _distributedCache.RemoveAsync(AccessTokenCacheKey);
        }

        /// <summary>
        /// Downloads public certificates for all countries from CTS API
        /// </summary>
        /// <param name="countryCodes">List of country codes to download certificates for</param>
        /// <returns>Dictionary mapping country codes to their PEM-formatted certificates</returns>
        public async Task<Dictionary<string, string>> GetCertificatesAsync(IEnumerable<string> countryCodes)
        {
            Check.NotNull(countryCodes, nameof(countryCodes));
            var countryList = countryCodes.Where(c => !string.IsNullOrWhiteSpace(c)).ToList();

            //Handle countries using Hub
            var countriesUseHub = _configuration.CountriesUseHub?.Split(',')
                .Where(x=>!string.IsNullOrWhiteSpace(x))
                .ToList();
            if (countriesUseHub != null && countriesUseHub.Count > 0)
            {
                foreach (var countryCode in countriesUseHub)
                {
                    countryList.Remove(countryCode);
                    countryList.Add($"{countryCode.Trim().ToUpperInvariant()}.00" );
                }
            }

            var results = new Dictionary<string, string>();

            var accessToken = await GetAccessTokenAsync();
            var semaphore = new SemaphoreSlim(1, 1); // Limit concurrent requests to 1

            var tasks = countryList.Select(async countryCode =>
            {
                await semaphore.WaitAsync();
                try
                {
                    var certificate = await GetCertificateAsync(countryCode, accessToken);
                    if (!string.IsNullOrEmpty(certificate))
                    {
                        lock (results)
                        {
                            results[countryCode] = certificate;
                        }
                    }
                }
                catch (Exception)
                {
                    // Continue with other countries even if one fails
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
            semaphore.Dispose();

            return results;
        }

        public async Task<string?> GetCertificateAsync(string countryCode, string? accessToken = null)
        {
            if (string.IsNullOrWhiteSpace(countryCode))
            {
                return null;
            }

            // Ensure country code is uppercase as required by CTS API
            countryCode = countryCode.ToUpperInvariant();

            if (string.IsNullOrWhiteSpace(accessToken))
            {
                accessToken = await GetAccessTokenAsync();
            }

            // CTS API endpoint: GET /certificates/<ISOCountryCd>
            var certificateEndpoint = $"{_configuration.BaseUrl.TrimEnd('/')}/certificates/{countryCode}";

            if (countryCode == "QM")
            {
                // Log request details
                var requestInfo = new
                {
                    Endpoint = certificateEndpoint,
                    Method = "GET",
                    CountryCode = countryCode,
                    Headers = new
                    {
                        Authorization = "Bearer [REDACTED]",
                        Accept = "multipart/form-data"
                    },
                    RequestTime = DateTime.UtcNow
                };
                Logger.LogInformation("CTS GetCertificate Request: {RequestInfo}", JsonSerializer.Serialize(requestInfo));
            }

            using var request = new HttpRequestMessage(HttpMethod.Get, certificateEndpoint);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            // Accept multipart/form-data for certificate file download
            request.Headers.Add("Accept", "multipart/form-data");
            using var httpClient = _httpClientFactory.CreateClient("CtsClient");
            var response = await httpClient.SendAsync(request);

            //When no certificate for the country, the response code would be 403
            string errorContent = "";
            if (!response.IsSuccessStatusCode)
            {
                errorContent = await response.Content.ReadAsStringAsync();
            }
            if (countryCode == "QM")
            {
                // Log error response
                var errorResponseInfo = new
                {
                    CountryCode = countryCode,
                    StatusCode = (int)response.StatusCode,
                    ReasonPhrase = response.ReasonPhrase,
                    ResponseTime = DateTime.UtcNow,
                    ResponseCode = response.StatusCode,
                    ErrorContent = errorContent,
                    Headers = response.Headers.ToDictionary(h => h.Key, h => string.Join(", ", h.Value))
                };
                Logger.LogError("CTS GetCertificate Response: {ErrorResponseInfo}", JsonSerializer.Serialize(errorResponseInfo));
            }
            //When no certificate for the country, the response code would be 403
            if (!response.IsSuccessStatusCode)
            {
                Logger.LogError("Failed to download certificate for {CountryCode}. Status: {StatusCode}, Response: {Response}",
                    countryCode, response.StatusCode, errorContent);
                throw new BusinessException($"Failed to download certificate for {countryCode}: {response.StatusCode}");
            }
            
            var stream = await response.Content.ReadAsStreamAsync();
            var parser = await MultipartFormDataParser.ParseAsync(stream);

            // Find the file by name pattern (starts with "x509" and ends with ".crt")
            var file = parser.Files.FirstOrDefault();
            Logger.LogInformation("CTS GetCertificate File Count: {FileCount} for CountryCode: {CountryCode}", parser.Files.Count, countryCode);
            if (file != null && file.Data != null)
            {
                using var memoryStream = new MemoryStream();
                await file.Data.CopyToAsync(memoryStream);
                byte[] fileBytes = memoryStream.ToArray();
                return Convert.ToBase64String(fileBytes); 
            }

            if (countryCode == "QM")
            {
                // Log when no certificate file found
                var noFileResponseInfo = new
                {
                    CountryCode = countryCode,
                    StatusCode = (int)response.StatusCode,
                    FilesFound = parser.Files.Count,
                    Headers = response.Headers.ToDictionary(h => h.Key, h => string.Join(", ", h.Value))
                };
                Logger.LogWarning("CTS GetCertificate No File Response: {NoFileResponseInfo}", JsonSerializer.Serialize(noFileResponseInfo));
            }
            throw new InvalidOperationException("Certificate file not found in response");
 
        }

        /*
        private void SetHttpClient()
        {
            // Ensure the base URL is set correctly
            if (string.IsNullOrWhiteSpace(_configuration.BaseUrl))
            {
                throw new BusinessException("CTS API base URL is not configured.");
            }
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            _httpClient.Timeout = TimeSpan.FromSeconds(_configuration?.TimeoutSeconds ?? 30);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "ESS-CtsIntegration/1.0");
        }
        */

        public async Task<CtsTransmissionStatusResponse> GetTransmissionStatusAsync(string filename, string senderFileId)
        {
            var ret = new CtsTransmissionStatusResponse();
            var statusEndpoint = $"{_configuration.BaseUrl.TrimEnd('/')}/status?filename={filename}&senderfileid={senderFileId}";

            var request = new HttpRequestMessage(HttpMethod.Get, statusEndpoint);

            var accessToken = await GetAccessTokenAsync();

            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            request.Headers.Add("Accept", "application/json");

            using var httpClient = _httpClientFactory.CreateClient("CtsClient");
            var response = await httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                ret = JsonSerializer.Deserialize<CtsTransmissionStatusResponse>(json) ?? new CtsTransmissionStatusResponse();
            }

            return ret;
        }

        public async Task<CtsMultipleTransmissionStatusResult> GeAllTransmissionStatusAsync(IEnumerable<CtsTransmissionStatusRequest> requests)
        {
            Check.NotNull(requests, nameof(requests));

            // Use semaphore to limit concurrent requests (similar to GetCertificatesAsync pattern)
            var semaphore = new SemaphoreSlim(1, 1); // Limit concurrent requests to 1
            var results = new CtsMultipleTransmissionStatusResult();

            var tasks = requests.Select(async request =>
            {
                await semaphore.WaitAsync();
                try
                {
                    var StatusResponse = await GetTransmissionStatusAsync(request.FileName, request.SenderFileId);
                    lock (results)
                    {
                        results.Responses.Add(StatusResponse);
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Failed to get transmission status for filename: {Filename}, senderFileId: {SenderFileId}",
                        request.FileName, request.SenderFileId);
                    lock (results)
                    {
                        results.Errors.Add($"Error for {request.FileName} ({request.SenderFileId}): {ex.Message}");
                    }
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
            semaphore.Dispose();

            return results;
        }
    }
}