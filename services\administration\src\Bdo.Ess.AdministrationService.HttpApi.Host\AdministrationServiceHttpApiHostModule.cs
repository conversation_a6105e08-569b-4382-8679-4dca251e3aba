using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Bdo.Ess.AdministrationService.DbMigrations;
using Bdo.Ess.AdministrationService.EntityFrameworkCore;
using Bdo.Ess.Shared.Hosting.AspNetCore;
using Bdo.Ess.Shared.Hosting.Microservices;
using Prometheus;
using Volo.Abp;
using Volo.Abp.Account;
using Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Http.Client.IdentityModel.Web;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict;
using Volo.Abp.PermissionManagement;
using Bdo.Ess.EconomicSubstanceService;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Bdo.Ess.Shared.Hosting.HealthChecks;
using Bdo.Ess.Shared.Hosting.AspNetCore.HealthChecks;
using Bdo.Ess.CorporateEntityService;
using Bdo.Ess.LookupService;
using Bdo.Ess.SearchService;
using Bdo.Ess.IdentityService.EntityFrameworkCore;
using Bdo.Ess.AuditService;
using Bdo.Ess.DashboardService;
using Bdo.Ess.Shared.HttpApi.Audit;
using Bdo.Ess.Shared.HttpApi;
using Volo.Abp.Security.Claims;
using Volo.Abp.TextTemplateManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.Http.Client;
using BDO.RF.NetMiddleware.DependencyInjection;

namespace Bdo.Ess.AdministrationService;

[DependsOn(
    typeof(EssSharedLocalizationModule),
    typeof(AbpHttpClientIdentityModelWebModule),
    typeof(AbpAspNetCoreMvcUiMultiTenancyModule),
    typeof(AbpIdentityHttpApiClientModule),
    typeof(AbpAccountAdminApplicationContractsModule),
    typeof(AbpAccountPublicApplicationContractsModule),
    typeof(EssSharedHostingMicroservicesModule),
    typeof(AdministrationServiceApplicationModule),
    typeof(AdministrationServiceEntityFrameworkCoreModule),
    typeof(AdministrationServiceHttpApiModule),
    typeof(AbpOpenIddictProDomainSharedModule),
    typeof(EconomicSubstanceServiceApplicationContractsModule),
    typeof(CorporateEntityServiceApplicationContractsModule),
    typeof(LookupServiceApplicationContractsModule),
    typeof(IdentityServiceEntityFrameworkCoreModule),
    typeof(AuditServiceApplicationContractsModule),
    typeof(DashboardServiceApplicationContractsModule),
    typeof(ESSSharedHttpApiModule),
    typeof(SearchServiceApplicationContractsModule)
)]
public class AdministrationServiceHttpApiHostModule : AbpModule
{
	public override void PreConfigureServices(ServiceConfigurationContext context)
	{
		PreConfigure<AbpHttpClientBuilderOptions>(options =>
		{
			options.ProxyClientActions.Add((remoteServiceName, clientBuilder, client) =>
			{
				client.Timeout = TimeSpan.FromMinutes(55);
			});
		});
	}


	public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Enable if you need these
        // var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();
                
        JwtBearerConfigurationHelper.Configure(context, "AdministrationService");
        
        //Version 7 code
        /*SwaggerConfigurationHelper.ConfigureWithAuth(
            context: context,
            authority: configuration["AuthServer:Authority"]!,
            scopes: new
                Dictionary<string, string>                 {
                    {"AdministrationService", "Administration Service API"}
                },
            apiTitle: "Administration Service API"
        );
        */

        SwaggerConfigurationHelper.ConfigureWithOidc(
            context: context,
            authority: configuration["AuthServer:Authority"]!,
            scopes: new[] { "AdministrationService" },
            flows: new[] { "authorization_code" },
            discoveryEndpoint: configuration["AuthServer:MetadataAddress"],
            apiTitle: "Administration Service API"
        );
        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = false;
        });
        context.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(
                        configuration["App:CorsOrigins"]?
                            .Split(",", StringSplitOptions.RemoveEmptyEntries)
                            .Select(o => o.Trim().RemovePostFix("/"))
                            .ToArray() ?? Array.Empty<string>()
                    )
                    .WithAbpExposedHeaders()
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });

        context.Services.AddFeatureRequestServices(configuration);

        Configure<PermissionManagementOptions>(options =>
        {
            options.IsDynamicPermissionStoreEnabled = true;
        });

        Configure<FeatureManagementOptions>(options =>
        {
            options.IsDynamicFeatureStoreEnabled = true;
        });

        Configure<TextTemplateManagementOptions>(options =>
        {
            options.IsDynamicTemplateStoreEnabled = true;
        });

        Configure<SettingManagementOptions>(options =>
        {
            options.IsDynamicSettingStoreEnabled = true;
        });
        context.Services.AddBdoHealthChecks(new[] {
            new ConnectionProperties
            {
                ConnectionString = configuration["ConnectionStrings:AdministrationService"] ?? "",
                DatabaseType = DatabaseType.SQLServer
            } }, new[] { "Administration Service" });
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseCorrelationId();
        app.UseAbpRequestLocalization();
        app.UseStaticFiles();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        app.UseCors();
        app.UseHttpMetrics();
        app.UseAuthentication();
        app.UseAbpClaimsMap();
        app.UseMultiTenancy();
        app.UseUnitOfWork();
        //app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "Administration Service API");
            options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
        });
        app.UseAbpSerilogEnrichers();
        app.UseAuditing();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapMetrics();
        });

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHealthChecks(HealthCheckHelper.LiveUrl, new HealthCheckOptions
            {
                Predicate = h => h.Name == HealthCheckHelper.LivenessName
            });
            endpoints.MapHealthChecks(HealthCheckHelper.ReadyUrl, new HealthCheckOptions
            {
                Predicate = (check) => check.Tags.Contains(HealthCheckHelper.ReadyTag)
            });
        });
    }

    public async override Task OnPostApplicationInitializationAsync(ApplicationInitializationContext context)
    {
        var env = context.GetEnvironment();

        if (!env.IsDevelopment())
        {
            using (var scope = context.ServiceProvider.CreateScope())
            {
                await scope.ServiceProvider
                    .GetRequiredService<AdministrationServiceDatabaseMigrationChecker>()
                    .CheckAndApplyDatabaseMigrationsAsync();
            }
        }
    }
}
