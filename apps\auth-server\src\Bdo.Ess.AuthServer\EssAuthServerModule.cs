using Bdo.Ess.AdministrationService.EntityFrameworkCore;
using Bdo.Ess.IdentityService.EntityFrameworkCore;
using Bdo.Ess.SaasService.EntityFrameworkCore;
using Bdo.Ess.Shared.Hosting.AspNetCore;
using Bdo.Ess.Shared.Hosting.AspNetCore.HealthChecks;
using Bdo.Ess.Shared.Hosting.HealthChecks;
using Bdo.Ess.Shared.Hosting.Twilio;
using Bdo.Ess.Shared.HttpApi;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Google;
using Microsoft.AspNetCore.Authentication.MicrosoftAccount;
using Microsoft.AspNetCore.Authentication.Twitter;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using OpenIddict.Server;
using OpenIddict.Server.AspNetCore;
using Owl.TokenWildcardIssuerValidator;
using Prometheus;
using StackExchange.Redis;
using System;
using System.IO;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using Volo.Abp;
using Volo.Abp.Account;
using Volo.Abp.Account.Public.Web;
using Volo.Abp.Account.Public.Web.ExternalProviders;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.MultiTenancy;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.Auditing;
using Volo.Abp.BackgroundJobs.RabbitMQ;
using Volo.Abp.Caching;
using Volo.Abp.Caching.StackExchangeRedis;
using Volo.Abp.Emailing;
using Volo.Abp.EventBus.RabbitMq;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.OpenIddict.WildcardDomains;
using Volo.Abp.Security.Claims;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;
using Volo.Saas.Host;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Bdo.Ess.Shared.Hosting.HealthChecks;
using Serilog;
using Bdo.Ess.Shared.Hosting.AspNetCore.HealthChecks;
using Microsoft.AspNetCore.Identity;
using Bdo.Ess.Shared.Hosting.Twilio;
using Volo.Abp.AspNetCore.MultiTenancy;
using Volo.Abp.OpenIddict.WildcardDomains;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX.Bundling;
using OpenIddict.Server;
using Owl.TokenWildcardIssuerValidator;
using Bdo.Ess.Shared.HttpApi.Audit;
using Bdo.Ess.Shared.HttpApi;
using Autofac.Core;

namespace Bdo.Ess.AuthServer;

[DependsOn(
	typeof(AbpCachingStackExchangeRedisModule),
	typeof(AbpEventBusRabbitMqModule),
	typeof(AbpBackgroundJobsRabbitMqModule),
	typeof(AbpAccountPublicWebOpenIddictModule),
	typeof(AbpAccountPublicApplicationModule),
	typeof(AbpAccountPublicHttpApiModule),
	typeof(AbpAccountAdminApplicationModule),
	typeof(AbpAccountAdminHttpApiModule),
	typeof(SaasHostApplicationContractsModule),
	typeof(AbpAspNetCoreMvcUiLeptonXThemeModule),
	typeof(AdministrationServiceEntityFrameworkCoreModule),
	typeof(IdentityServiceEntityFrameworkCoreModule),
	typeof(SaasServiceEntityFrameworkCoreModule),
	typeof(EssSharedHostingAspNetCoreModule),
	typeof(EssSharedLocalizationModule),
	typeof(ESSSharedHttpApiModule)
)]
public class EssAuthServerModule : AbpModule
{
	public override void PreConfigureServices(ServiceConfigurationContext context)
	{
		var hostingEnvironment = context.Services.GetHostingEnvironment();
		var configuration = context.Services.GetConfiguration();

		PreConfigure<OpenIddictBuilder>(builder =>
		{
			builder.AddValidation(options =>
			{
				options.AddAudiences("AccountService");
				options.UseLocalServer();
				options.UseAspNetCore();
			});
		});

		PreConfigure<OpenIddictServerBuilder>(builder =>
		{
			builder.SetAuthorizationCodeLifetime(TimeSpan.FromMinutes(30));
			builder.SetAccessTokenLifetime(TimeSpan.FromMinutes(30));
			builder.SetIdentityTokenLifetime(TimeSpan.FromMinutes(30));
			builder.SetRefreshTokenLifetime(TimeSpan.FromHours(8));
		});

		//Configurate Wild Card Domain options for OpenIddict only on non-local environments
		if (!hostingEnvironment.IsDevelopment() && !string.IsNullOrWhiteSpace(configuration["App:OpenIddictWildcardDomainsFormat"]))
		{
			PreConfigure<AbpOpenIddictWildcardDomainOptions>(options =>
			{
				options.EnableWildcardDomainSupport = true;
				//options.WildcardDomainsFormat.Add("https://{0}.ess:4200/");
				options.WildcardDomainsFormat.Add(configuration["App:OpenIddictWildcardDomainsFormat"]);
				options.WildcardDomainsFormat.Add($@"{configuration["App:OpenIddictWildcardDomainsFormat"]}signin-oidc");
				options.WildcardDomainsFormat.Add($@"{configuration["App:OpenIddictWildcardDomainsFormat"]}signout-callback-oidc");
			});
		}

		/*
		if (!hostingEnvironment.IsDevelopment())
		{
			PreConfigure<AbpOpenIddictAspNetCoreOptions>(options =>
			{
				options.AddDevelopmentEncryptionAndSigningCertificate = false;
			});

			PreConfigure<OpenIddictServerBuilder>(serverBuilder =>
			{
				serverBuilder.AddProductionEncryptionAndSigningCertificate("openiddict.pfx", "aa59f606-c526-4d91-9079-0b3699ae13ee");
				serverBuilder.SetIssuer(new Uri(configuration["AuthServer:Authority"]!));
			});
		}
		*/

		context.Services.AddBdoHealthChecks(new[] {
			new ConnectionProperties
			{
				ConnectionString = configuration["ConnectionStrings:IdentityService"] ?? "",
				DatabaseType = DatabaseType.SQLServer
			} }, new[] { "Auth Service" });
	}

	public override void ConfigureServices(ServiceConfigurationContext context)
	{
		var hostingEnvironment = context.Services.GetHostingEnvironment();
		var configuration = context.Services.GetConfiguration();

		if (!configuration.GetValue<bool>("App:DisablePII"))
		{
			Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
		}

		if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
		{
			Configure<OpenIddictServerAspNetCoreOptions>(options =>
			{
				options.DisableTransportSecurityRequirement = true;
			});
		}
        context.Services.Configure<DataProtectionTokenProviderOptions>(options =>
        {
            options.TokenLifespan = TimeSpan.FromMinutes(5); // Change this to your desired lifespan
        });
		ConfigureTenantResolver(hostingEnvironment, configuration);

		ConfigureBundles();
		ConfigureSwagger(context, configuration);
		ConfigureSameSiteCookiePolicy(context);
		ConfigureExternalProviders(context);

		Configure<AbpMultiTenancyOptions>(options =>
		{
			options.IsEnabled = true;
		});

		Configure<AbpAuditingOptions>(options =>
		{
			options.ApplicationName = "AuthServer";
		});

		Configure<AppUrlOptions>(options =>
		{
			options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
			options.Applications["Angular"].Urls[AccountUrlNames.PasswordReset] = "account/reset-password";
			options.Applications["Angular"].Urls[AccountUrlNames.EmailConfirmation] = "account/email-confirmation";

			options.RedirectAllowedUrls.AddRange(configuration["App:RedirectAllowedUrls"]?.Split(',') ?? Array.Empty<string>());
		});

		Configure<AbpDistributedCacheOptions>(options =>
		{
			options.KeyPrefix = "Ess:";
		});

		var dataProtectionBuilder = context.Services.AddDataProtection().SetApplicationName("Ess");
		var redis = ConnectionMultiplexer.Connect(configuration["Redis:Configuration"]!);
		dataProtectionBuilder.PersistKeysToStackExchangeRedis(redis, "Ess-Protection-Keys");

		context.Services.AddCors(options =>
		{
			options.AddDefaultPolicy(builder =>
			{
				builder
					.WithOrigins(
						configuration["App:CorsOrigins"]?
							.Split(",", StringSplitOptions.RemoveEmptyEntries)
							.Select(o => o.Trim().RemovePostFix("/"))
							.ToArray() ?? Array.Empty<string>()
					)
					.WithAbpExposedHeaders()
					.SetIsOriginAllowedToAllowWildcardSubdomains()
					.AllowAnyHeader()
					.AllowAnyMethod()
					.AllowCredentials();
			});
		});

#if DEBUG
		context.Services.Replace(ServiceDescriptor.Singleton<IEmailSender, NullEmailSender>());
#endif

		if (hostingEnvironment.IsDevelopment())
		{
			Configure<AbpVirtualFileSystemOptions>(options =>
			{
				options.FileSets.ReplaceEmbeddedByPhysical<EssSharedLocalizationModule>(Path.Combine(
					hostingEnvironment.ContentRootPath,
					$"..{Path.DirectorySeparatorChar}..{Path.DirectorySeparatorChar}..{Path.DirectorySeparatorChar}..{Path.DirectorySeparatorChar}shared{Path.DirectorySeparatorChar}Bdo.Ess.Shared.Localization"));
			});
		}
		/* TODO: Version 8 code, shall enable this code?
		Configure<LeptonXThemeOptions>(options =>
		{
			options.DefaultStyle = LeptonXStyleNames.Light;
		});
		*/
		context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
		{
			options.IsDynamicClaimsEnabled = false;
		});

		context.Services.Configure<AbpAccountOptions>(options =>
		{
			options.TenantAdminUserName = "admin";
			options.ImpersonationTenantPermission = SaasHostPermissions.Tenants.Impersonation;
			options.ImpersonationUserPermission = IdentityPermissions.Users.Impersonation;
		});
		context.Services
			.GetObject<IdentityBuilder>().AddTwilioVoiceLoginProvider();
	}

	public override void OnApplicationInitialization(ApplicationInitializationContext context)
	{
		var app = context.GetApplicationBuilder();
		var env = context.GetEnvironment();

		var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();

		app.Use(async (ctx, next) =>
		{
			if (ctx.Request.Headers.ContainsKey("from-ingress"))
			{
				ctx.Request.Scheme = "https";
			}

			await next();
		});

		if (env.IsDevelopment())
		{
			app.UseDeveloperExceptionPage();
		}

		app.UseAbpRequestLocalization();

		if (!env.IsDevelopment())
		{
			app.UseErrorPage();
		}

		app.UseCorrelationId();
		app.UseStaticFiles();
		app.UseRouting();
		app.UseAbpSecurityHeaders();
		app.UseCors();
		app.UseCookiePolicy();
		app.UseHttpMetrics();
		app.UseAuthentication();
		app.UseAbpOpenIddictValidation();
		app.UseMultiTenancy();
		app.UseAbpSerilogEnrichers();
		app.UseUnitOfWork();
		//app.UseDynamicClaims();
		app.UseAuthorization();
		app.UseSwagger();
		app.UseAbpSwaggerUI(options =>
		{
			options.SwaggerEndpoint("/swagger/v1/swagger.json", "Account Service API");
			options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
		});
		app.UseAuditing();
		app.UseConfiguredEndpoints(endpoints =>
		{
			endpoints.MapMetrics();
		});

		app.UseEndpoints(endpoints =>
		{
			endpoints.MapHealthChecks(HealthCheckHelper.LiveUrl, new HealthCheckOptions
			{
				Predicate = h => h.Name == HealthCheckHelper.LivenessName
			});
			endpoints.MapHealthChecks(HealthCheckHelper.ReadyUrl, new HealthCheckOptions
			{
				Predicate = (check) => check.Tags.Contains(HealthCheckHelper.ReadyTag)
			});
		});
	}

	private void ConfigureBundles()
	{
		Configure<AbpBundlingOptions>(options =>
		{
			options.StyleBundles.Configure(
				LeptonXThemeBundles.Styles.Global,
				bundle =>
				{
					bundle.AddFiles("/global-styles.css");
				}
			);
		});

    }

	private void ConfigureTenantResolver(IWebHostEnvironment hostingEnvironment, IConfiguration configuration)
	{
		if (hostingEnvironment.IsDevelopment() || string.IsNullOrWhiteSpace(configuration["App:AuthDomainTenantResolverFormat"]))
		{
			Configure<AbpTenantResolveOptions>(options =>
			{
				options.TenantResolvers.Clear();
				options.TenantResolvers.Add(new HeaderTenantResolveContributor());
				options.TenantResolvers.Add(new QueryStringTenantResolveContributor());
				options.TenantResolvers.Add(new CookieTenantResolveContributor());
				options.TenantResolvers.Add(new CurrentUserTenantResolveContributor()); //resolve tenant based on current user's tenant
			});
		}
		else //Non-Development environments, use sub-domain tenant resolver
		{
			Configure<AbpTenantResolveOptions>(options =>
			{
				//options.AddDomainTenantResolver("{0}.auth.ess:44322");
				options.AddDomainTenantResolver(configuration["App:AuthDomainTenantResolverFormat"]);
			});

			Configure<OpenIddictServerOptions>(options =>
			{
				options.TokenValidationParameters.IssuerValidator = TokenWildcardIssuerValidator.IssuerValidator;
				options.TokenValidationParameters.ValidIssuers = new[]
				{
					configuration["AuthServer:Authority"],
					$@"{configuration["App:AuthDomainTenantResolverFormat"] }/"
				};
			});
		}
	}

	private void ConfigureExternalProviders(ServiceConfigurationContext context)
	{
		context.Services.AddAuthentication()
			.AddGoogle(GoogleDefaults.AuthenticationScheme, options =>
			{
				options.ClaimActions.MapJsonKey(AbpClaimTypes.Picture, "picture");
			})
			.WithDynamicOptions<GoogleOptions, GoogleHandler>(
				GoogleDefaults.AuthenticationScheme,
				options =>
				{
					options.WithProperty(x => x.ClientId);
					options.WithProperty(x => x.ClientSecret, isSecret: true);
				}
			)
			.AddMicrosoftAccount(MicrosoftAccountDefaults.AuthenticationScheme, options =>
			{
				//Personal Microsoft accounts as an example.
				options.AuthorizationEndpoint = "https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize";
				options.TokenEndpoint = "https://login.microsoftonline.com/consumers/oauth2/v2.0/token";

				options.ClaimActions.MapCustomJson("picture", _ => "https://graph.microsoft.com/v1.0/me/photo/$value");
				options.SaveTokens = true;
			})
			.WithDynamicOptions<MicrosoftAccountOptions, MicrosoftAccountHandler>(
				MicrosoftAccountDefaults.AuthenticationScheme,
				options =>
				{
					options.WithProperty(x => x.ClientId);
					options.WithProperty(x => x.ClientSecret, isSecret: true);
				}
			)
			.AddTwitter(TwitterDefaults.AuthenticationScheme, options =>
			{
				options.ClaimActions.MapJsonKey(AbpClaimTypes.Picture, "profile_image_url_https");
				options.RetrieveUserDetails = true;
			})
			.WithDynamicOptions<TwitterOptions, TwitterHandler>(
				TwitterDefaults.AuthenticationScheme,
				options =>
				{
					options.WithProperty(x => x.ConsumerKey);
					options.WithProperty(x => x.ConsumerSecret, isSecret: true);
				}
			);
	}

	private X509Certificate2 GetSigningCertificate(IWebHostEnvironment hostingEnv, IConfiguration configuration)
	{
		var fileName = "authserver.pfx";
		var passPhrase = "2D7AA457-5D33-48D6-936F-C48E5EF468ED";
		var file = Path.Combine(hostingEnv.ContentRootPath, fileName);

		if (!File.Exists(file))
		{
			throw new FileNotFoundException($"Signing Certificate couldn't found: {file}");
		}

		return new X509Certificate2(file, passPhrase);
	}

	private void ConfigureSwagger(ServiceConfigurationContext context, IConfiguration configuration)
	{
		/* Version 7
		 SwaggerConfigurationHelper.ConfigureWithAuth(
			 context: context,
			 authority: configuration["AuthServer:Authority"]!,
			 scopes: new Dictionary<string, string> {
				 { "AccountService", "Account Service API" }
			 },
			 apiTitle: "Account Service API"
		 );
		 */
		SwaggerConfigurationHelper.ConfigureWithOidc(
			context: context,
			authority: configuration["AuthServer:Authority"]!,
			scopes: new[] { "AccountService" },
			flows: new[] { "authorization_code" },
			discoveryEndpoint: configuration["AuthServer:MetadataAddress"],
			apiTitle: "Account Service API"
		);
	}

	private void ConfigureSameSiteCookiePolicy(ServiceConfigurationContext context)
	{
		context.Services.AddSameSiteCookiePolicy();
	}
}