{
  "App": {
    "SelfUrl": "https://localhost:44643",
    "CorsOrigins": "https://localhost:44325,https://localhost:44353"
  },
  "AuthServer": {
    "Authority": "https://localhost:44322",
    "RequireHttpsMetadata": "true",
    "SwaggerClientId": "WebGateway_Swagger"
  },
  "RemoteServices": {
    "LookupService": {
      "BaseUrl": "https://localhost:44502/",
      "UseCurrentAccessToken": "false"
    },
    "IdentityService": {
      "BaseUrl": "https://localhost:44388/",
      "UseCurrentAccessToken": "false"
    },
    "AbpIdentity": {
      "BaseUrl": "https://localhost:44388/",
      "UseCurrentAccessToken": "false"
    },
    "EconomicSubstanceService": {
      "BaseUrl": "https://localhost:45186/",
      "UseCurrentAccessToken": "false"
    }
  },
  "IdentityClients": {
    "Default": {
      "GrantType": "client_credentials",
      "ClientId": "CtsIntegrationService",
      "ClientSecret": "1q2w3e*",
      "Authority": "https://localhost:44322",
      "Scope": "LookupService IdentityService EconomicSubstanceService" //TODO: Add other scopes as needed and remove service section
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "CtsIntegrationService": "Server=localhost,1434;Database=Ess_CtsIntegration;User Id=sa;password=**********;MultipleActiveResultSets=true;TrustServerCertificate=True",
    "AdministrationService": "Server=localhost,1434;Database=Ess_Administration;User Id=sa;password=**********;MultipleActiveResultSets=true;TrustServerCertificate=True",
    "SaasService": "Server=localhost,1434;Database=Ess_Saas;User Id=sa;password=**********;MultipleActiveResultSets=true;TrustServerCertificate=True"
  },
  "StringEncryption": {
    "DefaultPassPhrase": "FjItyg0mRv8XcFQ7"
  },
  "Redis": {
    "Configuration": "localhost:6379"
  },
  "RabbitMQ": {
    "Connections": {
      "Default": {
        "HostName": "localhost",
        "UserName": "guest",
        "Password": "guest",
        "Port": 5672
      }
    },
    "EventBus": {
      "ClientName": "Ess_CtsIntegrationService",
      "ExchangeName": "Ess"
    }
  },
  "ElasticSearch": {
    "Url": "http://localhost:9200"
  },
  "Cts": {
    "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=essangulardev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "DebugIntermFiles": true,
    "SFTP": {
      "HOST": "sftp-ppr.cts-eoi.org",
      "PORT": 4022,
      //"USERNAME": "essctssftpqa.cts.sftpadmin",
      // TODO: Remove this key and use KeyVault instead
      //"PRIVATE_KEY": "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",
      "MaxRetryAttempts": 3,
      "RetryDelayMs": 1000
    },
    "Api": {
      "BaseUrl": "https://int-ppr.cts-eoi.org/ctsapi/v1.0",
      //"Username": "ess-system-user",
      "TimeoutSeconds": 300,
      "MaxRetryAttempts": 3,
      "RetryDelayMs": 1000,
      "CountriesUseHub": "US"
    },
    "UseTestCountry": "QM"
  },
  "MaintenanceKey": "jJ36Lf4jjXU8ohSesOiSPmXOef6fgi5luia+fr0dChf1WltsWRI4Hi1OMtrHM0N4wD2tZeLr4YoiNr7bjPrA==",
  "DataProtection": {
    "Enabled": "true",
    "KeyVaultName": "ess-dp-kv-dev",
    "ManagedIdentityClientId": "ccd41834-acc7-4a1a-ac52-8c4a9f84c1eb"
  },
  "KeyVaultName": "ess-zkv-dev",
  "ManagedIdentityClientId": "ccd41834-acc7-4a1a-ac52-8c4a9f84c1eb"
}
