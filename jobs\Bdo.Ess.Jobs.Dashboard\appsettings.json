﻿{
  "AuthServer": {
    "Authority": "https://localhost:44322",
    "RequireHttpsMetadata": "false",
    "SwaggerClientId": "WebGateway_Swagger"
  },
  "RemoteServices": {
    "EconomicSubstanceService": {
      "BaseUrl": "https://localhost:45186/",
      "UseCurrentAccessToken": "false"
    },
    "DashboardService": {
      "BaseUrl": "https://localhost:44433/",
      "UseCurrentAccessToken": "false"
    },
    "CorporateEntityService": {
      "BaseUrl": "https://localhost:44450/",
      "UseCurrentAccessToken": "false"
    },
    "CtsIntegrationService": {
      "BaseUrl": "https://localhost:44643/",
      "UseCurrentAccessToken": "false"
    }
  },
  "IdentityClients": {
    "Default": {
      "GrantType": "client_credentials",
      "ClientId": "NightlyJob",
      "ClientSecret": "1q2w3e*",
      "Authority": "https://localhost:44322",
      "Scope": "EconomicSubstanceService CorporateEntityService DashboardService CtsIntegrationService",
      "RequireHttps": false
    }
  },
  "Job": {
    "SyncPreviousNdays": null,
    "BatchSize": 300,
    "WaitBetweenTasksInMinutes": 1,
    "SyncContentTypes": "0,1,2,3,4,5"
  },
  "Comments": {
    "ContentTypes": "0 for enitity, 1 for declartion, 2 for info exchange, 3 for dashboard stats, 4 for cts country certificate 5 for Cts Transmission status"
  }
}