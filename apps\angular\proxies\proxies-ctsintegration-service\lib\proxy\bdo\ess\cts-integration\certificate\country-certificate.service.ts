import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CountryCertificateService {
  apiName = 'CtsIntegrationService';
  

  refreshCertificates = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/CtsIntegrationService/country-certificates/refresh',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
