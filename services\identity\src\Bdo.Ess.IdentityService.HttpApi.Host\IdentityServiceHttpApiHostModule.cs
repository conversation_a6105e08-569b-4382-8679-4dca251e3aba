using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication.Google;
using Microsoft.AspNetCore.Authentication.MicrosoftAccount;
using Microsoft.AspNetCore.Authentication.Twitter;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Bdo.Ess.IdentityService.DbMigrations;
using Bdo.Ess.IdentityService.EntityFrameworkCore;
using Bdo.Ess.Shared.Hosting.AspNetCore;
using Bdo.Ess.Shared.Hosting.Microservices;
using Prometheus;
using Volo.Abp;
using Volo.Abp.Modularity;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Bdo.Ess.Shared.Hosting.HealthChecks;
using Bdo.Ess.Shared.Hosting.AspNetCore.HealthChecks;
using Bdo.Ess.Shared.HttpApi.Audit;
using Bdo.Ess.Shared.HttpApi;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Security.Claims;
using Volo.Abp.Http.Client;

namespace Bdo.Ess.IdentityService;

[DependsOn(
    typeof(EssSharedHostingMicroservicesModule),
    typeof(IdentityServiceEntityFrameworkCoreModule),
    typeof(IdentityServiceApplicationModule),
    typeof(IdentityServiceHttpApiModule),
    typeof(ESSSharedHttpApiModule)
)]
public class IdentityServiceHttpApiHostModule : AbpModule
{

	public override void PreConfigureServices(ServiceConfigurationContext context)
	{
		PreConfigure<AbpHttpClientBuilderOptions>(options =>
		{
			options.ProxyClientActions.Add((remoteServiceName, clientBuilder, client) =>
			{
				client.Timeout = TimeSpan.FromMinutes(55);
			});
		});
	}
	public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Enable if you need these
        // var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();


        JwtBearerConfigurationHelper.Configure(context, "IdentityService");
        //Version 7 code
        /*SwaggerConfigurationHelper.ConfigureWithAuth(
            context: context,
            authority: configuration["AuthServer:Authority"]!,
            scopes: new
                Dictionary<string, string> 
                {
                    {"IdentityService", "Identity Service API"}
                },
            apiTitle: "Identity Service API"
        );
        */
        SwaggerConfigurationHelper.ConfigureWithOidc(
            context: context,
            authority: configuration["AuthServer:Authority"]!,
            scopes: new[] { "IdentityService" },
            flows: new[] { "authorization_code" },
            discoveryEndpoint: configuration["AuthServer:MetadataAddress"],
            apiTitle: "Identity Service API"
        );
        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = false;
        });
        context.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(
                        configuration["App:CorsOrigins"]?
                            .Split(",", StringSplitOptions.RemoveEmptyEntries)
                            .Select(o => o.Trim().RemovePostFix("/"))
                            .ToArray() ?? Array.Empty<string>()
                    )
                    .WithAbpExposedHeaders()
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });

        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ExposeIntegrationServices = true;
        });
        context.Services.AddBdoHealthChecks(new[] {
            new ConnectionProperties
            {
                ConnectionString = configuration["ConnectionStrings:IdentityService"] ?? "",
                DatabaseType = DatabaseType.SQLServer
            } }, new[] { "Identity Service API" });

        //Version 8 has no this line, double check
        context.Services.AddHttpContextAccessor();
        ConfigureExternalProviders(context);
	}

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseCorrelationId();
        app.UseAbpRequestLocalization();
        app.UseStaticFiles();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        app.UseCors();
        app.UseHttpMetrics();
        app.UseAuthentication();
        app.UseAbpClaimsMap();
        app.UseMultiTenancy();
        app.UseUnitOfWork();
        //app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "Identity Service API");
            options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
        });
        app.UseAbpSerilogEnrichers();
        app.UseAuditing();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapMetrics();
        });

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHealthChecks(HealthCheckHelper.LiveUrl, new HealthCheckOptions
            {
                Predicate = h => h.Name == HealthCheckHelper.LivenessName
            });
            endpoints.MapHealthChecks(HealthCheckHelper.ReadyUrl, new HealthCheckOptions
            {
                Predicate = (check) => check.Tags.Contains(HealthCheckHelper.ReadyTag)
            });
        });
    }

    public async override Task OnPostApplicationInitializationAsync(ApplicationInitializationContext context)
    {
        var env = context.GetEnvironment();

        if (!env.IsDevelopment())
        {
            using (var scope = context.ServiceProvider.CreateScope())
            {
                await scope.ServiceProvider
                    .GetRequiredService<IdentityServiceDatabaseMigrationChecker>()
                    .CheckAndApplyDatabaseMigrationsAsync();
            }
        }
    }

    private void ConfigureExternalProviders(ServiceConfigurationContext context)
    {
        context.Services
        .AddDynamicExternalLoginProviderOptions<GoogleOptions>(
            GoogleDefaults.AuthenticationScheme,
            options =>
            {
                options.WithProperty(x => x.ClientId);
                options.WithProperty(x => x.ClientSecret, isSecret: true);
            }
        )
        .AddDynamicExternalLoginProviderOptions<MicrosoftAccountOptions>(
            MicrosoftAccountDefaults.AuthenticationScheme,
            options =>
            {
                options.WithProperty(x => x.ClientId);
                options.WithProperty(x => x.ClientSecret, isSecret: true);
            }
        )
        .AddDynamicExternalLoginProviderOptions<TwitterOptions>(
            TwitterDefaults.AuthenticationScheme,
            options =>
            {
                options.WithProperty(x => x.ConsumerKey);
                options.WithProperty(x => x.ConsumerSecret, isSecret: true);
            }
        );
    }
}
