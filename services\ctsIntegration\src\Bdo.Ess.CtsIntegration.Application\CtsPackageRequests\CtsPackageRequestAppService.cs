using Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests;
using Bdo.Ess.CtsIntegration.Constants;
using Bdo.Ess.CtsIntegration.CtsApi;
using Bdo.Ess.CtsIntegration.CtsPackageGeneration;
using Bdo.Ess.CtsIntegration.CtsPackageRequests;
using Bdo.Ess.CtsIntegration.Encryption;
using Bdo.Ess.CtsIntegration.Entities;
using Bdo.Ess.CtsIntegration.enums;
using Bdo.Ess.CtsIntegration.PackageGeneration;
using Bdo.Ess.CtsIntegration.SFTP;
using Bdo.Ess.CtsIntegration.StateMachine;
using Bdo.Ess.LookupService.Declaration;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Constants.CtsIntegration;
using Bdo.Ess.Shared.Hosting.Microservices.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Audit;
using Bdo.Ess.Shared.Utility.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Uow;
using Volo.Abp.Users;

namespace Bdo.Ess.CtsIntegration.Application.CtsPackageRequests
{
    public class CtsPackageRequestAppService : CtsIntegrationAppService, ICtsPackageRequestAppService
    {
        private readonly ICtsPackageRequestRepository _ctsPackageRequestRepository;
        private readonly IRepository<CtsPackageComment> _ctsPackageCommentRepository;
        private readonly ICtsEncryptionManager _ctsEncryptionManager;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly ICtsSftpUploader _ctsSftpUploader;
        private readonly ICtsApiClient _ctsApiClient;
        private readonly IAuditWebInfo _auditWebInfo;
        private readonly ICountryAppService _countryAppService;
        private readonly ILogger<CtsPackageRequestAppService> _logger;
        private readonly ICtsIntegrationBlobAppService _ctsIntegrationBlobAppService;
        private readonly IConfiguration _configuration;
        public CtsPackageRequestAppService(
              ICtsPackageRequestRepository ctsPackageRequestRepository
            , IRepository<CtsPackageComment> ctsPackageCommentRepository
            , ICtsEncryptionManager ctsEncryptionManager
            , IDistributedEventBus distributedEventBus
            , IUnitOfWorkManager unitOfWorkManager
            , ICtsSftpUploader ctsSftpUploader
            , ICtsApiClient ctsApiClient
            , IAuditWebInfo auditWebInfo
            , ICountryAppService countryAppService
            , ICtsIntegrationBlobAppService ctsIntegrationBlobAppService
            , IConfiguration configuration
            , ILogger<CtsPackageRequestAppService> logger)
        {
            _ctsPackageRequestRepository = ctsPackageRequestRepository;
            _ctsPackageCommentRepository = ctsPackageCommentRepository;
            _ctsEncryptionManager = ctsEncryptionManager;
            _distributedEventBus = distributedEventBus;
            _unitOfWorkManager = unitOfWorkManager;
            _ctsSftpUploader = ctsSftpUploader;
            _ctsApiClient = ctsApiClient;
            _auditWebInfo = auditWebInfo;
            _countryAppService = countryAppService;
            _ctsIntegrationBlobAppService = ctsIntegrationBlobAppService;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<PagedResultDto<CtsPackageRequestDto>> GetAllCtsPackageRequestAsync(GetCtsPackageRequestDto input)
        {
            var query = await _ctsPackageRequestRepository.GetQueryableAsync();
            query = query
                       .WhereIf(!string.IsNullOrWhiteSpace(input.FinancialEndYear), x => x.FiscalYear.ToString() == input.FinancialEndYear)
                       .WhereIf(!string.IsNullOrWhiteSpace(input.ReceivingCountry), x => x.ReceiverCountryCode.Contains(input.ReceivingCountry!))
                       .WhereIf(input.CtsUploadStatus.HasValue, x => x.UploadStatus == input.CtsUploadStatus)
                       .WhereIf(input.ExchangeReason.HasValue, x => x.ExchangeReason == input.ExchangeReason);

            var count = query.Count();

            var queryCtsPackageComment = await _ctsPackageCommentRepository.GetQueryableAsync();

            var dtoQuery = query.GroupJoin(queryCtsPackageComment,
                                            q => q.Id,
                                            c => c.CtsPackageId,
                                            (q, comments) => new { q, comments })
                                .Select(x => new CtsPackageRequestDto
                                {
                                    Id = x.q.Id,
                                    CtsPackageId = x.q.Id,
                                    ExchangeReason = x.q.ExchangeReason,
                                    DataPacket = x.q.CtsPackageFileName,
                                    FileCreationDate = x.q.FileCreatedAt,
                                    ReceivingCountry = x.q.ReceiverCountryCode,
                                    CtsUploadStatus = x.q.UploadStatus,
                                    CtsUploadStatusSortValue = x.q.UploadStatus == CTSUploadStatus.DataPacketGenerationFailed ? -1m :
                                                            x.q.UploadStatus == CTSUploadStatus.DoNotUpload ? -0.5m :
                                                            x.q.UploadStatus == CTSUploadStatus.GenerationInProgress ? 0m :
                                                            x.q.UploadStatus == CTSUploadStatus.NotStarted ? 0.5m :
                                                            x.q.UploadStatus == CTSUploadStatus.ReceivingCountryNotEnrolled ? 1m :
                                                            x.q.UploadStatus == CTSUploadStatus.Uploading ? 1.5m :
                                                            x.q.UploadStatus == CTSUploadStatus.Uploaded ? 2m :
                                                            x.q.UploadStatus == CTSUploadStatus.UploadFailed ? 2.5m : (decimal)(x.q.UploadStatus ?? 0),
                                    UploadedAt = x.q.UploadedAt,
                                    CtsTransmissionStatus = x.q.TransmissionStatus,
                                    ExcludeFromCtsUpload = x.q.IsExcludeCtsUpload,
                                    ViewExchangeRecords = x.q.EssInformationXmlId.HasValue ? true : false,
                                    ViewComments = x.comments
                                                    .Select(comment => new CtsPackageCommentDto
                                                    {
                                                        Id = comment.Id,
                                                        CtsPackageId = comment.CtsPackageId,
                                                        Comment = comment.Comment,
                                                        CreatorName = comment.CreatorName,
                                                        CreationTime = comment.CreationTime,
                                                        CreatorId = comment.CreatorId,
                                                        CtsUploadStatus = comment.CtsUploadStatus,
                                                        TransmissionStatus = comment.TransmissionStatus
                                                    }).ToList()
                                });

            // Sorting
            string sortingStr = input.Sorting?.Trim() ?? "ExchangeReason asc";
            bool isDescending = sortingStr.EndsWith(" desc", StringComparison.InvariantCultureIgnoreCase);
            string sortField = sortingStr.Split(' ')[0];

            // Use a dictionary for sort mapping
            var sortMap = new Dictionary<string, Func<IQueryable<CtsPackageRequestDto>, IOrderedQueryable<CtsPackageRequestDto>>>
            {
                ["exchangereason"] = q => isDescending ? q.OrderByDescending(x => x.ExchangeReason) : q.OrderBy(x => x.ExchangeReason),
                ["datapacket"] = q => isDescending ? q.OrderByDescending(x => x.DataPacket) : q.OrderBy(x => x.DataPacket),
                ["filecreationdate"] = q => isDescending ? q.OrderByDescending(x => x.FileCreationDate) : q.OrderBy(x => x.FileCreationDate),
                ["receivingcountry"] = q => isDescending ? q.OrderByDescending(x => x.ReceivingCountry) : q.OrderBy(x => x.ReceivingCountry),
                ["uploadedat"] = q => isDescending ? q.OrderByDescending(x => x.UploadedAt) : q.OrderBy(x => x.UploadedAt),
                ["ctsuploadstatus"] = q => isDescending ? q.OrderByDescending(x => x.CtsUploadStatusSortValue) : q.OrderBy(x => x.CtsUploadStatusSortValue),
                ["ctstransmissionstatus"] = q => isDescending ? q.OrderByDescending(x => x.CtsTransmissionStatus) : q.OrderBy(x => x.CtsTransmissionStatus)
            };

            if (sortMap.TryGetValue(sortField.ToLower(), out var orderFunc))
            {
                dtoQuery = orderFunc(dtoQuery);
            }
            else
            {
                dtoQuery = dtoQuery.OrderBy(x => x.ExchangeReason);
            }

            var result = dtoQuery
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToList();
            foreach (var comment in from request in result
                                    where request.ViewComments != null && request.ViewComments.Count > 0
                                    from comment in request.ViewComments ?? []
                                    select comment)
            {
                if (comment.CtsUploadStatus.HasValue)
                {
                    comment.CtsUploadStatusDesc = comment.CtsUploadStatus.GetEnumDescription();
                }
                if (!string.IsNullOrWhiteSpace(comment.TransmissionStatus) && Enum.TryParse<CtsTransmissionStatus>(comment.TransmissionStatus, out var status))
                {
                    comment.TransmissionStatus = $"{comment.TransmissionStatus} - {status.GetEnumDescription()}";
                }
            }
            foreach (var request in result)
            {
                request.AllowedActions = DataPacketStateMachine.GetAvailableActions(request.CtsUploadStatus, request.CtsTransmissionStatus, request.UploadedAt);

                if (!string.IsNullOrWhiteSpace(request.CtsTransmissionStatus) && Enum.TryParse<CtsTransmissionStatus>(request.CtsTransmissionStatus, out var status))
                {
                    request.CtsTransmissionStatus = $"{request.CtsTransmissionStatus} - {status.GetEnumDescription()}";
                }
            }
            return new PagedResultDto<CtsPackageRequestDto>(count, result);
        }

        public async Task<CtsPackageRequestDataDto> InitPackageGenerationRequestAsync(InformationExchangeXmlCreatedEto eto)
        {
            var entityDto = new CreateCtsPackageRequestDto()
            {
                Id = eto.CtsPackageRequestId,
                EssInformationXmlId = eto.EssInfoXmlId,
                ExchangeReason = eto.ExchangeReason,
                ReceiverCountryCode = eto.ReceivingCountryCode,
                FiscalYear = eto.FiscalYear,
                HasExchangeRecords = eto.HasExchangeRecords,
                UploadStatus = CTSUploadStatus.GenerationInProgress,
                XmlPayloadUrl = eto.XmlFileLocationUrl ?? "",
                TenantId = eto.TenantId,
                MessageRefId = eto.MessageRefId,
            };
            entityDto.MetaCountryCode = CalcuateCountryCodeInMeta(entityDto.ReceiverCountryCode);
            //Leave the Original XML payload in Azure storage
            return await CreateAsync(entityDto);
        }
        private string CalcuateCountryCodeInMeta(string receivingCountryCode)
        {
            var useTestCountry = _configuration.GetValue<string>("Cts:UseTestCountry", "");
            var countriesUseHub = _configuration.GetValue<string>("Cts:Api:CountriesUseHub", "")?
                .Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).Select(c => c.Trim().ToUpper()).ToList();

            var countryCodeInMeta = receivingCountryCode;
            if (!string.IsNullOrWhiteSpace(useTestCountry))
            {
                countryCodeInMeta = useTestCountry;
            }
            else if ((countriesUseHub ?? []).Contains(receivingCountryCode))
            {
                countryCodeInMeta = $"{receivingCountryCode}.00";
            }
            return countryCodeInMeta;
        }
        public async Task<CtsPackageRequestDataDto> CreateAsync(CreateCtsPackageRequestDto input)
        {
            var entity = new CtsPackageRequest(input.Id);
            ObjectMapper.Map<CreateCtsPackageRequestDto, Entities.CtsPackageRequest>(input, entity);
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
            _ctsEncryptionManager.EncryptCtsPackageRequest(entity);
            var created = await _ctsPackageRequestRepository.InsertAsync(entity, autoSave: true);
            await uow.SaveChangesAsync();
            await uow.CompleteAsync();
            return ObjectMapper.Map<Entities.CtsPackageRequest, CtsPackageRequestDataDto>(created);
        }

        public async Task<CtsPackageRequestDataDto> UpdateAysnc(CtsPackageRequestDataDto input)
        {
            var entity = await _ctsPackageRequestRepository.FirstOrDefaultAsync(x => x.Id == input.Id);
            Check.NotNull(entity, $"CtsPackageRequest with Id {input.Id} not found.");
            ObjectMapper.Map<CtsPackageRequestDataDto, CtsPackageRequest>(input, entity!);
            _ctsEncryptionManager.EncryptCtsPackageRequest(entity!);
            entity = await _ctsPackageRequestRepository.UpdateAsync(entity!, autoSave: true);
            var cloned = _ctsEncryptionManager.DecryptCtsPackageRequest(entity, true);
            return ObjectMapper.Map<CtsPackageRequest, CtsPackageRequestDataDto>(cloned);
        }

        public async Task<CtsPackageRequestDataDto> GetAsync(Guid id)
        {
            var entity = await _ctsPackageRequestRepository.GetAsync(id);
            entity = _ctsEncryptionManager.DecryptCtsPackageRequest(entity, true);
            return ObjectMapper.Map<Entities.CtsPackageRequest, CtsPackageRequestDataDto>(entity);
        }

        public async Task<List<CtsPackageRequestSummaryDto>> GetSummaryByYear(string year)
        {
            var query = await _ctsPackageRequestRepository.GetQueryableAsync();
            var summary = query.Where(x => x.FiscalYear.ToString() == year).GroupBy(x => x.FiscalYear)
                                .Select(g => new CtsPackageRequestSummaryDto
                                {
                                    TotalNotUploaded = g.Count(x => x.UploadStatus == CTSUploadStatus.DoNotUpload),
                                    TotalReadyForUpload = g.Count(x => x.UploadStatus == CTSUploadStatus.NotStarted),
                                    TotalFailedUpload = g.Count(x => x.UploadStatus == CTSUploadStatus.UploadFailed),
                                    TotalUploadedToCTS = g.Count(x => x.UploadStatus == CTSUploadStatus.Uploaded),
                                    TotalNotEnrolled = g.Count(x => x.UploadStatus == CTSUploadStatus.ReceivingCountryNotEnrolled),
                                    Year = year
                                }).FirstOrDefault();

            return new List<CtsPackageRequestSummaryDto> { summary ?? new CtsPackageRequestSummaryDto { Year = year } };
        }

        public async Task<CtsActionResultDto> MarkAsDoNotUploadAsync(Guid packageRequestId)
        {
            var packageRequest = await _ctsPackageRequestRepository.GetAsync(packageRequestId);
            var stateInfo = DataPacketStateMachine.PerformActionAsync(
                packageRequest,
                DataPacketAction.MarkDoNotUpload
            );

            if (stateInfo.IsSuccess)
            {
                await _ctsPackageRequestRepository.UpdateAsync(packageRequest, autoSave: true);
            }
            return MapToResultDto(stateInfo, "Successfully exclude packet from uploading");
        }

        public async Task<CtsActionResultDto> UnMarkAsDoNotUploadAsync(Guid packageRequestId)
        {
            var packageRequest = await _ctsPackageRequestRepository.GetAsync(packageRequestId);
            var stateInfo = DataPacketStateMachine.PerformActionAsync(
                packageRequest,
                DataPacketAction.UnmarkDoNotUpload
            );

            if (stateInfo.IsSuccess)
            {
                await _ctsPackageRequestRepository.UpdateAsync(packageRequest, autoSave: true);
            }
            return MapToResultDto(stateInfo, "Successfully include packet for uploading");
        }

        //This is called by Jobs
        public async Task<StateTransitionResult> UpdateUploadResultAsync(Guid packageRequestId, bool uploadSucess, string errMsg = "")
        {
            var packageRequest = await _ctsPackageRequestRepository.GetAsync(packageRequestId);
            packageRequest.ProcessInfo = errMsg;
            var stateInfo = DataPacketStateMachine.TriggerEvent(packageRequest, uploadSucess ? DataPacketEvent.UploadCompleted : DataPacketEvent.UploadFailed);
            if (stateInfo.IsSuccess)
            {
                await _ctsPackageRequestRepository.UpdateAsync(packageRequest, autoSave: true);
            }
            return stateInfo;
        }

        //This is called by Jobs
        public async Task<List<StateTransitionResult>> UploadStartedAsync(List<Guid> packageRequestIds)
        {
            var packageRequests = await _ctsPackageRequestRepository.GetListAsync(x => packageRequestIds.Contains(x.Id));
            if (packageRequests.Count == 0)
            {
                throw new UserFriendlyException("No package requests found for the provided IDs.");
            }
            var requestToUpdate = new List<CtsPackageRequest>();
            var stateInfos = new List<StateTransitionResult>();
            foreach (var packageRequest in packageRequests)
            {
                var stateInfo = DataPacketStateMachine.TriggerEvent(packageRequest, DataPacketEvent.StartUpload);
                if (stateInfo.IsSuccess)
                {
                    requestToUpdate.Add(packageRequest);
                }
                stateInfos.Add(stateInfo);
            }
            if (requestToUpdate.Count > 0)
            {
                await _ctsPackageRequestRepository.UpdateManyAsync(requestToUpdate, autoSave: true);
            }
            return stateInfos;
        }

        public async Task<CtsActionResultDto> RegeneratePackageAsync(Guid packageRequestId, string comments)
        {
            var packageRequest = await _ctsPackageRequestRepository.GetAsync(packageRequestId);
            var originUploadStatus = packageRequest.UploadStatus;
            var originTransmissionStatus = packageRequest.TransmissionStatus;
            var stateInfo = DataPacketStateMachine.PerformActionAsync(packageRequest, DataPacketAction.Regenerate);
            if (!stateInfo.IsSuccess)
            {
                return MapToResultDto(stateInfo, "");
            }
            packageRequest.MetaCountryCode = CalcuateCountryCodeInMeta(packageRequest.ReceiverCountryCode);
            await _ctsPackageRequestRepository.UpdateAsync(packageRequest, autoSave: true);

            var comment = new CtsPackageComment
            {
                CtsPackageId = packageRequest.Id,
                Comment = comments,
                CreatorName = $"{CurrentUser.Name} {CurrentUser.SurName}",
                CtsUploadStatus = originUploadStatus,
                TransmissionStatus = originTransmissionStatus
            };
            await _ctsPackageCommentRepository.InsertAsync(comment, autoSave: true);

            await _distributedEventBus.PublishAsync(new RegenerateCtsDataPacketEto
            {
                TenantId = packageRequest.TenantId,
                UserId = CurrentUser.GetId(),
                PackageRequestId = packageRequest.Id,
            });

            await AddAuditLogForRegeneratePackageAsync(packageRequest);

            return MapToResultDto(stateInfo, "Successfully triggered packet regeneration");
        }

        public async Task<CtsActionResultDto> UploadToCtsAsync(Guid packageRequestId)
        {
            var packageRequest = await _ctsPackageRequestRepository.GetAsync(packageRequestId);

            // Check if the action is allowed using the state machine
            if (!DataPacketStateMachine.CanPerformAction(packageRequest, DataPacketAction.Upload))
            {
                return new CtsActionResultDto
                {
                    Success = false,
                    Message = "Upload action is not allowed for the current package state.",
                    Errors = new List<string> { $"Package is in {packageRequest.UploadStatus} status. Upload action is not allowed." }
                };
            }

            try
            {
                // Update the state to uploading using the state machine
                var stateInfo = DataPacketStateMachine.PerformActionAsync(packageRequest, DataPacketAction.Upload);
                if (!stateInfo.IsSuccess)
                {
                    return MapToResultDto(stateInfo, "Failed to update package state for upload");
                }

                // Save the state change first
                await _ctsPackageRequestRepository.UpdateAsync(packageRequest, autoSave: true);

                // Perform the actual upload
                var uploadResult = await _ctsSftpUploader.UploadAsync(packageRequest.CtsPackageFileName ?? "", packageRequest.PackageZipUrl ?? "", packageRequest.MetaCountryCode ?? "");

                // Update the upload result
                if (string.IsNullOrEmpty(uploadResult))
                {
                    // Upload successful
                    await UpdateUploadResultAsync(packageRequestId, true);
                    await AddAuditLogForUploadSelectedDataPacketAsync(packageRequest);
                    return new CtsActionResultDto
                    {
                        Success = true,
                        Message = "Package uploaded to CTS successfully.",
                        Errors = new List<string>()
                    };
                }
                else
                {
                    // Upload failed
                    await UpdateUploadResultAsync(packageRequestId, false, uploadResult);
                    await AddAuditLogForUploadSelectedDataPacketAsync(packageRequest);
                    return new CtsActionResultDto
                    {
                        Success = false,
                        Message = "Failed to upload package to CTS.",
                        Errors = new List<string> { uploadResult }
                    };
                }
            }
            catch (Exception ex)
            {
                // Update to failed state
                await UpdateUploadResultAsync(packageRequestId, false, ex.Message);
                await AddAuditLogForUploadSelectedDataPacketAsync(packageRequest);
                return new CtsActionResultDto
                {
                    Success = false,
                    Message = "An error occurred during upload to CTS.",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<CtsActionResultDto> BatchUploadToCtsAsync(int fiscalYear)
        {
            // Get all packages eligible for upload for the given fiscal year
            var query = await _ctsPackageRequestRepository.GetQueryableAsync();
            var eligiblePackages = await _ctsPackageRequestRepository.GetListAsync(x =>
                x.FiscalYear == fiscalYear &&
                x.UploadStatus == CTSUploadStatus.NotStarted &&
                x.IsExcludeCtsUpload != true);

            if (eligiblePackages.Count == 0)
            {
                return new CtsActionResultDto
                {
                    Success = false,
                    Message = "No eligible packages found for upload in the specified fiscal year.",
                    Errors = new List<string> { "No eligible packages found for upload in the specified fiscal year." }
                };
            }

            var uploadResults = new List<string>();
            var successCount = 0;
            var failureCount = 0;

            foreach (var package in eligiblePackages)
            {
                // Double-check that upload action is allowed using state machine
                if (!DataPacketStateMachine.CanPerformAction(package, DataPacketAction.Upload))
                {
                    uploadResults.Add($"Package {package.CtsPackageFileName}: Upload not allowed in current state {package.UploadStatus}");
                    failureCount++;
                    continue;
                }

                try
                {
                    var uploadResult = await UploadToCtsAsync(package.Id);
                    if (uploadResult.Success)
                    {
                        uploadResults.Add($"Package {package.CtsPackageFileName}: Upload successful");
                        successCount++;
                    }
                    else
                    {
                        uploadResults.Add($"Package {package.CtsPackageFileName}: Upload failed - {string.Join(", ", uploadResult.Errors)}");
                        failureCount++;
                    }
                }
                catch (Exception ex)
                {
                    uploadResults.Add($"Package {package.CtsPackageFileName}: Upload failed with exception - {ex.Message}");
                    failureCount++;
                }
            }

            await AddAuditLogForCtsUploadAsync(fiscalYear);

            return new CtsActionResultDto
            {
                Success = successCount > 0,
                Message = $"Batch upload completed. {successCount} successful, {failureCount} failed.",
                Errors = uploadResults
            };
        }

        private static CtsActionResultDto MapToResultDto(StateTransitionResult stateInfo, string successMessage)
        {
            return new CtsActionResultDto
            {
                Success = stateInfo.IsSuccess,
                Message = stateInfo.IsSuccess ? successMessage : "",
                Errors = new List<string>
                {
                    stateInfo.IsSuccess ? "" : stateInfo.Message ?? "An error occurred during the operation."
                },
                AvailableActions = stateInfo.AvailableActions
            };
        }

        public async Task<CtsActionResultDto> CheckTransmissionStatusAsync(Guid packageRequestId)
        {
            try
            {
                var packageRequest = await _ctsPackageRequestRepository.GetAsync(packageRequestId);
                var shouldCheck = DataPacketStateMachine.ShouldRefreshTransmissionStatus(packageRequest);
                if (!shouldCheck)
                {
                    throw new UserFriendlyException("Transmission status check is not applicable for the current state of the package request.");
                }
                var response = await _ctsApiClient.GetTransmissionStatusAsync(
                    filename: packageRequest.CtsPackageFileName!,
                    senderFileId: GetSenderFileId(packageRequest)
                );

                var stateInfo = UpdatePackageRequestStatusByResponse(packageRequest, response);
                await _ctsPackageRequestRepository.UpdateAsync(packageRequest, autoSave: true);
                return MapToResultDto(stateInfo, "Successfully get latest trasmission status");
            }
            catch (BusinessException ex)
            {
                return new CtsActionResultDto
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = [ex.Message]
                };
            }
        }

        public async Task<CtsActionResultDto> RefreshTransmissionStatusAsync()
        {
            var actionResult = new CtsActionResultDto();
            try
            {
                var thresholdDate = Clock.Now.AddDays(-7);

                var all = await _ctsPackageRequestRepository.GetPendingForStatusRefreshAsync(thresholdDate);
                /* Eligible flag is decided by Transmission is final
                var pending = all
                    .Where(x => !DataPacketStateMachine.IsTransmissionFinal(x))
                    .ToList();
                */
                foreach (var req in all)
                {
                    try
                    {
                        var statusResponse = await _ctsApiClient.GetTransmissionStatusAsync(
                            filename: req.CtsPackageFileName!,
                            senderFileId: GetSenderFileId(req)
                        );

                        if (statusResponse != null && !string.IsNullOrWhiteSpace(statusResponse.StatusCd))
                        {
                            UpdatePackageRequestStatusByResponse(req, statusResponse);
                            await _ctsPackageRequestRepository.UpdateAsync(req, true);
                        }
                    }
                    catch (Exception ex)
                    {
                        req.ProcessInfo = $"Status check failed: {ex.Message}";
                        actionResult.Errors.Add($"Failed to refresh transmission status for package {req.CtsPackageFileName}");
                    }
                }

                if (actionResult.Errors.Count > 0)
                {
                    actionResult.Success = false;
                    actionResult.Message = "Some transmission statuses could not be refreshed.";
                }
                else
                {
                    actionResult.Success = true;
                    actionResult.Message = "All transmission statuses refreshed successfully.";
                }
            }
            //If Fail to get access token, Business Exception will be thrown
            catch (BusinessException ex)
            {
                actionResult.Success = false;
                actionResult.Message = ex.Message;
                actionResult.Errors = new List<string> { ex.Message };
            }
            return actionResult;
        }

        public async Task<string> DownloadDataPacketFileAsync(Guid requestId)
        {
            var request = await _ctsPackageRequestRepository.FirstOrDefaultAsync(x => x.Id == requestId);
            if (request == null)
            {
                throw new UserFriendlyException($"CtsPackageRequest with Id {requestId} not found.");
            }
            if (string.IsNullOrEmpty(request.PackageZipUrl))
            {
                throw new UserFriendlyException("Data Packet File is not available for the specified package request.");
            }
            return await _ctsIntegrationBlobAppService.DownloadFileBase64(request.PackageZipUrl);
        }

        private StateTransitionResult UpdatePackageRequestStatusByResponse(CtsPackageRequest req, CtsTransmissionStatusResponse statusResponse)
        {
            req.TransmissionStatus = statusResponse.StatusCd;
            req.CtsTransmissionId = statusResponse.CTSTransmissionId;
            req.TransmissionStatusLastCheckedUtc = Clock.Now;
            req.StatusUpdatedAt = req.TransmissionStatusLastCheckedUtc;

            var enumStatus = statusResponse.StatusCd.ToEnumValue<CtsTransmissionStatus>();

            var statusDesc = enumStatus?.GetEnumDescription();
            req.TransmissionStatusDesc = statusDesc;

            return DataPacketStateMachine.TriggerEvent(req, DataPacketEvent.TransmissionStatusReceived);
        }

        private static string GetSenderFileId(CtsPackageRequest request)
        {
            return $"{CtsConstants.CtsBahamsSenderCode}_{request.MetaCountryCode}_{CtsConstants.CtsMessageType}_{request.MessageRefId}";
        }


        private async Task AddAuditLogForRegeneratePackageAsync(CtsPackageRequest ctsPackageRequest)
        {
            try
            {
                var country = await _countryAppService.GetAllCountriesOrdered();
                var countryName = country.SingleOrDefault(c => c.Code2.Equals(ctsPackageRequest.ReceiverCountryCode, StringComparison.OrdinalIgnoreCase))?.Name;

                var eto = new AuditRegenerateDataPacketEto
                {
                    UserName = CurrentUser.UserName,
                    UserId = CurrentUser.Id,
                    IPAddress = _auditWebInfo.IPAddress,
                    ESPeriodEnd = ctsPackageRequest.FiscalYear,
                    Action = AuditActionEnum.RegenerateDataPacket,
                    TenantId = CurrentTenant.Id,
                    AuditDateTime = DateTime.UtcNow,
                    NewValue = new AuditRegenerateDataPacketDto
                    {
                        CtsPackageRequestId = ctsPackageRequest.Id.ToString(),
                        InformationExchangeReason = ctsPackageRequest.ExchangeReason?.GetEnumDescription() ?? "NA",
                        Country = countryName ?? ctsPackageRequest.ReceiverCountryCode,
                        Year = ctsPackageRequest.FiscalYear.ToString(),
                        UploadStatus = ctsPackageRequest.UploadStatus?.GetEnumDescription() ?? "NA"
                    }
                };

                if (!string.IsNullOrWhiteSpace(eto.NewValue.TransmissionStatus) && Enum.TryParse<CtsTransmissionStatus>(eto.NewValue.TransmissionStatus, out var status))
                {
                    eto.NewValue.TransmissionStatus = status.GetEnumDescription();
                }
                else
                {
                    eto.NewValue.TransmissionStatus = "NA";
                }

                await _distributedEventBus.PublishAsync(eto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to add audit log for regenerate package request with ID {PackageRequestId}", ctsPackageRequest.Id);
            }
        }

        private async Task AddAuditLogForCtsUploadAsync(int fiscalYear)
        {
            try
            {
                var eto = new AuditCtsUploadEto
                {
                    UserName = CurrentUser.UserName,
                    UserId = CurrentUser.Id,
                    IPAddress = _auditWebInfo.IPAddress,
                    Action = AuditActionEnum.CtsUpload,
                    TenantId = CurrentTenant.Id,
                    AuditDateTime = DateTime.UtcNow,
                    ESPeriodEnd = fiscalYear,
                    NewValue = new AuditCtsUploadDto
                    {
                        Year = fiscalYear.ToString()
                    }
                };

                await _distributedEventBus.PublishAsync(eto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to add audit log for CTS upload for fiscal year {FiscalYear}", fiscalYear);

            }
        }

        private async Task AddAuditLogForUploadSelectedDataPacketAsync(CtsPackageRequest ctsPackageRequest)
        {
            try
            {
                var country = await _countryAppService.GetAllCountriesOrdered();
                var countryName = country.SingleOrDefault(c => c.Code2.Equals(ctsPackageRequest.ReceiverCountryCode, StringComparison.OrdinalIgnoreCase))?.Name;

                var eto = new AuditUploadSelectedDataPacketEto
                {
                    UserName = CurrentUser.UserName,
                    UserId = CurrentUser.Id,
                    IPAddress = _auditWebInfo.IPAddress,
                    Action = AuditActionEnum.UploadSelectedDataPacket,
                    TenantId = CurrentTenant.Id,
                    AuditDateTime = DateTime.UtcNow,
                    ESPeriodEnd = ctsPackageRequest.FiscalYear,
                    NewValue = new AuditUploadSelectedDataPacketDto
                    {
                        CtsPackageRequestId = ctsPackageRequest.Id.ToString(),
                        InformationExchangeReason = ctsPackageRequest.ExchangeReason?.GetEnumDescription() ?? "NA",
                        Country = countryName ?? ctsPackageRequest.ReceiverCountryCode,
                        Year = ctsPackageRequest.FiscalYear.ToString(),
                    }
                };

                await _distributedEventBus.PublishAsync(eto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to add audit log for upload selected data packet with ID {PackageRequestId}", ctsPackageRequest.Id);
            }
        }

    }
}