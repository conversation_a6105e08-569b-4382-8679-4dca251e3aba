﻿using Bdo.Ess.EconomicSubstanceService.InformationExchanges.Dtos;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.InformationExchange;
using Bdo.Ess.Shared.HttpApi.Audit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Volo.Abp;
using static Bdo.Ess.EconomicSubstanceService.Permissions.ESServicePermissions;

namespace Bdo.Ess.EconomicSubstanceService.InformationExchanges
{
    [RemoteService(Name = EconomicSubstanceServiceRemoteServiceConsts.RemoteServiceName)]
    [Area("EconomicSubstanceService")]
    [Route("api/ESService/InformationDetails")]
    [Authorize]
    public class InformationExchangeDetailsController: EconomicSubstanceServiceController, IInformationExchangesDetailsAppService
    {

        private readonly IInformationExchangesDetailsAppService _informationExchangesDetails;

        /// <summary>
        ///  Note: _auditWebInfo is scoped dependency instance, 
        ///  so, it will be shared between HttpApi and AppService
        ///  Work for Auditing purpose to get client IP address.
        /// </summary>
        private readonly IAuditWebInfo _auditWebInfo;
        protected readonly AuditWebInfoService _auditWebInfoService;

        public InformationExchangeDetailsController(IInformationExchangesDetailsAppService informationExchangesDetails, IAuditWebInfo auditWebInfo, AuditWebInfoService auditWebInfoService)
        {
            _informationExchangesDetails = informationExchangesDetails;
            _auditWebInfo = auditWebInfo;
            _auditWebInfoService = auditWebInfoService;
            _auditWebInfo.IPAddress = _auditWebInfoService.GetClientIpAddress();
            _auditWebInfo.AuditUserId = _auditWebInfoService.GetAuditUserId();
        }

        [HttpGet]
        [Route("GetInformationExchangeDetails")]
        public async Task<InformationExchangeDetailDto> GetInformationExchangeDetails(Guid id)
        {
            return await _informationExchangesDetails.GetInformationExchangeDetails(id);
        }

        [HttpPost]
        [Route("AddInformationExchangeDetails")]
        [Authorize(InformationExchangeDetail.Create)]
        public async Task<Guid> AddInformationExchangeDetails(InformationExchangeDetailDto informationExchangeDetails)
        {
            return await _informationExchangesDetails.AddInformationExchangeDetails(informationExchangeDetails);
        }

        [HttpPost]
        [Route("UpdateInformationExchangeDetails")]
        [Authorize(InformationExchangeDetail.Edit)]
        public async Task<bool> UpdateInformationExchangeDetails(InformationExchangeDetailDto informationExchangeDetails)
        {
            return await _informationExchangesDetails.UpdateInformationExchangeDetails(informationExchangeDetails);
        }

        [HttpPost]
        [Route("UpdateInformationExchangeDetailStatus")]
        [Authorize(InformationExchangeDetail.Edit)]
        public async Task UpdateInformationExchangeDetailStatus(Guid id, InformationExchangeStatus status)
        {
            await _informationExchangesDetails.UpdateInformationExchangeDetailStatus(id, status);
        }

        [HttpPost]
        [Route("MapDetailsToXml")]
        public async Task<InformationExchangeDetailsListEto> MapDetailsToXml(List<Guid> exchangesIds, Guid userId)
        {
            return await _informationExchangesDetails.MapDetailsToXml(exchangesIds, userId);
        }

        [HttpPost]
        [Route("RetriggerXMLGenerationFlow")]
        public async Task ReTriggerXMLGenerationFlowAsync(Guid tenantId, Guid userId, Guid essXmlId)
        {
            await _informationExchangesDetails.ReTriggerXMLGenerationFlowAsync(tenantId, userId, essXmlId);
        }
        

        [HttpGet]
        [Route("StandardMonitoringFromYear")]
        public Task<string> StandardMonitoringFromYear()
        {
            return  _informationExchangesDetails.StandardMonitoringFromYear();
        }

        [HttpGet]
        [Route("GetEssInformationXml")]
        public Task<EssInformationExchangeXMLDto> GetEssInformationXml(Guid essInfoXmlId)
        {
            return _informationExchangesDetails.GetEssInformationXml(essInfoXmlId);
        }
    }
}
