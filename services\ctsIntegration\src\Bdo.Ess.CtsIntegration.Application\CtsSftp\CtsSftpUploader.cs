﻿using Azure.Storage.Blobs;
using Bdo.Ess.CtsIntegration.BahamasCtsSettings;
using Bdo.Ess.CtsIntegration.CtsPackageGeneration;
using Bdo.Ess.Shared.Hosting.Microservices.Application;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Renci.SshNet;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bdo.Ess.CtsIntegration.SFTP
{
    public class CtsSftpUploader : CtsIntegrationAppService, ICtsSftpUploader
    {
        private readonly IConfiguration _config;
        private readonly IBahamasCtsSettingAppService _ctsSettingAppService;
        private readonly ICtsIntegrationBlobAppService _blobAppService;

        public CtsSftpUploader(
            IConfiguration config,
            IBahamasCtsSettingAppService ctsSettingAppService,
            ICtsIntegrationBlobAppService blobAppService)
        {
            _config = config;
            _ctsSettingAppService = ctsSettingAppService;
            _blobAppService = blobAppService;
        }

        public async Task<string> UploadAsync(string zipFileName, string zipFileUrl, string receivingCountryCode)
        {
            try
            {
                var errorMsg = "";
                var ctsSettings = await _ctsSettingAppService.GetCurrentSettingsAsync().ConfigureAwait(false);
                
                // Configuration extraction
                var host = _config["Cts:SFTP:HOST"] ?? throw new ArgumentException("CTSIntegration: SFTP:HOST config missing.");
                var port = int.TryParse(_config["Cts:SFTP:PORT"], out var parsedPort) ? parsedPort : 22;
                var username = ctsSettings!.SftpUserName;
                var remoteDir = "/Outbox/" + receivingCountryCode.ToUpperInvariant() + "/";
                var privateKeyBase64 = ctsSettings.SftpSSHKey;

                // Retry configuration from appsettings
                var maxRetryAttempts = _config.GetValue<int>("Cts:SFTP:MaxRetryAttempts", 3);
                var retryDelayMs = _config.GetValue<int>("Cts:SFTP:RetryDelayMs", 1000);

                var fileBytes = await _blobAppService.DownloadFileBytes(zipFileUrl).ConfigureAwait(false);

                // Retry logic for SFTP upload
                for (int attempt = 1; attempt <= maxRetryAttempts; attempt++)
                {
                    try
                    {
                        Logger.LogInformation("SFTP upload attempt {Attempt} of {MaxRetries} for file {FileName}", 
                            attempt, maxRetryAttempts, zipFileName);

                        using var fileStream = new MemoryStream(fileBytes, writable: false);
                        using var privateKeyStream = new MemoryStream(Convert.FromBase64String(privateKeyBase64));
                        using var sftp = new SftpClient(host, port, username, new PrivateKeyFile(privateKeyStream));

                        sftp.Connect();
                        await sftp.ChangeDirectoryAsync(remoteDir);
                        
                        // Allow to overwrite previous uploaded file
                        sftp.UploadFile(fileStream, zipFileName);

                        var fileExists = sftp.ListDirectory(remoteDir)
                                             .Any(f => f.Name.Equals(zipFileName, StringComparison.OrdinalIgnoreCase));

                        sftp.Disconnect();
                        
                        if (fileExists)
                        {
                            Logger.LogInformation("Upload verified on attempt {Attempt}: {FileName} exists on SFTP.", 
                                attempt, zipFileName);
                            return "";
                        }
                        else
                        {
                            Logger.LogWarning("Upload verification failed on attempt {Attempt}: {FileName} not found on SFTP.", 
                                attempt, zipFileName);
                            
                            if (attempt == maxRetryAttempts)
                            {
                                return "Upload failed verification after all retry attempts: not found on SFTP.";
                            }
                        }
                    }
                    catch (Exception ex) when (attempt < maxRetryAttempts)
                    {
                        Logger.LogWarning(ex, "SFTP upload attempt {Attempt} failed for {FileName}. Will retry in {Delay}ms.", 
                            attempt, zipFileName, retryDelayMs);
                        errorMsg = ex.Message;
                        // Wait before retrying (except on the last attempt)
                        await Task.Delay(retryDelayMs).ConfigureAwait(false);
                    }
                }

                // This point is reached only if all retries failed
                Logger.LogError("All {MaxRetries} upload attempts failed for {FileName}", maxRetryAttempts, zipFileName);
                return $"Upload to SFTP failed after {maxRetryAttempts} attempts with error {errorMsg}";
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Upload to SFTP failed for {FileName} due to configuration or setup error", zipFileName);
                return $"Upload to SFTP failed: {ex.Message}";
            }
        }
    }
}