﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp;

namespace Bdo.Ess.CtsIntegration.Certificate
{
	[RemoteService(Name = CtsIntegrationRemoteServiceConsts.RemoteServiceName)]
	[Area(CtsIntegrationRemoteServiceConsts.RemoteServiceName)]
	[Route($"api/{CtsIntegrationRemoteServiceConsts.RemoteServiceName}/country-certificates")]
	//[Authorize]
	public class CountryCertificateController : CtsIntegrationController //, ICountryCertificateAppService
	{
		private readonly ICountryCertificateAppService _appService;

		public CountryCertificateController(ICountryCertificateAppService appService)
		{
			_appService = appService;
		}

			
		/* Disable maintainance API
		[HttpPost]
        [ApiExplorerSettings(IgnoreApi = true)]
		[AllowAnonymous]
        public async Task<CountryCertificateDto> InsertOrUpdateAsync(IFormFile pubCertFile, string countryCode, string opsKey)
		{
            return await _appService.InsertOrUpdateAsync(pubCertFile, countryCode, opsKey);
        }
		*/
        

		/// <summary>
		/// Refresh all country certificates from CTS API
		/// </summary>
		[HttpPost("refresh")]
		public async Task RefreshCertificates()
			=> await _appService.RefreshCertificates();


		

        
    }

}
