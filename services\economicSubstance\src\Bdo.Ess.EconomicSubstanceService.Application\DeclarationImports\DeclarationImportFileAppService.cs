﻿using Bdo.Ess.EconomicSubstanceService.DeclarationImports.Dtos;
using Bdo.Ess.EconomicSubstanceService.DeclarationImports.Exports;
using Bdo.Ess.EconomicSubstanceService.DeclarationImports.Validators;
using Bdo.Ess.ESFakeDataGenerator;
using Bdo.Ess.Shared.Hosting.Exceptions;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.DeclarationImports;
using Bdo.Ess.Shared.Utility.Extensions;
using FluentValidation.Results;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Data;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Auditing;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Timing;
using Volo.Abp.Uow;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Audit;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Audit;
using Volo.Abp;

namespace Bdo.Ess.EconomicSubstanceService.DeclarationImports
{
    public class DeclarationImportFileAppService : EconomicSubstanceServiceAppService, IDeclarationImportFileAppService
    {
        private readonly IDeclarationImportFileRepository _declarationImportFileRepository;
        private readonly IDeclarationImportValidationAppService _validationAppService;
        private readonly IClock _clock;
        private readonly IDeclarationBlobAppService _declarationBlobAppService;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IDeclarationImportCacheAppService _imortCacheAppService;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly IAuditingManager _auditingManager;

        /// <summary>
        ///  Note: _auditWebInfo is scoped dependency instance, 
        ///  so, it will be shared between HttpApi and AppService
        ///  Work for Auditing purpose to get client IP address.
        /// </summary>
        private readonly IAuditWebInfo _auditWebInfo;


        public DeclarationImportFileAppService(
            IDeclarationImportFileRepository declarationImportFileRepository,
            IDeclarationImportValidationAppService validationAppService,
            IDeclarationBlobAppService declarationBlobAppService,
            IDistributedEventBus distributedEventBus,
            IDeclarationImportCacheAppService imortCacheAppService,
            IUnitOfWorkManager unitOfWorkManager,
            IAuditingManager auditingManager,
            IAuditWebInfo auditWebInfo,
            IClock clock)
        {
            _declarationImportFileRepository = declarationImportFileRepository;
            _validationAppService = validationAppService;
            _declarationBlobAppService = declarationBlobAppService;
            _distributedEventBus = distributedEventBus;
            _unitOfWorkManager = unitOfWorkManager;
            _imortCacheAppService = imortCacheAppService;
            _auditingManager = auditingManager;
            _clock = clock;
            _auditWebInfo = auditWebInfo;
        }

        public async Task<DeclarationImportFileDto> GetAsync(Guid id)
        {
            try
            {
                var file = await _declarationImportFileRepository.GetAsync(id);
                return ObjectMapper.Map<DeclarationImportFile, DeclarationImportFileDto>(file);
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        public async Task<ValidationResult> GetFileErrors(Guid fileId)
        {
            var file = await _declarationImportFileRepository.GetAsync(fileId);
            var result = new ValidationResult();
            CompareTool.AddExcelErrorToValidationResult(result, file.Errors);
            return result;
        }

        public async Task<ValidationResult> GetDataErrors(Guid fileId)
        {
            var file = await _declarationImportFileRepository.GetAsync(fileId);
            var result = new ValidationResult();
            CompareTool.AddExcelErrorToValidationResult(result, file.DataErrors);
            return result;
        }

        public async Task<List<ExcelValidationErrorCsvDto>> GetFileErrorsForCsv(Guid fileId)
        {
            var file = await _declarationImportFileRepository.GetAsync(fileId);
            var errors = ObjectMapper.Map<List<ExcelValidationError>, List<ExcelValidationErrorCsvDto>>(file.Errors);
            return errors;
        }

        public async Task<List<ExcelValidationErrorCsvDto>> GetDataErrorsForCsv(Guid fileId)
        {
            var file = await _declarationImportFileRepository.GetAsync(fileId);
            var errors = ObjectMapper.Map<List<ExcelValidationError>, List<ExcelValidationErrorCsvDto>>(file.DataErrors);
            return errors;
        }

        public async Task<byte[]> DownloadFileErrors(Guid fileId)
        {
            var errors = await GetFileErrorsForCsv(fileId);
            //
            // Add Audit Log for Download File Errors.
            //
            try
            {
                await this.AuditLogForExportImportErrors(fileId, new AuditExportImportErrorsDto
                {
                    FileId = fileId,
                    NumberOfErrors = errors.Count,
                }, true);
            }
            catch(Exception ex) {
                // Hide exception.
                this.Logger.LogError(ex, "AuditLogForExportImportErrors");
            }

            var data = CsvExporter.ExportData<ExcelValidationErrorCsvDto, ExcelValidationErrorCsvDtoMap>(errors);
            return data;
        }

        public async Task<byte[]> DownloadDataErrors(Guid fileId)
        {
            var errors = await GetDataErrorsForCsv(fileId);
            //
            // Add Audit Log for Download Data Errors.
            //
            try
            {
                await this.AuditLogForExportImportErrors(fileId, new AuditExportImportErrorsDto
                {
                    FileId = fileId,
                    NumberOfErrors = errors.Count,
                }, false);
            }
            catch(Exception ex)
            {
                // Hide exception.
                this.Logger.LogError(ex, "AuditLogForExportImportErrors");
            }

            var data = CsvExporter.ExportData<ExcelValidationErrorCsvDto, ExcelValidationErrorCsvDtoMap>(errors);
            return data;
        }

        public async Task<PagedResultDto<DeclarationImportFileDto>> GetFakeFileListAsync(int count, Guid tenantId)
        {
            using (CurrentTenant.Change(tenantId))
            {
                var files = ImportFilePayloadGenerator.GetUploadedDeclarationFileList(count, CurrentTenant.Id);

                try
                {
                    await _declarationImportFileRepository.InsertManyAsync(files, true);
                    var dbFiles = await _declarationImportFileRepository.GetListAsync(false);
                    var fileIds = dbFiles.Select(x => x.Id).Take(count);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }

                return new PagedResultDto<DeclarationImportFileDto>(
                   count,
                   ObjectMapper.Map<List<DeclarationImportFile>, List<DeclarationImportFileDto>>((List<DeclarationImportFile>)files)
               );
            }
        }

        public async Task MockDataParseProcessAsync(List<string> entityUniqueIds, Guid tenantId, int childCount = 1)
        {
            using (CurrentTenant.Change(tenantId))
            {
                var file = ImportFilePayloadGenerator.GetUploadedDeclarationFileList(1, CurrentTenant.Id).FirstOrDefault();
                if (file == null) { throw new Exception("Fail to generate Import File"); }
                file = await _declarationImportFileRepository.InsertAsync(file, true);

                await _validationAppService.MockDeclarationImportParse(file.Id, entityUniqueIds, childCount);
            }
        }

        public async Task<PagedResultDto<DeclarationImportFileDto>> GetFileListAsync(GetDeclarationImportFileDto input)
        {
            var fileTuple = await _declarationImportFileRepository.GetListAsync(
                input.SkipCount,
                input.MaxResultCount,
                input.Sorting??"",
                input.UploadedDateTime
            );

            return new PagedResultDto<DeclarationImportFileDto>(
               fileTuple.Count,
               ObjectMapper.Map<List<DeclarationImportFile>, List<DeclarationImportFileDto>>(fileTuple.FileList)
           );
        }

        public async Task<DeclarationImportFileDto> GetFile(Guid id)
        {
            var file = await _declarationImportFileRepository.GetAsync(id);
            var fileDto = ObjectMapper.Map<DeclarationImportFile, DeclarationImportFileDto>(file);
            return fileDto;
        }

        public async Task<DeclarationImportFileDto> GetFileInTriage()
        {

            var file = await _declarationImportFileRepository.GetFileInTriage();
            var fileDto = ObjectMapper.Map<DeclarationImportFile, DeclarationImportFileDto>(file);
            return fileDto;
        }

        public async Task<DeclarationImportFileDto> GetLastImportedFile()
        {
            var file = await _declarationImportFileRepository.GetLastImportedFile();
            var fileDto = ObjectMapper.Map<DeclarationImportFile, DeclarationImportFileDto>(file);
            return fileDto;
        }

        [UnitOfWork(IsDisabled = true)]
        public async Task<ExcelUploadResultDto> UploadDeclarationImportExcel(IFormFile uploadFile)
        {
            throw new UserFriendlyException("Import function is disabled temporarily.");
            if (CurrentTenant.Id == null)
            {
                throw new EssBusinessException("Import:MissingTenant", "Import only works with a Tenant selected");
            }
            Guid? fileId = null;
            try
            {
                var validationResult = await ShouldDirectReject(uploadFile.FileName);
                if (!validationResult.IsValid)
                {
                    return new ExcelUploadResultDto()
                    {
                        StatusId = UploadedFileStatus.Rejected,
                        StatusName = UploadedFileStatus.Rejected.GetEnumDescription(),
                        ValidationResult = validationResult
                    };
                }

                AuditESFileUploadDto newAuditESFileUpload = new AuditESFileUploadDto
                {
                    FileName = uploadFile.FileName,               
                };

                await PublishProgressAsync($"Uploading {uploadFile.FileName}", isFirstMessage: true);

                fileId = await UploadToStorage(uploadFile);
                if (fileId == null)
                {
                    return new ExcelUploadResultDto();
                }
                var statusName = UploadedFileStatus.Uploaded.GetEnumDescription();
                this.Logger.LogDebug("Start Import Progress Message: {0} DateTime: {1}", "Check Excel size is done.", DateTime.Now);

                await PublishProgressAsync($"Check Excel size is done.", fileId, statusName);

                var rt = await _validationAppService.ExecuteExcelProcessAsync(fileId.Value, uploadFile);
                statusName = rt.StatusId.GetEnumDescription();

                try
                {
                    newAuditESFileUpload.FileStatus = statusName;
                    newAuditESFileUpload.FileId = fileId.Value;
                    newAuditESFileUpload.NumberOfDeclarations = rt.NumberOfDeclarations;
                    newAuditESFileUpload.NumberOfErrors = rt.NumberOfErrors;

                    //
                    // Add Audit Log for ES File Upload.
                    //
                    await this.AuditLogForESFileUpload(newAuditESFileUpload);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "AuditLogForESFileUpload. FileId: {FileId}", fileId);
                }

                await PublishProgressAsync($"Process Finished.", fileId, statusName);
                return rt;

            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                _auditingManager.Current?.Log.Exceptions.Add(ex);
                if (fileId.HasValue)
                {
                    await _validationAppService.UpdateFileStatus(fileId.Value, UploadedFileStatus.FailedToImport);
                }
                await PublishProgressAsync($"Failed to Import.", fileId, UploadedFileStatus.FailedToImport.GetEnumDescription());
                throw;
            }
        }

        private async Task PublishProgressAsync(string message, Guid? fileId = null, string status = "", bool isFirstMessage = false)
        {
            var eto = new ProgressNotficiationEto(CurrentTenant.Id, CurrentUser.Id, message, fileId, status);
            eto.IsFirstMessage = isFirstMessage;
            //SignalR not working after deployment, don't publish the event
            //await _distributedEventBus.PublishAsync(eto, false);
            await _imortCacheAppService.PublishToCacheAsync(eto);

        }

        private async Task<Guid> UploadToStorage(IFormFile uploadFile)
        {
            Guid fileId;
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
            {
                //Always inserting new record, don't overwrite previous failed uploaded file
                fileId = await InsertDeclartionImportFile(uploadFile.FileName);

                await uow.CompleteAsync();
            }

            //Don't storage uploaded Excel file since they might contain sensitive data.
            var fileNamePath = "";
            //var fileNamePath = await _declarationBlobAppService.UploadImportedExcel(uploadFile, fileId);
            //Sometimes the event bus not working, so we need to update the file status here
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
            {
                var file = await _declarationImportFileRepository.GetAsync(fileId);
                if (!string.IsNullOrWhiteSpace(file.FileName))
                {
                    file.Status = UploadedFileStatus.Uploaded;
                    file.FileName = uploadFile.FileName;
                    file.FileUrl = fileNamePath;
                    await _declarationImportFileRepository.UpdateAsync(file, true);
                }
                await uow.CompleteAsync();
            }
            return fileId;
        }

        private async Task<ValidationResult> ShouldDirectReject(string fileName)
        {
            var validationResult = new ValidationResult();

            if (!new List<string> { ".xlsx", ".xls" }.Contains(Path.GetExtension(fileName).ToLower()))
            {
                validationResult.Errors.Add(new ValidationFailure("Reject", "The file extension is incorrect, it only supports Excel files."));
                return validationResult;
            }
            var alreadyHasFileInTriage = false;

            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                alreadyHasFileInTriage = await _declarationImportFileRepository.HasFileInTriageAsyc();
                await uow.CompleteAsync();
            }
            if (alreadyHasFileInTriage)
            {
                validationResult.Errors.Add(new ValidationFailure("Reject", "New upload is not accepted when there is file in triage."));
            }
            return validationResult;
        }

        private async Task<Guid> InsertDeclartionImportFile(string fileName)
        {
            var file = new DeclarationImportFile
            {
                FileName = fileName,
                TenantId = CurrentTenant.Id,
                UploadedDateTime = _clock.Now,
                SubmitterId = CurrentUser.Id,
                SubmitterName = $"{CurrentUser.Name} {CurrentUser.SurName}",
                Status = UploadedFileStatus.Processing,
            };
            var inserted = await _declarationImportFileRepository.InsertAsync(file, true);
            return inserted.Id;
        }

        public async Task<bool> HasFileInTriageAsync()
        {
            return await _declarationImportFileRepository.HasFileInTriageAsyc();
        }

        public async Task<ProgressNotficiationEto> GetUploadProgress()
        {
            return await _imortCacheAppService.GetUploadProgressAsync();
        }

        /// <summary>
        ///  Audit Log for ES File Upload.
        /// </summary>
        /// <param name="newValue"></param>
        /// <returns></returns>
        private async Task<bool> AuditLogForESFileUpload(AuditESFileUploadDto newValue)
        {
            try
            {
                AuditESFileUploadEto eto = new AuditESFileUploadEto
                {
                    UserName = this.CurrentUser.UserName,
                    UserId = this.CurrentUser.Id,
                    IPAddress = _auditWebInfo.IPAddress,
                    Action = AuditActionEnum.ESFileUpload,
                    TenantId = CurrentTenant.Id,
                    AuditDateTime = DateTime.UtcNow,
                    NewValue = newValue
                };

                await _distributedEventBus.PublishAsync(eto);

                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "AuditLogForESFileUpload. FileId: {0}", newValue?.FileId);
                return false;
            }
        }


        /// <summary>
        ///  Audit Log for Export Import Errors
        /// </summary>
        /// <param name="newValue"></param>
        /// <returns></returns>
        private async Task<bool> AuditLogForExportImportErrors(Guid? fileId, AuditExportImportErrorsDto newValue, bool isFileError)
        {            
            try
            {
                if (fileId.HasValue)
                {
                    var file = await _declarationImportFileRepository.GetAsync(fileId.Value);

                    newValue.FileId = fileId;
                    newValue.FileName = file.FileName;
                    newValue.FileStatus = file.Status.GetEnumDescription();
                    newValue.NumberOfDeclarations = file.NumberOfDeclarations;
                    newValue.FileId = fileId.Value;
                    newValue.DownloadFileName = isFileError? string.Format("FileErrors_{0}.csv", fileId.Value) : string.Format("DataErrors_{0}.csv", fileId.Value);
                    newValue.Type = isFileError? "File" : "Data";

                    AuditExportImportErrorsEto eto = new AuditExportImportErrorsEto
                    {
                        UserName = this.CurrentUser.UserName,
                        UserId = this.CurrentUser.Id,
                        IPAddress = _auditWebInfo.IPAddress,
                        Action = AuditActionEnum.ExportImportErrors,
                        TenantId = CurrentTenant.Id,
                        AuditDateTime = DateTime.UtcNow,
                        NewValue = newValue
                    };

                    await _distributedEventBus.PublishAsync(eto);

                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "AuditLogForExportImportErrors. FileId: {0}", fileId);
                return false;
            }
        }

    }
}