﻿using Bdo.Ess.Shared.Hosting.Microservices.BdoAuthHelper;
using IdentityModel.Client;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.IdentityModel;

namespace Ess.SearchReIndexerJob.Services
{
    public class SearchReIndexerService : ITransientDependency
    {
        private readonly IIdentityModelAuthenticationService _authenticationService;
        private readonly IConfiguration _configuration;

        public SearchReIndexerService(IIdentityModelAuthenticationService authenticationService,
            IConfiguration configuration
            )
        {
            _authenticationService = authenticationService;
            _configuration = configuration;
        }

        public async Task RunAsync() {
            Console.WriteLine("Starting to migrate Previously Submitted Declarations with no Assessment Status");
            await UpdateSubmitted();

            Console.WriteLine("Dropping all search index");
            await DropIndexes();

            Console.WriteLine("Starting to reindex Corporate Entities");
            await ReIndexEntity();
            Console.WriteLine("Finished triggering Reindex on Corporate Entities. The Search Service will continue to run the actual indexing async");
            
            Console.WriteLine("Starting to reindex Declarations");
            await ReIndexDeclaration();
            Console.WriteLine("Finished triggering Reindex on Declarations. The Search Service will continue to run the actual indexing async");
            
            Console.WriteLine("Application Completed Successfully");
        }


        private async Task UpdateSubmitted()
        {
            var apiPath = "MigrateSubmittedDeclarations";
            var url = _configuration["EconomicSubstanceSearchHelperRootUrl"] + apiPath;

            await CallHttp(url);

        }
        private async Task DropIndexes()
        {
            var apiPath = "DropAllSearchIndex";
            var url = _configuration["EconomicSubstanceSearchHelperRootUrl"] + apiPath;

            await CallHttp(url);
        }

        private async Task ReIndexEntity()
        {
            var apiPath = "ReIndexEntitySearchIndexAllTenants";
            var url = _configuration["CorporateEntitySearchHelperRootUrl"] + apiPath;

            await CallHttp(url);
        }

        private async Task ReIndexDeclaration()
        {
            var apiPath = "UpdateSearchIndexAllTenants";
            var url = _configuration["EconomicSubstanceSearchHelperRootUrl"] + apiPath;

            await CallHttp(url);
        }


		private async Task CallHttp(string url)
		{
			var accessToken = await _authenticationService.GetAccessTokenAsync(
				new IdentityClientConfiguration(
					_configuration["IdentityClients:Default:Authority"],
					_configuration["IdentityClients:Default:Scope"],
					_configuration["IdentityClients:Default:ClientId"],
					_configuration["IdentityClients:Default:ClientSecret"],
					_configuration["IdentityClients:Default:GrantType"],
                    cacheAbsoluteExpiration: 1, // set it to 1 second to force refresh everyt time
					requireHttps: false
				)
			);

			using var httpClient = new HttpClient() { Timeout = TimeSpan.FromHours(24) };
			httpClient.SetBearerToken(accessToken);

			int retryCount = 3;
			for (int i = 0; i < retryCount; i++)
			{
				try
				{
					var responseMessage = await httpClient.GetAsync(url);
					if (responseMessage.IsSuccessStatusCode)
					{
						var responseString = await responseMessage.Content.ReadAsStringAsync();
						Console.WriteLine($"CallHttp: {url} success");
						return;
					}
					else
					{
						Console.WriteLine($"CallHttp: {url} failed with status code: {responseMessage.StatusCode}, Attempt {i + 1} ");
					}
				}
				catch (Exception ex)
				{
					Console.WriteLine($"CallHttp: {url} failed with exception: {ex.Message}, Attempt {i + 1}");
				}

				if (i < retryCount - 1)
				{
					await Task.Delay(TimeSpan.FromSeconds(5)); // Wait before retrying
				}
			}

			throw new Exception($"CallHttp: {url} failed after multiple attempts");
		}
	}
}
