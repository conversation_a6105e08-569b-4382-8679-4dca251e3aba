{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@app/shared/services/upload-file.service\";\nimport * as i4 from \"@abp/ng.theme.shared\";\nimport * as i5 from \"@app/shared/services/sweetalert.service\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/progress-bar\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"@ngx-validate/core\";\nimport * as i12 from \"@angular/common\";\nfunction DecryptDataPacketDialogComponent_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r2.selectedFile.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFile.name, \" \");\n  }\n}\nfunction DecryptDataPacketDialogComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1, \"No file chosen\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DecryptDataPacketDialogComponent_mat_hint_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 26);\n    i0.ɵɵtext(1, \" File is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DecryptDataPacketDialogComponent_mat_progress_bar_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-progress-bar\", 27);\n  }\n}\nfunction DecryptDataPacketDialogComponent_div_27_ul_5_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r4);\n  }\n}\nfunction DecryptDataPacketDialogComponent_div_27_ul_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 32);\n    i0.ɵɵtemplate(1, DecryptDataPacketDialogComponent_div_27_ul_5_li_1_Template, 2, 1, \"li\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.xmlError);\n  }\n}\nfunction DecryptDataPacketDialogComponent_div_27_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.xmlError[0]);\n  }\n}\nfunction DecryptDataPacketDialogComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"i\", 30);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, DecryptDataPacketDialogComponent_div_27_ul_5_Template, 2, 1, \"ul\", 31)(6, DecryptDataPacketDialogComponent_div_27_ng_template_6_Template, 2, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const singleError_r5 = i0.ɵɵreference(7);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Validation Error\", ctx_r2.xmlError.length > 1 ? \"s\" : \"\", \":\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.xmlError.length > 1)(\"ngIfElse\", singleError_r5);\n  }\n}\nexport let DecryptDataPacketDialogComponent = /*#__PURE__*/(() => {\n  class DecryptDataPacketDialogComponent {\n    constructor(dialogRef, fb, fileUploadService, toasterService, sweetAlert) {\n      this.dialogRef = dialogRef;\n      this.fb = fb;\n      this.fileUploadService = fileUploadService;\n      this.toasterService = toasterService;\n      this.sweetAlert = sweetAlert;\n      this.selectedFile = null;\n      this.xmlError = [];\n      this.isInProgress = false;\n      this.form = this.fb.group({\n        file: [null, Validators.required]\n      });\n    }\n    onFileChange(event) {\n      const input = event.target;\n      const file = input.files && input.files[0];\n      this.xmlError = [];\n      if (file) {\n        if (!file.name.toLowerCase().endsWith('.zip')) {\n          this.xmlError = ['Only ZIP files are allowed.'];\n          this.resetFile();\n          return;\n        }\n        this.selectedFile = file;\n        this.form.get('file')?.setValue(file);\n      } else {\n        this.resetFile();\n      }\n    }\n    resetFile() {\n      this.selectedFile = null;\n      this.form.patchValue({\n        file: null\n      });\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (!_this.selectedFile) {\n          _this.form.get('file')?.markAsTouched();\n          return;\n        }\n        if (_this.form.invalid) return;\n        if (_this.form.valid) {\n          _this.isInProgress = true;\n          const formData = new FormData();\n          formData.append('fileName', _this.selectedFile.name);\n          formData.append('file', _this.selectedFile);\n          formData.append('fileType', _this.selectedFile.type);\n          _this.fileUploadService.unpackCtsPackage(formData).subscribe({\n            next: response => {\n              _this.toasterService.success('Decrypt Data Packet successfully completed', '', {\n                life: 5000\n              });\n              _this.dialogRef.close({\n                response\n              });\n            },\n            error: error => {\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n              _this.xmlError = [error?.error];\n              _this.isInProgress = false;\n              console.error('Error decrypting data packet:', error);\n            },\n            complete: () => {\n              _this.isInProgress = false;\n            }\n          });\n        }\n      })();\n    }\n    onCancel() {\n      if (this.form.dirty) {\n        this.sweetAlert.fireDialog({\n          action: \"delete\",\n          title: \"Are you sure you want to close?\",\n          text: \"Any unsaved changes may be lost\",\n          type: \"confirm\"\n        }, confirm => {\n          if (confirm) {\n            this.dialogRef.close();\n          }\n        });\n      } else {\n        this.dialogRef.close();\n      }\n    }\n    static {\n      this.ɵfac = function DecryptDataPacketDialogComponent_Factory(t) {\n        return new (t || DecryptDataPacketDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.FileUploadService), i0.ɵɵdirectiveInject(i4.ToasterService), i0.ɵɵdirectiveInject(i5.SweetAlertService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DecryptDataPacketDialogComponent,\n        selectors: [[\"app-decrypt-data-packet-dialog\"]],\n        decls: 33,\n        vars: 7,\n        consts: [[\"noFile\", \"\"], [\"fileInput\", \"\"], [\"singleError\", \"\"], [\"mat-dialog-title\", \"\"], [1, \"row\", \"align-items-center\"], [1, \"col-8\", \"title\"], [1, \"col-4\", \"text-end\", \"modal-action-button\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"file-upload-group\"], [1, \"file-upload-label\"], [1, \"required\"], [1, \"file-upload-row\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 3, \"click\"], [\"class\", \"file-name\", 3, \"matTooltip\", 4, \"ngIf\", \"ngIfElse\"], [\"accept\", \".zip\", \"type\", \"file\", \"formControlName\", \"file\", \"required\", \"\", 1, \"file-input\", 3, \"change\"], [\"class\", \"file-error\", 4, \"ngIf\"], [1, \"status-section\"], [\"mode\", \"indeterminate\", 4, \"ngIf\"], [\"class\", \"alert alert-danger xml-error-container\", 4, \"ngIf\"], [\"align\", \"end\"], [\"mat-raised-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"type\", \"submit\", 1, \"ui-button\", 3, \"disabled\"], [1, \"file-name\", 3, \"matTooltip\"], [1, \"file-placeholder\"], [1, \"file-error\"], [\"mode\", \"indeterminate\"], [1, \"alert\", \"alert-danger\", \"xml-error-container\"], [1, \"xml-error-header\"], [1, \"fas\", \"fa-exclamation-triangle\"], [\"class\", \"xml-error-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"xml-error-list\"], [\"class\", \"xml-error-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"xml-error-item\"], [1, \"xml-error-single\"]],\n        template: function DecryptDataPacketDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5);\n            i0.ɵɵtext(3, \"Decrypt Received Data Packet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 6)(5, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function DecryptDataPacketDialogComponent_Template_button_click_5_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onCancel());\n            });\n            i0.ɵɵelement(6, \"i\", 8);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(7, \"form\", 9);\n            i0.ɵɵlistener(\"ngSubmit\", function DecryptDataPacketDialogComponent_Template_form_ngSubmit_7_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementStart(8, \"mat-dialog-content\")(9, \"div\", 10)(10, \"label\", 11);\n            i0.ɵɵtext(11, \"ZIP File \");\n            i0.ɵɵelementStart(12, \"span\", 12);\n            i0.ɵɵtext(13, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 13)(15, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function DecryptDataPacketDialogComponent_Template_button_click_15_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const fileInput_r2 = i0.ɵɵreference(23);\n              return i0.ɵɵresetView(fileInput_r2.click());\n            });\n            i0.ɵɵelementStart(16, \"mat-icon\");\n            i0.ɵɵtext(17, \"upload_file\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(18, \" Choose File \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(19, DecryptDataPacketDialogComponent_span_19_Template, 2, 2, \"span\", 15)(20, DecryptDataPacketDialogComponent_ng_template_20_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"input\", 16, 1);\n            i0.ɵɵlistener(\"change\", function DecryptDataPacketDialogComponent_Template_input_change_22_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileChange($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(24, DecryptDataPacketDialogComponent_mat_hint_24_Template, 2, 0, \"mat-hint\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 18);\n            i0.ɵɵtemplate(26, DecryptDataPacketDialogComponent_mat_progress_bar_26_Template, 1, 0, \"mat-progress-bar\", 19)(27, DecryptDataPacketDialogComponent_div_27_Template, 8, 3, \"div\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"mat-dialog-actions\", 21)(29, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function DecryptDataPacketDialogComponent_Template_button_click_29_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onCancel());\n            });\n            i0.ɵɵtext(30, \"Cancel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"button\", 23);\n            i0.ɵɵtext(32, \"Submit\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_5_0;\n            const noFile_r6 = i0.ɵɵreference(21);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"formGroup\", ctx.form);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedFile)(\"ngIfElse\", noFile_r6);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.form.get(\"file\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.form.get(\"file\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isInProgress);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.xmlError.length > 0);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.form.invalid);\n          }\n        },\n        dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.FormGroupDirective, i2.FormControlName, i6.MatHint, i7.MatIcon, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i8.MatButton, i9.MatProgressBar, i10.MatTooltip, i11.ValidationGroupDirective, i11.ValidationDirective, i12.NgForOf, i12.NgIf],\n        styles: [\".title[_ngcontent-%COMP%]{font-size:1.3em;color:#00779b;display:block}.modal-action-button[_ngcontent-%COMP%]{font-size:1em}.file-upload-group[_ngcontent-%COMP%]{margin-bottom:16px;display:flex;flex-direction:column}.file-upload-label[_ngcontent-%COMP%]{font-weight:500;margin-bottom:4px}.required[_ngcontent-%COMP%]{color:red}.file-upload-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin-bottom:4px}.file-input[_ngcontent-%COMP%]{display:none}.file-name[_ngcontent-%COMP%]{flex:1 1 auto;max-width:250px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:#333;cursor:pointer}.file-placeholder[_ngcontent-%COMP%]{color:#888;font-style:italic}.file-error[_ngcontent-%COMP%]{color:#f44336}.status-section[_ngcontent-%COMP%]{margin:16px 0}.error-message[_ngcontent-%COMP%]{color:#d32f2f;background:#fdecea;border:1px solid #f44336;border-radius:4px;padding:8px;margin-top:12px;display:flex;align-items:center;font-size:.98em}.error-icon[_ngcontent-%COMP%]{margin-right:8px}.xml-error-container[_ngcontent-%COMP%]{background-color:#f8d7da;border:1px solid #f5c6cb;border-radius:.375rem;padding:.75rem 1rem;margin-top:.5rem}.xml-error-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem}.xml-error-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#721c24;font-size:1.1em}.xml-error-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#721c24;font-size:.95em}.xml-error-list[_ngcontent-%COMP%]{margin:0;padding-left:1.5rem;color:#721c24}.xml-error-list[_ngcontent-%COMP%]   .xml-error-item[_ngcontent-%COMP%]{margin-bottom:.25rem;font-size:.9em;line-height:1.4}.xml-error-list[_ngcontent-%COMP%]   .xml-error-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.xml-error-single[_ngcontent-%COMP%]{color:#721c24;font-size:.9em;line-height:1.4;margin-left:1.75rem}\"]\n      });\n    }\n  }\n  return DecryptDataPacketDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}