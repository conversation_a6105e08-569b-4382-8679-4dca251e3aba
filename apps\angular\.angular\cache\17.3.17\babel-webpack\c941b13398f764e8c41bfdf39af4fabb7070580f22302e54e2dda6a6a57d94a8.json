{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport let FileUploadService = /*#__PURE__*/(() => {\n  class FileUploadService {\n    constructor(restService) {\n      this.restService = restService;\n      this.apiName = 'EconomicSubstanceService';\n      this.uploadDeclarationImportExcelByFile = file => this.restService.request({\n        method: 'POST',\n        url: '/api/ESService/Import/File/UploadDeclarationImportExcel',\n        body: file\n      }, {\n        apiName: this.apiName\n      });\n      /**\n       * Since Proxy client cannot generate associated web API method since IFormFile compile error, it needs to be manually called here.\n       * Called by CA portal information exchange import page, upload excel file.\n       *\n       */\n      this.uploadInformationExchangeImportExcelByFile = file => this.restService.request({\n        method: 'POST',\n        url: '/api/ESService/Import/InfoExchange/UploadInfoExchangeImportExcel',\n        body: file\n      }, {\n        apiName: this.apiName\n      });\n      this.uploadBahamasCertificate = (file, password) => {\n        // Add password to the FormData instead of query params for security\n        file.append('password', password);\n        return this.restService.request({\n          method: 'POST',\n          url: '/api/CtsIntegrationService/certificate/UploadBahamasCertificate',\n          body: file\n        }, {\n          apiName: this.apiName\n        });\n      };\n      this.uploadHistoricalXml = (file, receivingCountry, financialPeriodEnd) => this.restService.request({\n        method: 'POST',\n        url: '/api/ESService/CtsIntegration/UploadHistoricalXml',\n        params: {\n          receivingCountry,\n          financialPeriodEnd\n        },\n        body: file\n      }, {\n        apiName: this.apiName\n      });\n      this.unpackCtsPackage = file => this.restService.request({\n        method: 'POST',\n        url: '/api/CtsIntegrationService/CtsPackageRequest/UnpackCtsPackage',\n        body: file\n      }, {\n        apiName: this.apiName\n      });\n      this.createBahamasCtsSettings = (input, file, ctsCert) => {\n        const formData = new FormData();\n        // Add sensitive data to form body instead of query params for security\n        formData.append('systemUserName', input.systemUserName || '');\n        formData.append('systemUserPassword', input.systemUserPassword || '');\n        formData.append('sftpUserName', input.sftpUserName || '');\n        if (file) {\n          formData.append('file', file.get('file'));\n        }\n        if (ctsCert) {\n          formData.append('ctsCert', ctsCert.get('file'));\n        }\n        return this.restService.request({\n          method: 'POST',\n          url: '/api/CtsIntegrationService/bahamas-cts-settings',\n          body: formData\n        }, {\n          apiName: this.apiName\n        });\n      };\n      this.updateBahamasCtsSettings = (input, file, ctsCert) => {\n        const formData = new FormData();\n        // Add sensitive data to form body instead of query params for security\n        formData.append('id', input.id || '');\n        formData.append('systemUserName', input.systemUserName || '');\n        formData.append('systemUserPassword', input.systemUserPassword || '');\n        formData.append('sftpUserName', input.sftpUserName || '');\n        if (file) {\n          formData.append('file', file.get('file'));\n        }\n        if (ctsCert) {\n          formData.append('ctsCert', ctsCert.get('file'));\n        }\n        return this.restService.request({\n          method: 'PUT',\n          url: '/api/CtsIntegrationService/bahamas-cts-settings',\n          body: formData\n        }, {\n          apiName: this.apiName\n        });\n      };\n    }\n    static {\n      this.ɵfac = function FileUploadService_Factory(t) {\n        return new (t || FileUploadService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FileUploadService,\n        factory: FileUploadService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FileUploadService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}