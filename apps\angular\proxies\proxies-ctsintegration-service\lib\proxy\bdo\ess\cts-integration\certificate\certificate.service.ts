import type { BahamasCertificateDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CertificateService {
  apiName = 'CtsIntegrationService';
  

  getBahamasCertificateInfo = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, BahamasCertificateDto>({
      method: 'GET',
      url: '/api/CtsIntegrationService/certificate/GetBahamasCertificateInfo',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
