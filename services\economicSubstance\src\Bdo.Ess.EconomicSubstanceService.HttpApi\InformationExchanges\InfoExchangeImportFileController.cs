﻿using Bdo.Ess.EconomicSubstanceService.DeclarationImports.Dtos;
using Bdo.Ess.EconomicSubstanceService.InformationExchanges.HistoricalMigration;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.DeclarationImports;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using static Bdo.Ess.EconomicSubstanceService.Permissions.ESServicePermissions;

namespace Bdo.Ess.EconomicSubstanceService.InformationExchanges
{
    [RemoteService(Name = EconomicSubstanceServiceRemoteServiceConsts.RemoteServiceName)]
    [Area("EconomicSubstanceService")]
    [Route("api/ESService/Import/InfoExchange")]
    [Authorize]
    public class InfoExchangeImportFileController : EconomicSubstanceServiceController, IInfoExchangeImportAppService
    {
        private readonly IInfoExchangeImportAppService _importFileAppService;

        public InfoExchangeImportFileController(
            IInfoExchangeImportAppService importAppService
            )
        {
            _importFileAppService = importAppService;
        }

        [HttpPost]
        [Route("UploadInfoExchangeImportExcel")]
        //[ApiExplorerSettings(IgnoreApi = true)]
        [Authorize(InformationExchangeImportFile.Import)]
        public async Task<ExcelUploadInfoExchangeResultDto> UploadInfoExchangeImportExcel(IFormFile file)
        {
            return await _importFileAppService.UploadInfoExchangeImportExcel(file);
        }

        [HttpGet]
        [Route("GetFileList")]
        public Task<PagedResultDto<InfoExchangeImportFileDto>> GetFileList(GetInfoExchangeImportFileDto input)
        {
            return _importFileAppService.GetFileList(input);
        }

        [HttpGet]
        [Route("{id}")]
        public async Task<InfoExchangeImportFileDto> GetAsync(Guid id)
        {
            return await _importFileAppService.GetAsync(id);
        }

        [HttpGet]
        [Route("GetFileReadyToSubmit")]
        public async Task<InfoExchangeImportFileDto> GetFileReadyToSubmit()
        {
            return await _importFileAppService.GetFileReadyToSubmit();
        }
        [HttpGet]
        [Route("GetLastImportedFile")]
        public async Task<InfoExchangeImportFileDto> GetLastImportedFile()
        {
            return await _importFileAppService.GetLastImportedFile();
        }
        [HttpGet]
        [Route("GetUploadProgress")]
        public async Task<ProgressNotficiationEto> GetUploadProgress()
        {
            return await _importFileAppService.GetUploadProgress();
        }

        [HttpGet]
        [Route("GetFileErrors")]
        public async Task<ValidationResult> GetFileErrors(Guid fileId)
        {
            return await _importFileAppService.GetFileErrors(fileId);
        }

        [HttpGet]
        [Route("GetDataErrors")]
        public async Task<ValidationResult> GetDataErrors(Guid fileId)
        {
            return await _importFileAppService.GetDataErrors(fileId);
        }

        [HttpGet]
        [Route("DownloadFileErrors")]
        public async Task<byte[]> DownloadFileErrors(Guid fileId)
        {
            var fileBytes = await _importFileAppService.DownloadFileErrors(fileId);
            return fileBytes;
        }

        [HttpGet]
        [Route("DownloadDataErrors")]
        public async Task<byte[]> DownloadDataErrors(Guid fileId)
        {
            var fileBytes = await _importFileAppService.DownloadDataErrors(fileId);
            return fileBytes;
        }

        [HttpGet]
        [Route("HasFileReadyToSumbit")]
        public async Task<bool> HasFileReadyToSumbit()
        {
            return await _importFileAppService.HasFileReadyToSumbit();
        }

        [HttpPost]
        [Route("SubmitFile")]
        [Authorize(InformationExchangeImportFile.Submit)]
        public async Task<ValidationResult> SubmitFile(Guid fileId)
        {
            return await _importFileAppService.SubmitFile(fileId);
        }

        [HttpPost]
        [Route("DiscardFile")]
        [Authorize(InformationExchangeImportFile.Discard)]
        public async Task DiscardFile(Guid fileId)
        {
            await _importFileAppService.DiscardFile(fileId);
        }
    }
}
