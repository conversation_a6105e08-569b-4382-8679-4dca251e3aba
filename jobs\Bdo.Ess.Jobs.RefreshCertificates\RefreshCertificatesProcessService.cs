﻿using IdentityModel;
using IdentityModel.Client;

using Microsoft.Extensions.Configuration;

using Volo.Abp.DependencyInjection;
using Volo.Abp.IdentityModel;

namespace Bdo.Ess.Jobs.RefreshCertificates
{
    public class RefreshCertificatesProcessService : ITransientDependency
    {
        private readonly IIdentityModelAuthenticationService _authenticationService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        public RefreshCertificatesProcessService(IIdentityModelAuthenticationService authenticationService,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration)
        {
            _authenticationService = authenticationService;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
        }
        public async Task RunAsync()
        {
            try
            {
				Console.WriteLine("Start of RefreshCertificates Job");
				await RefreshCertificates();
				Console.WriteLine("End of RefreshCertificates Job");
			}
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                throw;
            }
        }

        private async Task RefreshCertificates()
        {
            try
            {
                var apiPath = "api/CtsIntegrationService/country-certificates/refresh";

                var accessToken = await GetAccessToken();

                
                HttpClient httpClient =  _httpClientFactory.CreateClient("EssClient");
                httpClient.SetBearerToken(accessToken);

                var baseUrl = _configuration[$"RemoteServices:CtsIntegrationService:BaseUrl"] ?? "";

                var url = baseUrl + apiPath;
                Console.WriteLine("Refresh Certificates Job - calling url: " + url);
                var responseMessage = await httpClient.PostAsync(url, null);
                
                if (!responseMessage.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Refresh Certificates Job - returns error code: " + responseMessage.StatusCode);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("RefreshCertificates {0}", ex.Message);
            }
        }
        private async Task<string> GetAccessToken()
        {
            var accessToken = await _authenticationService.GetAccessTokenAsync(
                new IdentityClientConfiguration(
                    _configuration["IdentityClients:Default:Authority"]!,
                    _configuration["IdentityClients:Default:Scope"]!,
                    _configuration["IdentityClients:Default:ClientId"]!,
                    _configuration["IdentityClients:Default:ClientSecret"]!,
                    grantType: OidcConstants.GrantTypes.ClientCredentials,
                    requireHttps: false
                )
            );
            return accessToken;
        }
    }
}
