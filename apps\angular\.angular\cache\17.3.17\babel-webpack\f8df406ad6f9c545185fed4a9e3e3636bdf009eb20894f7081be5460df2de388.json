{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport class CertificateService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'CtsIntegrationService';\n    this.getBahamasCertificateInfo = config => this.restService.request({\n      method: 'GET',\n      url: '/api/CtsIntegrationService/certificate/GetBahamasCertificateInfo'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function CertificateService_Factory(t) {\n      return new (t || CertificateService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CertificateService,\n      factory: CertificateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["CertificateService", "constructor", "restService", "apiName", "getBahamasCertificateInfo", "config", "request", "method", "url", "i0", "ɵɵinject", "i1", "RestService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\proxies\\proxies-ctsintegration-service\\lib\\proxy\\bdo\\ess\\cts-integration\\certificate\\certificate.service.ts"], "sourcesContent": ["import type { BahamasCertificateDto } from './models';\r\nimport { RestService, Rest } from '@abp/ng.core';\r\nimport { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CertificateService {\r\n  apiName = 'CtsIntegrationService';\r\n  \r\n\r\n  getBahamasCertificateInfo = (config?: Partial<Rest.Config>) =>\r\n    this.restService.request<any, BahamasCertificateDto>({\r\n      method: 'GET',\r\n      url: '/api/CtsIntegrationService/certificate/GetBahamasCertificateInfo',\r\n    },\r\n    { apiName: this.apiName,...config });\r\n\r\n  constructor(private restService: RestService) {}\r\n}\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,kBAAkB;EAW7BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAV/B,KAAAC,OAAO,GAAG,uBAAuB;IAGjC,KAAAC,yBAAyB,GAAIC,MAA6B,IACxD,IAAI,CAACH,WAAW,CAACI,OAAO,CAA6B;MACnDC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE;KACN,EACD;MAAEL,OAAO,EAAE,IAAI,CAACA,OAAO;MAAC,GAAGE;IAAM,CAAE,CAAC;EAES;;;uBAXpCL,kBAAkB,EAAAS,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAlBZ,kBAAkB;MAAAa,OAAA,EAAlBb,kBAAkB,CAAAc,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}