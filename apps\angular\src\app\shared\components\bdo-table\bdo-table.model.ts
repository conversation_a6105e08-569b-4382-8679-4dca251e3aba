export interface BdoTableColumnDefinition {
    columnId: string;
    columnName: string;
    isSortable?: boolean;
    sortColumnId?: string;

    width?: number; // in pixels
    minWidth?: number; // in pixels
    maxWidth?: number; // in pixels

    //used if column mode = 'flex'
    flexGrow?: number;

    frozenLeft?: boolean;
    frozenRight?: boolean;

    type: BdoTableColumnType;

    //custom css class applied to column header
    class?: string;

    matHeaderClass?: string; // custom css class applied to mat header cell
}

export class BdoTableData {
    constructor(tableId?: string, data?: BdoTableRowData[], totalRecords?: number, resetToFirstPage = false) {
        this.tableId = tableId;
        this.data = data;
        this.totalRecords = totalRecords;
        this.resetToFirstPage = resetToFirstPage;
    }

    public tableId: string | undefined;
    public data: BdoTableRowData[] | undefined;
    public totalRecords: number | undefined;

    //data for the footer row
    public hasFooter: boolean;
    public footerCells: BdoTableCellData[];

    public resetToFirstPage: boolean;
}

export interface BdoTableRowData {
    id: string;
    class?: string; // css class applied on row level
    cells: BdoTableCellData[];
    searchableText?: string; //text searchable by global filter
    height?: number; //can define height of row manually
    rawData?: any; //pass raw data to be used later
    cannotCheck?: boolean; // indicates whether you cannot check this row for selection
    checked?: boolean; // indicates if the row is selected
    hideExpand?: boolean; //row is not expandable so hide he icon to expand/collapse
}

export interface BdoTableCellData {
    class?: string; // css class applied on cell level
    columnId: string;
    value: any;
    hide?: boolean;

    //used for columns that are conditonally link or text
    //for columns that are 'isLink=true', set this property to true to display as text only, not a link
    isTextInsteadOfLink?: boolean;
}

export interface BdoTableActionsRowData {
    actionType: string;
    tooltip?: string;
    icon?: string;
    displayName?: string;
    source? :string;
}

export class BdoTableRowClickEvent {
    public id: string;
    public rowData: BdoTableRowData;
}

export class BdoTableCellLinkClickEvent {
    public id: string;
    public columnId: string;
    public rawData: any;
    public event?: any; // click event
    public arrayIndex?: number;
}

export class BdoTableRowActionClickEvent {
    public action: string;
    public id: string;
    public data?: BdoTableRowData;
    public event?: any; // click event
}

export class BdoTableLazyLoadEvent {
    public pageNumber: number;
    public pageSize: number;
    public sortField: string;
    public isAscending: boolean;
}

export class BdoTableRowExpandedEvent {
    public id: string;
    public rawData: any;
}

export class BdoTableCheckboxClickEvent {
    constructor(
        selectedRows: BdoTableRowData[],
        rowId: string,
        isChecked: boolean,
        isSelectAll: boolean) {
            this.selectedRows = selectedRows,
            this.rowId = rowId,
            this.isChecked = isChecked,
            this.isSelectAll = isSelectAll
    }
    public rowId: string;
    public selectedRows: BdoTableRowData[];

    //type of event: check or uncheck
    public isChecked: boolean;
    public isSelectAll: boolean;
}

export enum BdoTableColumnType {
    String = 0,
    Boolean = 1,
    YesNo = 2,
    Html = 3,
    Array = 4,
    Link = 5,
    Number = 6,
    Date = 7,
    Currency = 8,
    Actions = 9,
    Checkbox = 10,
    FileSize = 11,
    DateTime = 12,
    ThreeDotActions = 13,
    LinkArray = 14,
    SingleActionButton = 15
}

export interface BdoTablePageInfo {
    offset: number;
    pageSize: number;
    limit: number;
    count: number;
}

/**
 * An object used to get page information from the server
 */
export class BdoTablePage {
    // The number of elements in the page
    size = 0;
    // The total number of elements
    totalElements = 0;
    // The total number of pages
    totalPages = 0;
    // The current page number
    pageNumber = 0;
}
