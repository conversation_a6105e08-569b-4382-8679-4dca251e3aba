{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@abp/ng.core\";\nexport let CertificateService = /*#__PURE__*/(() => {\n  class CertificateService {\n    constructor(restService) {\n      this.restService = restService;\n      this.apiName = 'CtsIntegrationService';\n      this.getBahamasCertificateInfo = config => this.restService.request({\n        method: 'GET',\n        url: '/api/CtsIntegrationService/certificate/GetBahamasCertificateInfo'\n      }, {\n        apiName: this.apiName,\n        ...config\n      });\n    }\n    static {\n      this.ɵfac = function CertificateService_Factory(t) {\n        return new (t || CertificateService)(i0.ɵɵinject(i1.RestService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CertificateService,\n        factory: CertificateService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return CertificateService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}