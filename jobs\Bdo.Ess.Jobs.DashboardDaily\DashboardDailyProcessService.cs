﻿using IdentityModel;
using IdentityModel.Client;
using Microsoft.Extensions.Configuration;
using Volo.Abp.DependencyInjection;
using Volo.Abp.IdentityModel;

namespace Bdo.Ess.Jobs.DashboardDaily
{
    public class DashboardDailyProcessService : ITransientDependency
    {
        private readonly IIdentityModelAuthenticationService _authenticationService;
        private readonly IConfiguration _configuration;

        public DashboardDailyProcessService(IIdentityModelAuthenticationService authenticationService,
            IConfiguration configuration)
        {
            _authenticationService = authenticationService;
            _configuration = configuration;
        }
        public async Task RunAsync()
        {
            Console.WriteLine("Starts update Dashboard Stats");
            await UpdateDashboardStats();
            Console.WriteLine("Starts update Dashboard Stats");
        }

        private async Task UpdateDashboardStats()
        {
            try
            {
                var apiPath = "api/DashboardService/CADashboard/GenerateDashboardStatisticDataDaily";

                var accessToken = await GetAccessToken();
                Console.WriteLine("AccessToken aquired");
                //Perform the actual HTTP request
                using var httpClient = new HttpClient() { Timeout = TimeSpan.FromHours(24) };
                httpClient.SetBearerToken(accessToken);

                var baseUrl = _configuration[$"RemoteServices:DashboardService:BaseUrl"] ?? "";

                var url = baseUrl + apiPath;
                var responseMessage = await httpClient.GetAsync(url);
                var count = 0;
                if (!responseMessage.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Dashboard Stats Update - returns error code: " + responseMessage.StatusCode);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("UpdateDashboardStats {0}", ex.Message);
            }
        }

        private async Task<string> GetAccessToken()
        {
            var accessToken = await _authenticationService.GetAccessTokenAsync(
                new IdentityClientConfiguration(
                    _configuration["IdentityClients:Default:Authority"]!,
                    _configuration["IdentityClients:Default:Scope"]!,
                    _configuration["IdentityClients:Default:ClientId"]!,
                    _configuration["IdentityClients:Default:ClientSecret"]!,
                    grantType: OidcConstants.GrantTypes.ClientCredentials,
                    requireHttps: false
                )
            );
            return accessToken;
        }
    }
}
