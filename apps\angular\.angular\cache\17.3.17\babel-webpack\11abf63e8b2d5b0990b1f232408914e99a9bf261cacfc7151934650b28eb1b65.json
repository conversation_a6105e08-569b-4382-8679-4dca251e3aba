{"ast": null, "code": "import { utcToZonedTime, format } from 'date-fns-tz';\nexport class DateHelper {\n  static formatEstUtcDate(dateStr, formatStr) {\n    if (!dateStr) return '';\n    if (dateStr.length === 10) return dateStr; //avoid this method called twice\n    const dt = utcToZonedTime(dateStr, 'America/New_York');\n    return format(dt, formatStr);\n  }\n  /**\n   * Converts UTC date string to user's browser local time and formats it\n   * @param dateStr UTC date string (ISO format)\n   * @param formatStr Format string for date-fns (default: 'dd/MM/yyyy')\n   * @returns Formatted date string in user's local timezone\n   */\n  static formatUtcToLocalDate(dateStr, formatStr = 'dd/MM/yyyy') {\n    if (!dateStr) return '';\n    if (dateStr.length === 10) return dateStr; // avoid this method called twice for date-only strings\n    // Get user's browser timezone\n    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n    // Convert UTC to user's local timezone\n    const localDateTime = utcToZonedTime(dateStr, userTimezone);\n    // Format and return\n    return format(localDateTime, formatStr, {\n      timeZone: userTimezone\n    });\n  }\n  /**\n   * Converts UTC date string to user's browser local Date object\n   * @param dateStr UTC date string (ISO format)\n   * @returns Date object in user's local timezone\n   */\n  static convertUtcToLocalDate(dateStr) {\n    if (!dateStr) return null;\n    // Get user's browser timezone\n    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n    // Convert UTC to user's local timezone and return as Date object\n    return utcToZonedTime(dateStr, userTimezone);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}