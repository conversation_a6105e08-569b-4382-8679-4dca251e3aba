﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Bdo.Ess.EconomicSubstanceService.DbMigrations;
using Bdo.Ess.EconomicSubstanceService.MongoDb;
using Bdo.Ess.Shared.Hosting.Microservices;
using Bdo.Ess.Shared.Hosting.AspNetCore;
using Prometheus;
using Volo.Abp;
using Volo.Abp.Modularity;
using Volo.Abp.AspNetCore.Mvc;
using MongoDB.Bson.Serialization;
using Bdo.Ess.EconomicSubstanceService.Templates;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Bdo.Ess.Shared.Hosting.HealthChecks;
using Serilog;
using Bdo.Ess.Shared.Hosting.AspNetCore.HealthChecks;
using Bdo.Ess.LookupService;
using Volo.Abp.Http.Client.IdentityModel;
using Volo.Abp.Http.Client;
using Volo.Abp.AspNetCore.SignalR;
using Bdo.Ess.EconomicSubstanceService.Extensions;
using Bdo.Ess.EconomicSubstanceService.DeclarationImports;
using Bdo.Ess.IdentityService.EntityFrameworkCore;
using Bdo.Ess.IdentityService;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Volo.Abp.Emailing;
using Bdo.Ess.Shared.HttpApi.Audit;
using Bdo.Ess.Shared.HttpApi;
using Volo.Abp.Security.Claims;
using Azure.Identity;
using Azure.Storage.Blobs;
using Bdo.Ess.Shared.Constants.Encryption;
using Microsoft.AspNetCore.DataProtection;

namespace Bdo.Ess.EconomicSubstanceService;

[DependsOn(
    typeof(EssSharedHostingMicroservicesModule),
    typeof(EconomicSubstanceServiceApplicationModule),
    typeof(EconomicSubstanceServiceHttpApiModule),
    typeof(EconomicSubstanceServiceMongoDbModule),
    typeof(LookupServiceApplicationContractsModule),
    typeof(AbpAspNetCoreSignalRModule),
    typeof(AbpHttpClientIdentityModelModule),
    typeof(IdentityServiceEntityFrameworkCoreModule),
    typeof(IdentityServiceApplicationContractsModule),
    typeof(ESSSharedHttpApiModule)
    )]
public class EconomicSubstanceServiceHttpApiHostModule : AbpModule
{
	public override void PreConfigureServices(ServiceConfigurationContext context)
	{
		PreConfigure<AbpHttpClientBuilderOptions>(options =>
		{
			options.ProxyClientActions.Add((remoteServiceName, clientBuilder, client) =>
			{
				client.Timeout = TimeSpan.FromMinutes(55);
			});
		});
	}
	public override void ConfigureServices(ServiceConfigurationContext context)
    {
        //You can disable this setting in production to avoid any potential security risks.
        Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
        
        // Enable if you need these
        // var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();
                
        JwtBearerConfigurationHelper.Configure(context, "EconomicSubstanceService");
        /* Version 7
        SwaggerConfigurationHelper.ConfigureWithAuth(
            context: context,
            authority: configuration["AuthServer:Authority"],
            scopes: new
                Dictionary<string, string> 
                {
                    {"EconomicSubstanceService", "EconomicSubstanceService API"}
                },
            apiTitle: "EconomicSubstanceService API"
        );
        */
        SwaggerConfigurationHelper.ConfigureWithOidc(
            context: context,
            authority: configuration["AuthServer:Authority"]!,
            scopes: new[] { "EconomicSubstanceService" },
            flows: new[] { "authorization_code" },
            discoveryEndpoint: configuration["AuthServer:MetadataAddress"],
            apiTitle: "EconomicSubstanceService API"
        );
        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = false;
        });
        //
        // Call custom register method to register SignalR Hub service.
        //
        context.Services.AddAppSignalR();

        context.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(
                        configuration["App:CorsOrigins"]?
                            .Split(",", StringSplitOptions.RemoveEmptyEntries)
                            .Select(o => o.Trim().RemovePostFix("/"))
                            .ToArray()
                    )
                    .WithAbpExposedHeaders()
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });

#if DEBUG
        // Note: NullEmailSender works for disable email sending feature in debug mode. Affecting user login code validation with email.
        context.Services.Replace(ServiceDescriptor.Singleton<IEmailSender, NullEmailSender>());
#endif

        if (!BsonClassMap.IsClassMapRegistered(typeof(SurveyDto)))
        {
            BsonClassMap.RegisterClassMap<SurveyDto>();
        }

        EncryptionConfigurationHelper.ConfigureDataProtection(context, configuration);

        //Health Checks
        context.Services.AddBdoHealthChecks(new[] {
            new ConnectionProperties {
                ConnectionString = configuration["ConnectionStrings:AdministrationService"] ?? "",
                DatabaseType = DatabaseType.SQLServer
            },
            new ConnectionProperties {
                ConnectionString = configuration["ConnectionStrings:SaasService"] ?? "",
                DatabaseType = DatabaseType.SQLServer
            },
            new ConnectionProperties{
                ConnectionString = configuration["ConnectionStrings:EconomicSubstanceService"] ?? "",
                DatabaseName = "Template",
                DatabaseType = DatabaseType.MongoDb
            }}, new[] { "Economic Substance Service API" });
                
        PreConfigure<AbpHttpClientBuilderOptions>(options =>
        {
            options.ProxyClientActions.Add((remoteServiceName, clientBuilder, client) =>
            {
                client.Timeout = TimeSpan.FromMinutes(60);
            });
        });
	}

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseCorrelationId();
        app.UseAbpRequestLocalization();
        app.UseStaticFiles();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        app.UseCors();
        app.UseAuthentication();
        app.UseAbpClaimsMap();
        app.UseMultiTenancy();
        app.UseUnitOfWork();
        //app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "EconomicSubstanceService API");
            options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
        });
        app.UseAbpSerilogEnrichers();
        app.UseAuditing();
        app.UseConfiguredEndpoints(endpoints => endpoints.MapMetrics());

        app.UseEndpoints(endpoints =>
        {
            //
            // Note: Important line of code for SignalR Hub service setup.
            // Don't modify it.
            //
            endpoints.MapHub<NotificationHub>("/signalr-hubs/notification");

            endpoints.MapHealthChecks(HealthCheckHelper.LiveUrl, new HealthCheckOptions
            {
                Predicate = h => h.Name == HealthCheckHelper.LivenessName
            });
            endpoints.MapHealthChecks(HealthCheckHelper.ReadyUrl, new HealthCheckOptions
            {
                Predicate = (check) => check.Tags.Contains(HealthCheckHelper.ReadyTag)
            });
        });
    }

    public async override Task OnPostApplicationInitializationAsync(ApplicationInitializationContext context)
    {
        using (var scope = context.ServiceProvider.CreateScope())
        {
            await scope.ServiceProvider
                .GetRequiredService<EconomicSubstanceServiceDatabaseMigrationChecker>()
                .CheckAndApplyDatabaseMigrationsAsync();
                
        }
    }
}
