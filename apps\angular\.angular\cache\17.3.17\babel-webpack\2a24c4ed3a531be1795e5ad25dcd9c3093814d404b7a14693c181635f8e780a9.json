{"ast": null, "code": "import { InformationExchangeStatus } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';\nimport { AppComponentBase } from '../../../../app-component-base';\nimport { DateHelper } from '../../../../shared/utils/date-helper';\nimport { BdoTableColumnType, BdoTableData } from '../../../../shared/components/bdo-table/bdo-table.model';\nimport { ExchangeReasonDic, InformationExchangeStatusDic, CTSUploadStatusDic, CTSUploadExchangeReasonDic } from '../../../../shared/constants';\nimport Swal from 'sweetalert2';\nimport { InformationExchangeHistoryComponent } from '../information-exchange-history/information-exchange-history.component';\nimport { UpdateCaCertificateDialogComponent } from '../update-ca-certificate-dialog/update-ca-certificate-dialog.component';\nimport { ViewAssociatedExchangeRecordsComponent } from '../view-associated-exchange-records/view-associated-exchange-records.component';\nimport { UploadHistoricalXmlDialogComponent } from '../upload-historical-xml-dialog/upload-historical-xml-dialog.component';\nimport { DecryptDataPacketDialogComponent } from '../decrypt-data-packet-dialog/decrypt-data-packet-dialog.component';\nimport { RegeneratePacketDialogComponent } from '../regenerate-packet-dialog/regenerate-packet-dialog.component';\nimport { ViewCommentDialogComponent } from '../view-comment-dialog/view-comment-dialog.component';\nimport { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/enums/ctsupload-status.enum';\nimport { UpdateCtsSettingDialogComponent } from '../update-cts-setting-dialog/update-cts-setting-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/controllers\";\nimport * as i3 from \"../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges\";\nimport * as i4 from \"@abp/ng.core\";\nimport * as i5 from \"proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/certificate.service\";\nimport * as i8 from \"../../../../shared/services/upload-file.service\";\nimport * as i9 from \"@abp/ng.theme.shared\";\nimport * as i10 from \"proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration\";\nimport * as i11 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-request/cts-package-request.service\";\nimport * as i12 from \"@app/shared/services/sweetalert.service\";\nimport * as i13 from \"proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"@angular/material/input\";\nimport * as i16 from \"@angular/material/form-field\";\nimport * as i17 from \"@angular/material/icon\";\nimport * as i18 from \"@angular/material/button\";\nimport * as i19 from \"@angular/material/select\";\nimport * as i20 from \"@angular/material/core\";\nimport * as i21 from \"@angular/material/tabs\";\nimport * as i22 from \"../../../../shared/components/bdo-table/bdo-table.component\";\nimport * as i23 from \"@angular/common\";\nconst _c0 = () => [10, 20, 50, 100];\nfunction InformationExchangeMainComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"CA Certificate expires at \", i0.ɵɵpipeBind2(2, 1, ctx_r0.certificateExpirationDate, \"dd/MM/yyyy\"), \"\");\n  }\n}\nfunction InformationExchangeMainComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openUpdateCtsSettingDialog());\n    });\n    i0.ɵɵtext(1, \" Update CTS Settings \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openUpdateCaCertificateDialog());\n    });\n    i0.ɵɵtext(1, \" Update CA Certificate \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(0));\n    });\n    i0.ɵɵtext(1, \" Non-compliance XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(1));\n    });\n    i0.ɵɵtext(1, \" High Risk IP XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(2));\n    });\n    i0.ɵɵtext(1, \" Non-resident XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.GenerateXMlByType(3));\n    });\n    i0.ɵɵtext(1, \" Other Cases XML \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r8, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r9.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r9.description, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_mat_tab_38_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.openDecryptDialog());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"lock_open\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Decrypt Received Data Packet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_mat_tab_38_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.openUploadHistoricalXmlDialog());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Upload Historical XML\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_mat_tab_38_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.openCtsUploadDialog());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" CTS Upload\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_mat_tab_38_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.refreshAllTransmissionStatus());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Refresh CTS Transmission Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_mat_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r15);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r15, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_mat_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r16.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r16.description, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_mat_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r17.code2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r17.name, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_mat_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", element_r18.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r18.description, \" \");\n  }\n}\nfunction InformationExchangeMainComponent_mat_tab_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab\", 25)(1, \"div\", 26)(2, \"div\", 3);\n    i0.ɵɵelement(3, \"bdo-table\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 5)(5, \"div\", 6);\n    i0.ɵɵtemplate(6, InformationExchangeMainComponent_mat_tab_38_button_6_Template, 4, 0, \"button\", 8)(7, InformationExchangeMainComponent_mat_tab_38_button_7_Template, 4, 0, \"button\", 8)(8, InformationExchangeMainComponent_mat_tab_38_button_8_Template, 4, 0, \"button\", 8)(9, InformationExchangeMainComponent_mat_tab_38_button_9_Template, 4, 0, \"button\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 15)(12, \"mat-label\", 28);\n    i0.ɵɵtext(13, \"Financial Period End\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 12)(15, \"mat-select\", 13);\n    i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.ctsUploadSelectedYear, $event) || (ctx_r0.ctsUploadSelectedYear = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadYearChange($event));\n    });\n    i0.ɵɵtemplate(16, InformationExchangeMainComponent_mat_tab_38_mat_option_16_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 15)(18, \"mat-label\", 28);\n    i0.ɵɵtext(19, \"Exchange Reason\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"mat-form-field\", 12)(21, \"mat-select\", 29);\n    i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectExchangeReason, $event) || (ctx_r0.selectExchangeReason = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadExchangeReasonChange($event));\n    });\n    i0.ɵɵtemplate(22, InformationExchangeMainComponent_mat_tab_38_mat_option_22_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 15)(24, \"mat-label\", 28);\n    i0.ɵɵtext(25, \"Receiving Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-form-field\", 12)(27, \"mat-select\", 30);\n    i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectReceivingCountry, $event) || (ctx_r0.selectReceivingCountry = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadReceivingCountryChange($event));\n    });\n    i0.ɵɵtemplate(28, InformationExchangeMainComponent_mat_tab_38_mat_option_28_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 15)(30, \"mat-label\", 28);\n    i0.ɵɵtext(31, \"CTS Upload Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-form-field\", 12)(33, \"mat-select\", 31);\n    i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_valueChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectCtsUploadStatus, $event) || (ctx_r0.selectCtsUploadStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_mat_tab_38_Template_mat_select_selectionChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadStatusChange($event));\n    });\n    i0.ɵɵtemplate(34, InformationExchangeMainComponent_mat_tab_38_mat_option_34_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(35, \"div\", 2)(36, \"div\", 3)(37, \"bdo-table\", 32);\n    i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onLazyLoad_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadLazyLoadEvent($event));\n    })(\"onLinkClick\", function InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onLinkClick_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadLinkClick($event));\n    })(\"onActionClick\", function InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onActionClick_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCtsUploadActionClick($event));\n    })(\"onCheckboxClick\", function InformationExchangeMainComponent_mat_tab_38_Template_bdo_table_onCheckboxClick_37_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCheckboxClick($event));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", ctx_r0.ctsDashboardTableId)(\"columns\", ctx_r0.ctsDashboardColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx_r0.currentPageIndexS)(\"pageSize\", ctx_r0.PageSizeS)(\"isVirtualScroll\", false)(\"hidePagination\", true)(\"rowSelectable\", false)(\"lazyLoad\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showDecryptReceivedDataPacket);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showUploadHistoricalXml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showCTSUpload);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showRefreshStatus);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r0.ctsUploadSelectedYear);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.year);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r0.selectExchangeReason);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.ctsUploadExchangeReasonDic);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r0.selectReceivingCountry);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.countries);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r0.selectCtsUploadStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.ctsUploadStatusDic);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", ctx_r0.ctsUploadTableId)(\"columns\", ctx_r0.ctsUploadColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx_r0.ctsUploadCurrentPage)(\"pageSize\", ctx_r0.ctsUploadPageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(31, _c0))(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n  }\n}\nexport let InformationExchangeMainComponent = /*#__PURE__*/(() => {\n  class InformationExchangeMainComponent extends AppComponentBase {\n    constructor(injector, router, informationExchangeService, informationExchangeDetailService, permissionService, dashboardService, dialog, certificateService, fileUploadService, toasterService, countryService, ctsPackageRequestService, sweetAlert, bahamasCtsSettingService) {\n      super(injector);\n      this.router = router;\n      this.informationExchangeService = informationExchangeService;\n      this.informationExchangeDetailService = informationExchangeDetailService;\n      this.permissionService = permissionService;\n      this.dashboardService = dashboardService;\n      this.dialog = dialog;\n      this.certificateService = certificateService;\n      this.fileUploadService = fileUploadService;\n      this.toasterService = toasterService;\n      this.countryService = countryService;\n      this.ctsPackageRequestService = ctsPackageRequestService;\n      this.sweetAlert = sweetAlert;\n      this.bahamasCtsSettingService = bahamasCtsSettingService;\n      this.input = {\n        maxResultCount: 10,\n        skipCount: 0,\n        sorting: 'ExchangeReason asc',\n        informationExchangeStatus: InformationExchangeStatus.None,\n        entityName: '',\n        year: ''\n      };\n      this.TableId = 'information_ex-results';\n      /* Work for pagination. Default value = 0, it is rendering first page by default. */\n      this.currentPageIndex = 0;\n      /** It is string year number array. */\n      this.year = [];\n      /** Selected year from Financial Period End Years dropdown, default is current year. */\n      this.selectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default.\n      this.exchangeResultColumns = [{\n        columnId: \"exchangeReason\" /* InformationExchangeTableColumns.EXCHANGE_REASON */,\n        type: BdoTableColumnType.String,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: true,\n        columnName: 'Exchange Reason'\n      }, {\n        columnId: \"raCode\" /* InformationExchangeTableColumns.RA_CODE */,\n        type: BdoTableColumnType.String,\n        minWidth: 60,\n        isSortable: true,\n        columnName: 'RA Name'\n      }, {\n        columnId: \"entityName\" /* InformationExchangeTableColumns.ENTITY_NAME */,\n        type: BdoTableColumnType.String,\n        minWidth: 100,\n        isSortable: true,\n        columnName: 'Entity Name'\n      }, {\n        columnId: \"incopFormationNo\" /* InformationExchangeTableColumns.INCROP_NUMBER */,\n        type: BdoTableColumnType.String,\n        minWidth: 60,\n        isSortable: true,\n        columnName: 'Incop#/Formation#'\n      }, {\n        columnId: \"financialPeriod\" /* InformationExchangeTableColumns.FINANCIAL_PERIOD */,\n        type: BdoTableColumnType.Date,\n        minWidth: 60,\n        isSortable: true,\n        columnName: 'Financial Period End Date'\n      }, {\n        columnId: \"dueDate\" /* InformationExchangeTableColumns.DUE_DATE */,\n        type: BdoTableColumnType.Date,\n        minWidth: 60,\n        isSortable: true,\n        columnName: 'Due Date'\n      }, {\n        columnId: \"informationExchangeStatus\" /* InformationExchangeTableColumns.INFORMATIONEXCH_STATUS */,\n        type: BdoTableColumnType.String,\n        minWidth: 60,\n        isSortable: true,\n        columnName: 'Information Exchange Status'\n      }, {\n        columnId: \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */,\n        type: BdoTableColumnType.Link,\n        minWidth: 60,\n        columnName: 'View Declaration'\n      }, {\n        columnId: \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */,\n        type: BdoTableColumnType.Link,\n        minWidth: 60,\n        columnName: 'XML Data'\n      }, {\n        columnId: \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */,\n        type: BdoTableColumnType.Link,\n        minWidth: 60,\n        columnName: 'View History'\n      }];\n      /** Page size setting for \"TableId\" grid */\n      this.PageSize = 10;\n      this.exchangeInformationResultRecords = [];\n      this.selectReportStatus = InformationExchangeStatus.None;\n      this.informationExchangedDic = InformationExchangeStatusDic;\n      this.TableIdS = 'information_ex_summary';\n      this.currentPageIndexS = 0;\n      this.summaryExchangeColumns = [{\n        columnId: \"totalReport\" /* ExchangeSummaryTableColumns.TOTAL_REPORT */,\n        type: BdoTableColumnType.Number,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: 'Total # of Reports'\n      }, {\n        columnId: \"totalReportSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT */,\n        type: BdoTableColumnType.Number,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: 'Total # of Reports XML Generated'\n      }, {\n        columnId: \"totalReportReady\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_READY */,\n        type: BdoTableColumnType.Number,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: 'Total # of Reports Ready for Exchange'\n      }, {\n        columnId: \"totalReportReview\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW */,\n        type: BdoTableColumnType.Number,\n        minWidth: 200,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: 'Total # of Reports for Review<br>(Include: Not required, Waiting for Review/Appeal)'\n      }, {\n        columnId: \"totalReportNotSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT */,\n        type: BdoTableColumnType.Number,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: 'Total # of Reports Not Started'\n      }];\n      /** Page Size setting for \"TableIdS\" grid. Note: It is not the same as PageSize variable. Don't confuse. */\n      this.PageSizeS = 10;\n      this.totalRecords = 0;\n      /** Note: Only logon user with permission \"Generate XML\"\n       * is able to see the \"Non-compliance XML\",\"High Risk IP XML\",\"Non-resident XML\" buttons. */\n      this.showButton = true;\n      this.showOtherCase = true;\n      /* Default current year value as string. */\n      this.currnetYear = new Date().getFullYear().toString();\n      this.certificateExpirationDate = null;\n      this.bahamasCtsSetting = null;\n      this.isCaSystemAdmin = false;\n      // Dashboard columns for CTS Upload & Transmission\n      this.ctsDashboardColumns = [{\n        columnId: 'totalNotUploaded',\n        type: BdoTableColumnType.Number,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: '# of Packets Do Not Upload'\n      }, {\n        columnId: 'totalReadyForUpload',\n        type: BdoTableColumnType.Number,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: '# of Packets Ready For Upload'\n      }, {\n        columnId: 'totalFailedUpload',\n        type: BdoTableColumnType.Number,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: '# of Packets Failed in Upload'\n      }, {\n        columnId: 'totalUploadedToCTS',\n        type: BdoTableColumnType.Number,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: '# of Packets Uploaded to CTS'\n      }, {\n        columnId: 'totalNotEnrolled',\n        type: BdoTableColumnType.Number,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: false,\n        columnName: '# of Packets Receiving Country Not Enrolled'\n      }];\n      // Dashboard data for CTS Upload & Transmission\n      this.ctsDashboardList = [{\n        id: 1,\n        totalNotUploaded: 0,\n        totalReadyForUpload: 1,\n        totalFailedUpload: 1,\n        totalUploadedToCTS: 2,\n        totalNotEnrolled: 2\n      }];\n      // Grid columns for CTS Upload & Transmission\n      this.ctsUploadColumns = [{\n        columnId: 'exchangeReason',\n        type: BdoTableColumnType.String,\n        minWidth: 120,\n        frozenLeft: true,\n        isSortable: true,\n        columnName: 'Exchange Reason'\n      }, {\n        columnId: 'dataPacket',\n        type: BdoTableColumnType.Link,\n        minWidth: 120,\n        isSortable: false,\n        columnName: 'Data Packet'\n      }, {\n        columnId: 'fileCreationDate',\n        type: BdoTableColumnType.Date,\n        minWidth: 120,\n        isSortable: true,\n        columnName: 'File Creation Date'\n      }, {\n        columnId: 'receivingCountry',\n        type: BdoTableColumnType.String,\n        minWidth: 120,\n        isSortable: false,\n        columnName: 'Receiving Country'\n      }, {\n        columnId: 'ctsUploadStatus',\n        type: BdoTableColumnType.String,\n        minWidth: 120,\n        isSortable: false,\n        columnName: 'CTS Upload Status'\n      }, {\n        columnId: 'uploadedAt',\n        type: BdoTableColumnType.DateTime,\n        minWidth: 120,\n        isSortable: true,\n        columnName: 'Uploaded At'\n      }, {\n        columnId: 'ctsTransmissionStatus',\n        type: BdoTableColumnType.String,\n        minWidth: 120,\n        isSortable: false,\n        columnName: 'CTS Transmission Status'\n      }, {\n        columnId: 'viewExchangeRecords',\n        type: BdoTableColumnType.Link,\n        minWidth: 60,\n        isSortable: false,\n        columnName: 'View Exchange Records'\n      }, {\n        columnId: 'viewComments',\n        type: BdoTableColumnType.Link,\n        minWidth: 60,\n        isSortable: false,\n        columnName: 'View Comments'\n      }, {\n        columnId: 'regeneratePacket',\n        type: BdoTableColumnType.SingleActionButton,\n        minWidth: 60,\n        isSortable: false,\n        columnName: 'Regenerate Packet'\n      }, {\n        columnId: 'ctsUpload',\n        type: BdoTableColumnType.SingleActionButton,\n        minWidth: 60,\n        isSortable: false,\n        columnName: 'CTS Upload'\n      }, {\n        columnId: 'excludeFromCtsUpload',\n        type: BdoTableColumnType.Checkbox,\n        minWidth: 60,\n        isSortable: false,\n        columnName: 'Exclude From CTS Upload'\n      }];\n      // CTS Upload & Transmission Dashboard\n      this.ctsUploadExchangeReasonDic = CTSUploadExchangeReasonDic;\n      this.ctsUploadStatusDic = CTSUploadStatusDic;\n      this.ctsUploadSelectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default. \n      this.ctsUploadResultRecords = [];\n      this.selectExchangeReason = -1;\n      this.selectCtsUploadStatus = CTSUploadStatus.NotStarted;\n      this.selectReceivingCountry = '';\n      // Table IDs and page settings\n      this.ctsDashboardTableId = 'cts_dashboard';\n      this.ctsUploadTableId = 'cts_upload_grid';\n      this.ctsUploadPageSize = 10;\n      this.ctsUploadCurrentPage = 0;\n      this.ctsUploadTotalRecords = 0;\n      this.ctsUploadInput = {\n        maxResultCount: 10,\n        skipCount: 0,\n        sorting: 'ExchangeReason asc',\n        ctsUploadStatus: null,\n        exchangeReason: null,\n        financialEndYear: '',\n        receivingCountry: ''\n      };\n      this.showBahamasSettings = false;\n      this.showDataPacketDashboard = false;\n      this.showUpdateCACertificate = false;\n      this.showCTSUpload = false;\n      this.showRefreshStatus = false;\n      this.showRegenerateDataPacket = false;\n      this.showDecryptReceivedDataPacket = false;\n      this.showUploadHistoricalXml = false;\n      this.summaryExchangeList = [{\n        id: 1,\n        totalNoReport: 100,\n        totalNoReportSent: 10,\n        totalNoReportRExchange: 5,\n        totalNoReportRReview: 2,\n        totalNoReportNotSent: 5\n      }];\n    }\n    ngOnInit() {\n      this.getFiscalYears().subscribe(response => {\n        if (response && response.length > 0) {\n          this.year = [];\n          response.forEach(element => {\n            this.year.push(element.toString());\n          });\n        }\n        ;\n      });\n      if (localStorage.getItem('selectedYear')) {\n        this.selectedYear = localStorage.getItem('selectedYear') ?? this.currnetYear;\n      }\n      if (localStorage.getItem('selectReportStatus')) {\n        this.selectReportStatus = Number(localStorage.getItem('selectReportStatus'));\n      }\n      this.informationExchangeDetailService.standardMonitoringFromYear().subscribe(response => {\n        this.standardMonitoringYear = response;\n        this.IsShowOtherCase(this.selectedYear);\n      });\n      this.onLazyLoadEvent(undefined);\n      this.onLazyLoadEventS(undefined);\n      this.showButton = this.checkUserPermission();\n      // CTS Upload & Transmission Dashboard\n      // Check CA System Admin role\n      const currentUser = this.configState.getOne('currentUser');\n      this.isCaSystemAdmin = !!currentUser?.roles?.includes('CA System Admin');\n      if (localStorage.getItem('ctsUploadSelectedYear')) {\n        this.ctsUploadSelectedYear = localStorage.getItem('ctsUploadSelectedYear') ?? this.currnetYear;\n      }\n      if (localStorage.getItem('selectExchangeReason')) {\n        this.selectExchangeReason = Number(localStorage.getItem('selectExchangeReason'));\n      }\n      if (localStorage.getItem('selectCtsUploadStatus')) {\n        this.selectCtsUploadStatus = Number(localStorage.getItem('selectCtsUploadStatus'));\n      }\n      if (localStorage.getItem('selectReceivingCountry')) {\n        this.selectReceivingCountry = localStorage.getItem('selectReceivingCountry');\n      }\n      this.showBahamasSettings = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.BahamasCtsSetting\" /* Permissions.BAHAMAS_CTS_SETTING */);\n      this.showDataPacketDashboard = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.DataPacketDashboard\" /* Permissions.DATA_PACKET_DASHBOARD */);\n      this.showUpdateCACertificate = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.UpdateCACertificate\" /* Permissions.UPDATE_CA_CERTIFICATE */);\n      this.showCTSUpload = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.CTSUpload\" /* Permissions.CTS_UPLOAD */);\n      this.showRefreshStatus = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.RefreshStatus\" /* Permissions.REFRESH_STATUS */);\n      this.showRegenerateDataPacket = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.RegenerateDataPacket\" /* Permissions.REGENERATE_DATA_PACKET */);\n      this.showDecryptReceivedDataPacket = this.permissionService.getGrantedPolicy(\"CtsIntegrationService.DecryptReceivedDataPacket\" /* Permissions.DECRYPT_RECEIVED_DATA_PACKET */);\n      this.showUploadHistoricalXml = this.permissionService.getGrantedPolicy(\"CtsIntegration.UploadHistoricalXml\" /* Permissions.UPLOAD_HISTORICAL_XML */);\n      this.getCountries();\n      if (this.showUpdateCACertificate) {\n        this.getBahamasCertificateInfo();\n      }\n      if (this.showBahamasSettings) {\n        this.getBahamasCtsSettingInfo();\n      }\n      if (this.showDataPacketDashboard) {\n        this.onCtsUploadLazyLoadEvent(undefined);\n        this.onCtsDashboardLazyLoadEvent(undefined);\n      }\n    }\n    IsShowOtherCase(year) {\n      const selectedYearAsInt = parseInt(year, 10);\n      const standardMonitoringYearAsInt = parseInt(this.standardMonitoringYear, 10);\n      this.showOtherCase = selectedYearAsInt <= standardMonitoringYearAsInt ? true : false;\n    }\n    /** Lazy load event works for \"TableIds\" grid only. */\n    onLazyLoadEventS(event) {\n      this.currentPageIndexS = 0;\n      this.informationExchangeService.getSummaryByYearByYear(this.selectedYear).subscribe(response => {\n        this.summaryExchangeList = response;\n        setTimeout(() => {\n          this.setTableDataS();\n        }, 200);\n      });\n    }\n    /** Lazy load event works for grid \"TableId\" only. */\n    onLazyLoadEvent(event) {\n      if (event) {\n        if (this.PageSize === (event.pageSize ?? 10)) {\n          this.currentPageIndex = event.pageNumber ?? 0;\n        } else {\n          //\n          // if Page size got changed through pagination control,\n          // need to reset current page index to 0.\n          //\n          this.PageSize = event.pageSize ?? 10;\n          this.currentPageIndex = 0;\n        }\n        this.input.skipCount = (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);\n        this.input.maxResultCount = this.PageSize ?? 10;\n        if (event.isAscending === false) {\n          this.input.sorting = `${event.sortField} desc`;\n        } else {\n          this.input.sorting = `${event.sortField} asc`;\n        }\n      } else {\n        this.currentPageIndex = 0;\n        this.PageSize = 10;\n        this.input.informationExchangeStatus = this.selectReportStatus;\n        this.input.year = this.selectedYear;\n        this.input.skipCount = 0;\n        this.input.maxResultCount = this.PageSize;\n      }\n      this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n        if (response) {\n          this.totalRecords = response.totalCount;\n          this.exchangeInformationResultRecords = response.items;\n        } else {\n          this.totalRecords = 0;\n          this.exchangeInformationResultRecords = [];\n        }\n        setTimeout(() => {\n          this.setTableData();\n        }, 200);\n      });\n    }\n    setTableDataS() {\n      const tableData = new BdoTableData();\n      tableData.resetToFirstPage = false;\n      tableData.tableId = this.TableIdS;\n      tableData.totalRecords = 1;\n      tableData.data = this.summaryExchangeList.map(x => {\n        return {\n          id: x.id,\n          rawData: x,\n          cells: [{\n            columnId: \"totalReport\" /* ExchangeSummaryTableColumns.TOTAL_REPORT */,\n            value: x.totalNoofReports\n          }, {\n            columnId: \"totalReportSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT */,\n            value: x.totalNoofExchangedReports\n          }, {\n            columnId: \"totalReportReady\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_READY */,\n            value: x.totalNoofReadyExchangedReports\n          }, {\n            columnId: \"totalReportReview\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW */,\n            value: x.totalNoofReviewReports\n          }, {\n            columnId: \"totalReportNotSent\" /* ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT */,\n            value: x.totalNotSentReports\n          }]\n        };\n      });\n      setTimeout(() => {\n        this.tableService.setGridData(tableData);\n      }, 10);\n    }\n    getExchangeReasonDescription(input) {\n      const foundStatus = ExchangeReasonDic.find(status => status.value === input);\n      if (foundStatus) return foundStatus.description;\n      return '';\n    }\n    getInformationExchangeStatusDescription(input) {\n      const foundStatus = InformationExchangeStatusDic.find(status => status.value === input);\n      if (foundStatus) return foundStatus.description;\n      return '';\n    }\n    base64ToUint8Array(x) {\n      const raw = atob(x);\n      var rawLength = raw.length;\n      var array = new Uint8Array(new ArrayBuffer(rawLength));\n      for (let i = 0; i < rawLength; i++) {\n        array[i] = raw.charCodeAt(i);\n      }\n      return array;\n    }\n    downloadFile(content, name) {\n      var file = new Blob([this.base64ToUint8Array(content)]);\n      var fileURL = window.URL.createObjectURL(file);\n      var element = document.createElement('a');\n      document.body.appendChild(element);\n      element.style.display = 'none';\n      element.href = fileURL;\n      element.download = name;\n      element.click();\n      element.remove();\n    }\n    GenerateXMlByType(exchangeType) {\n      this.informationExchangeService.getXMLFilesFilterByExchangeTypeByReasonAndYear(exchangeType, this.selectedYear).subscribe(result => {\n        this.onLazyLoadEvent(undefined);\n        if (result.fileName != '') {\n          this.downloadFile(result.content.toString(), result.fileName);\n        } else Swal.fire({\n          icon: 'info',\n          title: 'XML Import',\n          text: 'No data to export.',\n          allowOutsideClick: false\n        });\n      });\n    }\n    tabChanged(event) {\n      if (event.index === 1) {\n        if (this.showDataPacketDashboard) {\n          this.onCtsUploadLazyLoadEvent(undefined);\n          this.onCtsDashboardLazyLoadEvent(undefined);\n        }\n      }\n    }\n    setTableData() {\n      const tableData = new BdoTableData();\n      tableData.resetToFirstPage = false;\n      tableData.tableId = this.TableId;\n      tableData.totalRecords = this.totalRecords;\n      tableData.data = this.exchangeInformationResultRecords.map(x => {\n        return {\n          id: x.id,\n          rawData: x,\n          cells: [{\n            columnId: \"exchangeReason\" /* InformationExchangeTableColumns.EXCHANGE_REASON */,\n            value: this.getExchangeReasonDescription(x.exchangeReason)\n          }, {\n            columnId: \"raCode\" /* InformationExchangeTableColumns.RA_CODE */,\n            value: x.raCode\n          }, {\n            columnId: \"entityName\" /* InformationExchangeTableColumns.ENTITY_NAME */,\n            value: x.entityName\n          }, {\n            columnId: \"incopFormationNo\" /* InformationExchangeTableColumns.INCROP_NUMBER */,\n            value: x.companyFormationNumber\n          }, {\n            columnId: \"financialPeriod\" /* InformationExchangeTableColumns.FINANCIAL_PERIOD */,\n            value: x.fiscalEndDate\n          }, {\n            columnId: \"dueDate\" /* InformationExchangeTableColumns.DUE_DATE */,\n            value: x.dueDate\n          }, {\n            columnId: \"informationExchangeStatus\" /* InformationExchangeTableColumns.INFORMATIONEXCH_STATUS */,\n            value: this.getInformationExchangeStatusDescription(x.informationExchangeStatus)\n          }, {\n            columnId: \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */,\n\n            /** If the underneath data \"IsMigrated\" flag is true, then disable the link, otherwise enable the link to view declaration page */\n            value: x.isMigrated === false ? 'view' : ''\n          }, {\n            columnId: \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */,\n            value: 'XML Data'\n          }, {\n            columnId: \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */,\n            value: x.hasHistoryRecord ? 'View History' : ''\n          }]\n        };\n      });\n      setTimeout(() => {\n        this.tableService.setGridData(tableData);\n      }, 100);\n    }\n    onYearChange(ob) {\n      this.selectedYear = ob.value;\n      this.IsShowOtherCase(this.selectedYear);\n      // Keep the selected Year in local storage.\n      localStorage.setItem('selectedYear', ob.value);\n      this.input.informationExchangeStatus = this.selectReportStatus;\n      this.input.year = this.selectedYear;\n      this.input.entityName = this.searchEntityName;\n      this.input.skipCount = 0;\n      this.input.maxResultCount = this.PageSize;\n      this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n        this.totalRecords = response.totalCount;\n        this.exchangeInformationResultRecords = response.items;\n        setTimeout(() => {\n          this.setTableData();\n        }, 200);\n      });\n      this.onLazyLoadEventS(undefined);\n    }\n    onSearch() {\n      this.input.informationExchangeStatus = this.selectReportStatus;\n      this.input.year = this.selectedYear;\n      this.input.entityName = this.searchEntityName;\n      this.input.skipCount = 0;\n      this.input.maxResultCount = this.PageSize;\n      this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n        this.totalRecords = response.totalCount;\n        this.exchangeInformationResultRecords = response.items;\n        setTimeout(() => {\n          this.setTableData();\n        }, 200);\n      });\n      this.onLazyLoadEventS(undefined);\n    }\n    onReportChange(ob) {\n      this.selectReportStatus = Number(ob.value);\n      this.input.informationExchangeStatus = this.selectReportStatus;\n      this.input.year = this.selectedYear;\n      this.input.entityName = this.searchEntityName;\n      this.input.skipCount = 0;\n      this.input.maxResultCount = this.PageSize;\n      // Keep the selected Report Status in local storage.\n      localStorage.setItem('selectReportStatus', ob.value);\n      this.informationExchangeService.getALLInformationExchangeByInput(this.input).subscribe(response => {\n        this.totalRecords = response.totalCount;\n        this.exchangeInformationResultRecords = response.items;\n        setTimeout(() => {\n          this.setTableData();\n        }, 200);\n      });\n    }\n    onLinkClick(event) {\n      const data = event.rawData;\n      if (event.columnId === \"xmlData\" /* InformationExchangeTableColumns.XML_DATA */) {\n        //\n        // Note: /es-info-exchange/exchangedetail page is shared with \"XML Data\" view which parameter \"id\" = \"Id\" of table dbo.InformationExchanges,\n        // and \"Information Exchange History Page\" view, which paramter \"id\" = \"InformationExchangeDetailId\" of table dbo.InformationExchangeHistories.\n        //\n        this.router.navigate(['/es-info-exchange/exchangedetail'], {\n          //\n          // Passed \"Id\" of table dbo.InformationExchanges.\n          //\n          queryParams: {\n            id: data.id,\n            ishistory: false\n          }\n        });\n      } else if (event.columnId === \"viewDeclarations\" /* InformationExchangeTableColumns.VIEW_DECLARATION */) {\n        //\n        // When click the \"view\" link button in the Information Exchange records grid.\n        // Route to CaActionPageComponent.ts component\n        //\n        this.router.navigate(['/action-page'], {\n          queryParams: {\n            declarationid: data.declarationId,\n            entityid: data.corporateEntityId,\n            from: 'info-exchange'\n          }\n        });\n      } else if (event.columnId === \"viewHistory\" /* InformationExchangeTableColumns.VIEW_HISTORY */) {\n        //\n        // Open dialog to show history records. informantion-exchange-history.component.ts\n        //\n        this.openInformationExchangeHistoryDialog(data.id);\n      }\n    }\n    openInformationExchangeHistoryDialog(informationExchangeId) {\n      const dialogRef = this.dialog.open(InformationExchangeHistoryComponent, {\n        height: '750px',\n        width: '1200px',\n        data: {\n          informationExchangeId: informationExchangeId\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        console.log('The dialog was closed', result);\n      });\n    }\n    /** Check if logon user has permission \"Generate XML\".\n     *  Work for show/hide three xml buttons.\n     */\n    checkUserPermission() {\n      let result = false;\n      // Get current logon user object.\n      const currentUser = this.configState.getOne('currentUser');\n      if (currentUser) {\n        result = this.permissionService.getGrantedPolicy(\"DashboardService.Dashboard.GenerateXML\" /* Permissions.DASHBOARD_GENERATE_XML */);\n      }\n      return result;\n    }\n    /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */\n    getFiscalYears() {\n      return this.dashboardService.getFiscalYears().pipe();\n    }\n    // CTS Upload & Transmission Dashboard Methods\n    getCTSUploadStatusDescription(input) {\n      const foundStatus = CTSUploadStatusDic.find(status => status.value === input);\n      if (foundStatus) return foundStatus.description;\n      return '';\n    }\n    getReceivingCountryName(input) {\n      const foundCountry = this.countries.find(status => status.code2 === input);\n      if (foundCountry) return foundCountry.name;\n      return '';\n    }\n    getCountries() {\n      this.countryService.getList({\n        sorting: \"name asc\",\n        maxResultCount: 1000\n      }).subscribe(response => {\n        this.countries = response.items;\n        this.uploadHistoricalCountries = response.items;\n        // Remove code2 with empty string and null values in countries      \n        this.countries = this.countries.filter(country => country.code2 && country.code2.trim() !== '');\n        this.uploadHistoricalCountries = this.uploadHistoricalCountries.filter(country => country.code2 && country.code2.trim() !== '');\n        // add new country ALL in countries \n        this.countries.unshift({\n          name: 'All',\n          code2: ''\n        });\n      });\n    }\n    getBahamasCertificateInfo() {\n      return this.certificateService.getBahamasCertificateInfo().subscribe({\n        next: info => {\n          this.certificateExpirationDate = info?.expiredAt || null;\n        },\n        error: () => {\n          this.certificateExpirationDate = null;\n        }\n      });\n    }\n    getBahamasCtsSettingInfo() {\n      return this.bahamasCtsSettingService.getCurrentSettings().subscribe({\n        next: info => {\n          this.bahamasCtsSetting = info || null;\n        },\n        error: () => {\n          this.bahamasCtsSetting = null;\n        }\n      });\n    }\n    openUpdateCtsSettingDialog() {\n      const dialogRef = this.dialog.open(UpdateCtsSettingDialogComponent, {\n        width: '1200px',\n        data: this.bahamasCtsSetting || null\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (!result) return;\n        const formData = new FormData();\n        if (result.file) {\n          formData.append('fileName', result.file.name);\n          formData.append('file', result.file);\n          formData.append('fileType', result.file.type);\n        }\n        const certificateFileFormData = new FormData();\n        if (result.certificateFile) {\n          certificateFileFormData.append('fileName', result.certificateFile.name);\n          certificateFileFormData.append('file', result.certificateFile);\n          certificateFileFormData.append('fileType', result.certificateFile.type);\n        }\n        const isCreating = result.id == null;\n        const serviceCall = isCreating ? this.fileUploadService.createBahamasCtsSettings(result, formData, certificateFileFormData) : this.fileUploadService.updateBahamasCtsSettings(result, formData, certificateFileFormData);\n        const action = isCreating ? 'create' : 'update';\n        serviceCall.subscribe({\n          next: response => {\n            if (response) {\n              this.getBahamasCtsSettingInfo();\n              this.toasterService.success(`CTS Settings successfully ${action}d.`, '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn(`CTS Settings could not be ${action}d. Please try again later.`, null, {\n                life: 7000\n              });\n            }\n          },\n          error: error => {\n            this.toasterService.error(`An error occurred while trying to ${action} CTS Settings.`, null, {\n              life: 5000\n            });\n            console.error(`Error ${action}ing CTS Settings:`, error);\n          }\n        });\n      });\n    }\n    openUpdateCaCertificateDialog() {\n      const dialogRef = this.dialog.open(UpdateCaCertificateDialogComponent, {\n        width: '500px',\n        data: {}\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result && result.file) {\n          const formData = new FormData();\n          formData.append('fileName', result.file.name);\n          formData.append('file', result.file);\n          formData.append('fileType', result.file.type);\n          // Only call upload if file is present\n          this.fileUploadService.uploadBahamasCertificate(formData, result.password).subscribe({\n            next: response => {\n              if (response) {\n                // Fetch certificate expiration date\n                this.getBahamasCertificateInfo();\n                this.toasterService.success('Bahamas Certificate successfully uploaded', '', {\n                  life: 5000\n                });\n              } else {\n                this.toasterService.warn('Bahamas Certificate couldn’t be uploaded. Please try again later', null, {\n                  life: 7000\n                });\n              }\n            },\n            error: error => {\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n              console.error('Error uploading Bahamas certificate:', error);\n            }\n          });\n        }\n      });\n    }\n    // CTS Upload & Transmission Dashboard Methods\n    setCtsDashboardTableData() {\n      const tableData = new BdoTableData();\n      tableData.resetToFirstPage = false;\n      tableData.tableId = this.ctsDashboardTableId;\n      tableData.totalRecords = 1;\n      tableData.data = this.ctsDashboardList.map(x => ({\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: 'totalNotUploaded',\n          value: x.totalNotUploaded\n        }, {\n          columnId: 'totalReadyForUpload',\n          value: x.totalReadyForUpload\n        }, {\n          columnId: 'totalFailedUpload',\n          value: x.totalFailedUpload\n        }, {\n          columnId: 'totalUploadedToCTS',\n          value: x.totalUploadedToCTS\n        }, {\n          columnId: 'totalNotEnrolled',\n          value: x.totalNotEnrolled\n        }]\n      }));\n      setTimeout(() => this.tableService.setGridData(tableData), 10);\n    }\n    setCtsUploadTableData() {\n      const tableData = new BdoTableData();\n      tableData.resetToFirstPage = false;\n      tableData.tableId = this.ctsUploadTableId;\n      tableData.totalRecords = this.ctsUploadTotalRecords;\n      tableData.data = this.ctsUploadResultRecords.map(x => ({\n        id: x.id,\n        rawData: x,\n        cells: [{\n          columnId: 'exchangeReason',\n          value: this.getExchangeReasonDescription(x.exchangeReason)\n        }, {\n          columnId: 'dataPacket',\n          value: x.dataPacket\n        }, {\n          columnId: 'fileCreationDate',\n          value: DateHelper.convertUtcToLocalDate(x.fileCreationDate)\n        }, {\n          columnId: 'receivingCountry',\n          value: this.getReceivingCountryName(x.receivingCountry)\n        }, {\n          columnId: 'ctsUploadStatus',\n          value: this.getCTSUploadStatusDescription(x.ctsUploadStatus)\n        }, {\n          columnId: 'uploadedAt',\n          value: DateHelper.convertUtcToLocalDate(x.uploadedAt)\n        }, {\n          columnId: 'ctsTransmissionStatus',\n          value: x.ctsTransmissionStatus\n        }, {\n          columnId: 'viewExchangeRecords',\n          value: x.viewExchangeRecords === true ? 'View' : null\n        }, {\n          columnId: 'viewComments',\n          value: x.viewComments.length > 0 ? 'View Comments' : null\n        }, {\n          columnId: 'regeneratePacket',\n          value: this.showRegenerateDataPacket && x.allowedActions?.includes(0) ? {\n            actionType: 'RegenerateDataPacket',\n            icon: 'refresh',\n            tooltip: \"Regenerate Packet\"\n          } : null\n        }, {\n          columnId: 'ctsUpload',\n          value: this.showCTSUpload && x.allowedActions?.includes(1) ? {\n            actionType: 'ctsUpload',\n            icon: 'cloud_upload',\n            tooltip: \"CTS Upload\"\n          } : null\n        }, {\n          columnId: 'excludeFromCtsUpload',\n          hide: x.allowedActions?.includes(2) || x.allowedActions?.includes(3) ? false : true,\n          value: x.excludeFromCtsUpload\n        }]\n      }));\n      setTimeout(() => this.tableService.setGridData(tableData), 100);\n    }\n    onCtsDashboardLazyLoadEvent(event) {\n      this.currentPageIndexS = 0;\n      this.ctsPackageRequestService.getSummaryByYearByYear(this.ctsUploadSelectedYear).subscribe(response => {\n        this.ctsDashboardList = response;\n        setTimeout(() => {\n          this.setCtsDashboardTableData();\n        }, 200);\n      });\n    }\n    onCtsUploadLazyLoadEvent(event) {\n      if (event) {\n        if (this.ctsUploadPageSize === (event.pageSize ?? 10)) {\n          this.ctsUploadCurrentPage = event.pageNumber ?? 0;\n        } else {\n          //\n          // if Page size got changed through pagination control,\n          // need to reset current page index to 0.\n          //\n          this.ctsUploadPageSize = event.pageSize ?? 10;\n          this.ctsUploadCurrentPage = 0;\n        }\n        this.ctsUploadInput.skipCount = (this.ctsUploadCurrentPage ?? 0) * (this.ctsUploadPageSize ?? 10);\n        this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize ?? 10;\n        if (event.isAscending === false) {\n          this.ctsUploadInput.sorting = `${event.sortField} desc`;\n        } else {\n          this.ctsUploadInput.sorting = `${event.sortField} asc`;\n        }\n      } else {\n        this.ctsUploadCurrentPage = 0;\n        this.ctsUploadPageSize = 10;\n        this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n        this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n        this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n        this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n        this.ctsUploadInput.skipCount = 0;\n        this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n      }\n      this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n        if (response) {\n          this.ctsUploadTotalRecords = response.totalCount;\n          this.ctsUploadResultRecords = response.items;\n        } else {\n          this.ctsUploadTotalRecords = 0;\n          this.ctsUploadResultRecords = [];\n        }\n        setTimeout(() => {\n          this.setCtsUploadTableData();\n        }, 200);\n      });\n    }\n    onCtsUploadYearChange(ob) {\n      this.ctsUploadSelectedYear = ob.value;\n      localStorage.setItem('ctsUploadSelectedYear', ob.value);\n      this.onCtsUploadSearch();\n      this.onCtsDashboardLazyLoadEvent(undefined);\n    }\n    onCtsUploadSearch() {\n      this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;\n      this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;\n      this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;\n      this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;\n      this.ctsUploadInput.skipCount = 0;\n      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;\n      this.ctsPackageRequestService.getAllCtsPackageRequestByInput(this.ctsUploadInput).subscribe(response => {\n        this.ctsUploadTotalRecords = response.totalCount;\n        this.ctsUploadResultRecords = response.items;\n        setTimeout(() => {\n          this.setCtsUploadTableData();\n        }, 200);\n      });\n    }\n    onCtsUploadExchangeReasonChange(ob) {\n      this.selectExchangeReason = Number(ob.value);\n      // Keep the selected Exchange Reason in local storage.\n      localStorage.setItem('selectExchangeReason', ob.value);\n      this.onCtsUploadSearch();\n    }\n    onCtsUploadStatusChange(ob) {\n      this.selectCtsUploadStatus = Number(ob.value);\n      // Keep the selected Upload Status in local storage.\n      localStorage.setItem('selectCtsUploadStatus', ob.value);\n      this.onCtsUploadSearch();\n    }\n    onCtsUploadReceivingCountryChange(ob) {\n      this.selectReceivingCountry = ob.value;\n      // Keep the selected Receiving Country in local storage.\n      localStorage.setItem('selectReceivingCountry', ob.value);\n      this.onCtsUploadSearch();\n    }\n    onCtsUploadLinkClick(event) {\n      if (event.columnId === 'viewExchangeRecords') {\n        this.dialog.open(ViewAssociatedExchangeRecordsComponent, {\n          width: '1200px',\n          data: {\n            row: event.rawData\n          }\n        });\n      } else if (event.columnId === 'viewComments') {\n        this.dialog.open(ViewCommentDialogComponent, {\n          width: '1200px',\n          data: {\n            row: event.rawData\n          }\n        });\n      } else if (event.columnId === 'dataPacket') {\n        this.downloadCtsDataPacket(event.rawData);\n      }\n    }\n    onCtsUploadActionClick(event) {\n      if (event.action === 'RegenerateDataPacket') {\n        const dialogRef = this.dialog.open(RegeneratePacketDialogComponent, {\n          width: '500px',\n          data: {\n            row: event?.data?.rawData\n          }\n        });\n        dialogRef.afterClosed().subscribe(result => {\n          if (result && result.comment) {\n            this.ctsPackageRequestService.regeneratePackage(event.data?.rawData?.ctsPackageId, result.comment).subscribe({\n              next: response => {\n                if (response.success) {\n                  this.toasterService.success(response.message || 'Regenerate Package successfully requested', '', {\n                    life: 5000\n                  });\n                } else {\n                  this.toasterService.warn(response.message || 'Regenerate Package couldn’t be requested. Please try again later', null, {\n                    life: 7000\n                  });\n                }\n                this.onCtsUploadLazyLoadEvent(undefined);\n                this.onCtsDashboardLazyLoadEvent(undefined);\n              },\n              error: error => {\n                //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n                console.error('Error requesting Regenerate Package:', error);\n              }\n            });\n          }\n        });\n      } else if (event.action === 'ctsUpload') {\n        this.sweetAlert.fireDialog({\n          action: \"submit\",\n          title: \"CTS Upload\",\n          text: \"Are you sure you would like to proceed?\",\n          type: \"confirm\"\n        }, confirm => {\n          if (confirm) {\n            this.ctsPackageRequestService.uploadToCts(event.data?.rawData?.ctsPackageId).subscribe({\n              next: response => {\n                if (response.success) {\n                  this.toasterService.success(response.message || 'CTS Upload successfully completed', '', {\n                    life: 5000\n                  });\n                } else {\n                  this.toasterService.warn(response.message || 'CTS Upload couldn’t be completed. Please try again later', null, {\n                    life: 7000\n                  });\n                }\n                this.onCtsUploadLazyLoadEvent(undefined);\n                this.onCtsDashboardLazyLoadEvent(undefined);\n              },\n              error: error => {\n                //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n                console.error('Error requesting CTS Upload:', error);\n              }\n            });\n          }\n        });\n      }\n    }\n    onCheckboxClick(event) {\n      // Commented out: console.log('Checkbox clicked:', event.selectedRows[0]?.rawData?.ctsPackageId);\n      // Commented out: console.log('Checkbox clicked:', event.rowId);\n      if (event.isChecked) {\n        this.ctsPackageRequestService.markAsDoNotUpload(event.rowId).subscribe({\n          next: response => {\n            if (response.success) {\n              this.toasterService.success(response.message || 'Exclude packet from uploading successfully completed', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn(response.message || 'Exclude packet from uploading  couldn’t be completed. Please try again later', null, {\n                life: 7000\n              });\n            }\n            this.onCtsUploadLazyLoadEvent(undefined);\n            this.onCtsDashboardLazyLoadEvent(undefined);\n          },\n          error: error => {\n            console.error('Error marking as Do Not Upload:', error);\n          }\n        });\n      } else {\n        this.ctsPackageRequestService.unMarkAsDoNotUpload(event.rowId).subscribe({\n          next: response => {\n            if (response.success) {\n              this.toasterService.success(response.message || 'Include packet for uploading successfully completed', '', {\n                life: 5000\n              });\n            } else {\n              this.toasterService.warn(response.message || 'Include packet for uploading couldn’t be completed. Please try again later', null, {\n                life: 7000\n              });\n            }\n            this.onCtsUploadLazyLoadEvent(undefined);\n            this.onCtsDashboardLazyLoadEvent(undefined);\n          },\n          error: error => {\n            console.error('Error unmarking as Do Not Upload:', error);\n          }\n        });\n      }\n    }\n    // Add this method to open the Upload Historical XML dialog\n    openUploadHistoricalXmlDialog() {\n      const dialogRef = this.dialog.open(UploadHistoricalXmlDialogComponent, {\n        width: '500px',\n        data: {\n          receivingCountries: this.uploadHistoricalCountries || [],\n          fiscalYears: this.year || []\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result && result.success) {\n          this.onCtsUploadLazyLoadEvent(undefined);\n          this.onCtsDashboardLazyLoadEvent(undefined);\n        }\n      });\n    }\n    openCtsUploadDialog() {\n      this.sweetAlert.fireDialog({\n        action: \"submit\",\n        title: \"CTS Upload\",\n        text: `Are you sure you want to upload data packets with a Financial Period End in ${this.ctsUploadSelectedYear}? Once confirmed, all data packets with a status of Not Started for the year will be uploaded to the CTS platform.`,\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.ctsPackageRequestService.batchUploadToCts(Number(this.ctsUploadSelectedYear)).subscribe({\n            next: response => {\n              if (response.success) {\n                this.toasterService.success(response.message || 'CTS Upload successfully requested', '', {\n                  life: 5000\n                });\n              } else {\n                this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, {\n                  life: 7000\n                });\n              }\n              this.onCtsUploadLazyLoadEvent(undefined);\n              this.onCtsDashboardLazyLoadEvent(undefined);\n            },\n            error: error => {\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n              console.error('Error requesting CTS Upload:', error);\n            }\n          });\n        }\n      });\n      // const dialogRef = this.dialog.open(UploadCtsDialogComponent, {\n      //   width: '500px',\n      //   data: {\n      //     fiscalYears: this.year || []\n      //   }\n      // });\n      // dialogRef.afterClosed().subscribe((result) => {\n      //   if (result && result.fiscalYear) {\n      //     this.ctsPackageRequestService\n      //       .batchUploadToCts(result.fiscalYear).subscribe({\n      //         next: (response) => {\n      //           if (response.success) {\n      //             this.toasterService.success(response.message || 'CTS Upload successfully requested', '', { life: 5000 });\n      //           }\n      //           else {\n      //             this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, { life: 7000 });\n      //           }\n      //           this.onCtsUploadLazyLoadEvent(undefined);\n      //           this.onCtsDashboardLazyLoadEvent(undefined);\n      //         },\n      //         error: (error) => {\n      //           //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n      //           console.error('Error requesting CTS Upload:', error);\n      //         }\n      //       })\n      //   }\n      // });\n    }\n    openDecryptDialog() {\n      const dialogRef = this.dialog.open(DecryptDataPacketDialogComponent, {\n        width: '500px'\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result && result.response) {\n          this.onCtsUploadLazyLoadEvent(undefined);\n          this.onCtsDashboardLazyLoadEvent(undefined);\n          if (result.response.fileName != '') {\n            this.downloadFile(result.response.fileContent?.toString(), result.response.fileName);\n          }\n        }\n      });\n    }\n    refreshAllTransmissionStatus() {\n      this.sweetAlert.fireDialog({\n        action: \"submit\",\n        title: \"Refresh All Transmission Status\",\n        text: \"Are you sure you would like to proceed?\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.ctsPackageRequestService.refreshTransmissionStatus().subscribe({\n            next: response => {\n              if (response.success) {\n                this.toasterService.success(response.message || 'Refresh All Transmission Status successfully requested', '', {\n                  life: 5000\n                });\n              } else {\n                this.toasterService.warn(response.message || 'Refresh All Transmission Status couldn’t be requested. Please try again later', null, {\n                  life: 7000\n                });\n              }\n              this.onCtsUploadLazyLoadEvent(undefined);\n              this.onCtsDashboardLazyLoadEvent(undefined);\n            },\n            error: error => {\n              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });\n              console.error('Error requesting Refresh All Transmission Status:', error);\n            }\n          });\n        }\n      });\n    }\n    downloadCtsDataPacket(row) {\n      this.sweetAlert.fireDialog({\n        action: \"submit\",\n        title: \"Download CTS Data Packet\",\n        text: \"Are you sure you would like to download?\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.ctsPackageRequestService.downloadDataPacketFile(row?.ctsPackageId).subscribe({\n            next: response => {\n              if (response) {\n                this.downloadFile(response, row?.dataPacket);\n                this.toasterService.success('Download CTS Data Packet successfully completed', '', {\n                  life: 5000\n                });\n              } else {\n                this.toasterService.warn('Download CTS Data Packet couldn’t be completed. Please try again later', null, {\n                  life: 7000\n                });\n              }\n            },\n            error: error => {\n              // this.toasterService.error('Error Download CTS Data Packet', null, { life: 200000 });            \n              console.error('Error Download CTS Data Packet:', error);\n            }\n          });\n        }\n      });\n    }\n    static {\n      this.ɵfac = function InformationExchangeMainComponent_Factory(t) {\n        return new (t || InformationExchangeMainComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.InformationExchangeService), i0.ɵɵdirectiveInject(i3.InformationExchangeDetailsService), i0.ɵɵdirectiveInject(i4.PermissionService), i0.ɵɵdirectiveInject(i5.CADashboardContorllerService), i0.ɵɵdirectiveInject(i6.MatDialog), i0.ɵɵdirectiveInject(i7.CertificateService), i0.ɵɵdirectiveInject(i8.FileUploadService), i0.ɵɵdirectiveInject(i9.ToasterService), i0.ɵɵdirectiveInject(i10.CountryService), i0.ɵɵdirectiveInject(i11.CtsPackageRequestService), i0.ɵɵdirectiveInject(i12.SweetAlertService), i0.ɵɵdirectiveInject(i13.BahamasCtsSettingService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: InformationExchangeMainComponent,\n        selectors: [[\"app-information-exchange-main\"]],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 39,\n        vars: 35,\n        consts: [[3, \"selectedTabChange\"], [\"label\", \"Info Exchange Readiness\"], [1, \"top-action-row-exchange\", \"row\"], [1, \"table-container\"], [\"scrollHeight\", \"100%\", \"defaultSortColumnId\", \"uploadedDateTime\", 3, \"onLazyLoad\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\", \"pageSizeOptions\"], [1, \"top-action-column-exchange\", \"row\", \"justify-content-end\"], [1, \"col-md-auto\"], [\"class\", \"certificate-text\", 4, \"ngIf\"], [\"type\", \"button\", \"mat-raised-button\", \"\", \"class\", \"ui-button margin-l-5\", 3, \"click\", 4, \"ngIf\"], [1, \"top-action-column-exchange\", \"row\"], [1, \"col-md-4\", \"col-sm-12\", \"margin-top\"], [1, \"outside-mat-label\"], [1, \"form-field-reduce-length\"], [\"placeholder\", \"Financial Period End\", 3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-3\", \"col-sm-12\", \"margin-top\"], [\"placeholder\", \"Report Status\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"matInput\", \"\", \"placeholder\", \"File Name\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-2\", \"col-sm-12\", \"margin-top\", \"search-button-column\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"search-button\", 3, \"click\"], [\"scrollHeight\", \"36vh\", \"defaultSortColumnId\", \"ExchangeReason\", 3, \"onLazyLoad\", \"onLinkClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [\"label\", \"CTS Upload & Transmission\", 4, \"ngIf\"], [1, \"certificate-text\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", \"margin-l-5\", 3, \"click\"], [3, \"value\"], [\"label\", \"CTS Upload & Transmission\"], [1, \"row\", \"top-action-row-exchange\"], [\"scrollHeight\", \"auto\", 3, \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"], [1, \"outside-label\", \"outside-mat-label\"], [\"placeholder\", \"Exchange Reason\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"placeholder\", \"Receiving Country\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"placeholder\", \"CTS Upload Status\", 3, \"valueChange\", \"selectionChange\", \"value\"], [\"scrollHeight\", \"36vh\", \"defaultSortColumnId\", \"ExchangeReason\", 3, \"onLazyLoad\", \"onLinkClick\", \"onActionClick\", \"onCheckboxClick\", \"id\", \"columns\", \"defaultSortOrder\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\", \"isVirtualScroll\", \"hidePagination\", \"rowSelectable\", \"lazyLoad\"]],\n        template: function InformationExchangeMainComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"mat-tab-group\", 0);\n            i0.ɵɵlistener(\"selectedTabChange\", function InformationExchangeMainComponent_Template_mat_tab_group_selectedTabChange_0_listener($event) {\n              return ctx.tabChanged($event);\n            });\n            i0.ɵɵelementStart(1, \"mat-tab\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"bdo-table\", 4);\n            i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_4_listener($event) {\n              return ctx.onLazyLoadEventS($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n            i0.ɵɵtemplate(7, InformationExchangeMainComponent_span_7_Template, 3, 4, \"span\", 7)(8, InformationExchangeMainComponent_button_8_Template, 2, 0, \"button\", 8)(9, InformationExchangeMainComponent_button_9_Template, 2, 0, \"button\", 8)(10, InformationExchangeMainComponent_button_10_Template, 2, 0, \"button\", 8)(11, InformationExchangeMainComponent_button_11_Template, 2, 0, \"button\", 8)(12, InformationExchangeMainComponent_button_12_Template, 2, 0, \"button\", 8)(13, InformationExchangeMainComponent_button_13_Template, 2, 0, \"button\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"mat-label\", 11);\n            i0.ɵɵtext(17, \"Financial Period End\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"mat-form-field\", 12)(19, \"mat-select\", 13);\n            i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_19_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedYear, $event) || (ctx.selectedYear = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_19_listener($event) {\n              return ctx.onYearChange($event);\n            });\n            i0.ɵɵtemplate(20, InformationExchangeMainComponent_mat_option_20_Template, 2, 2, \"mat-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(21, \"div\", 15)(22, \"mat-label\", 11);\n            i0.ɵɵtext(23, \"Report Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"mat-form-field\", 12)(25, \"mat-select\", 16);\n            i0.ɵɵtwoWayListener(\"valueChange\", function InformationExchangeMainComponent_Template_mat_select_valueChange_25_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectReportStatus, $event) || (ctx.selectReportStatus = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectionChange\", function InformationExchangeMainComponent_Template_mat_select_selectionChange_25_listener($event) {\n              return ctx.onReportChange($event);\n            });\n            i0.ɵɵtemplate(26, InformationExchangeMainComponent_mat_option_26_Template, 2, 2, \"mat-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(27, \"div\", 15)(28, \"mat-label\", 11);\n            i0.ɵɵtext(29, \"Entity Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"mat-form-field\", 12)(31, \"input\", 17);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function InformationExchangeMainComponent_Template_input_ngModelChange_31_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchEntityName, $event) || (ctx.searchEntityName = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(32, \"div\", 18)(33, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function InformationExchangeMainComponent_Template_button_click_33_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵtext(34, \" Search \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"div\", 2)(36, \"div\", 3)(37, \"bdo-table\", 20);\n            i0.ɵɵlistener(\"onLazyLoad\", function InformationExchangeMainComponent_Template_bdo_table_onLazyLoad_37_listener($event) {\n              return ctx.onLazyLoadEvent($event);\n            })(\"onLinkClick\", function InformationExchangeMainComponent_Template_bdo_table_onLinkClick_37_listener($event) {\n              return ctx.onLinkClick($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(38, InformationExchangeMainComponent_mat_tab_38_Template, 38, 32, \"mat-tab\", 21);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"id\", ctx.TableIdS)(\"columns\", ctx.summaryExchangeColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndexS)(\"pageSize\", ctx.PageSizeS)(\"isVirtualScroll\", false)(\"hidePagination\", true)(\"rowSelectable\", true)(\"lazyLoad\", true)(\"pageSizeOptions\", i0.ɵɵpureFunction0(33, _c0));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.certificateExpirationDate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showBahamasSettings);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showUpdateCACertificate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showButton);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showButton && ctx.showOtherCase);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedYear);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.year);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectReportStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.informationExchangedDic);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchEntityName);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"id\", ctx.TableId)(\"columns\", ctx.exchangeResultColumns)(\"defaultSortOrder\", \"desc\")(\"pageIndex\", ctx.currentPageIndex)(\"pageSize\", ctx.PageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(34, _c0))(\"isVirtualScroll\", false)(\"hidePagination\", false)(\"rowSelectable\", true)(\"lazyLoad\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showDataPacketDashboard);\n          }\n        },\n        dependencies: [i14.DefaultValueAccessor, i14.NgControlStatus, i15.MatInput, i16.MatFormField, i16.MatLabel, i17.MatIcon, i18.MatButton, i19.MatSelect, i20.MatOption, i21.MatTab, i21.MatTabGroup, i22.BdoTableComponent, i23.NgForOf, i23.NgIf, i14.NgModel, i23.DatePipe],\n        styles: [\".search-title[_ngcontent-%COMP%]{font-size:2em;color:#00779b;display:block}.display-flex-main[_ngcontent-%COMP%]{display:flex;height:100%!important}.table-container[_ngcontent-%COMP%]{z-index:0;position:relative;margin-right:.5em;min-height:100%!important;max-width:100%!important}.display-flex[_ngcontent-%COMP%]{display:flex}.top-action-row-exchange[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;padding-top:10px}.top-action-row-exchange-noalign[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start}.top-action-column-exchange[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:flex-start;margin-top:1em}.margin-top[_ngcontent-%COMP%]{margin-top:1.5em}.margin-left-push[_ngcontent-%COMP%]{margin-left:30em}.margin-left[_ngcontent-%COMP%]{margin-left:2em}.top-action-row-header[_ngcontent-%COMP%]{display:flex;flex-direction:column}.upload-import-container[_ngcontent-%COMP%]{display:flex}.outside-mat-label[_ngcontent-%COMP%]{font-size:1.2em;margin-right:10px;margin-top:2em}.form-field-container[_ngcontent-%COMP%]{display:flex;align-items:center}.mat-form-field[_ngcontent-%COMP%]{margin-right:10px}.search-button-column[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;vertical-align:middle;height:100px!important}@media (max-width: 1750px){.outside-mat-label[_ngcontent-%COMP%]{font-size:1.2em;margin-right:10px;margin-top:0}.search-button[_ngcontent-%COMP%]{margin-top:1.9em}}@media (max-width: 900px){.outside-mat-label[_ngcontent-%COMP%]{font-size:1.2em;margin-right:10px}.search-button[_ngcontent-%COMP%]{margin-top:0}}.certificate-text[_ngcontent-%COMP%]{font-size:14px;color:#333;margin-right:15px}@media (min-width: 768px){.col-md-2-5[_ngcontent-%COMP%]{flex:0 0 auto;max-width:20.8333%}.col-md-2-75[_ngcontent-%COMP%]{flex:0 0 auto;max-width:22.9166%}}@media (min-width: 1750px) and (max-width: 3840px){.search-button-m-top[_ngcontent-%COMP%]{margin-top:3.9em}}.outside-label[_ngcontent-%COMP%]{display:flex;justify-content:flex-start}\"]\n      });\n    }\n  }\n  return InformationExchangeMainComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}