﻿using Bdo.Ess.SearchService.AdvancedSearch;
using Bdo.Ess.SearchService.ElasticSearch;
using Bdo.Ess.SearchService.Features;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Constants.Saas;
using Bdo.Ess.Shared.Hosting.Microservices;
using Bdo.Ess.Shared.Hosting.Microservices.Audit;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.Audit;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Clients.Elasticsearch.IndexManagement;
using Elastic.Clients.Elasticsearch.QueryDsl;
using Elastic.Transport.Diagnostics.Auditing;
using Elasticsearch.Net;
using LdapForNet;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Features;

namespace Bdo.Ess.SearchService.BasicSearch
{
	public class BasicSearchAppService : SearchServiceAppService, IBasicSearchAppService
	{
		private readonly IElasticSearchService _elasticSearchService;
		private readonly BdoTenantService _bdoTenantHelperService;
		private readonly ILogger<BasicSearchAppService> _logger;

		private const string RASearch = "RA";
		private const string CASearch = "CA";

		/// <summary>
		///  Note: _auditWebInfo is scoped dependency instance, 
		///  so, it will be shared between HttpApi and AppService
		///  Work for Auditing purpose to get client IP address.
		/// </summary>
		private readonly IAuditWebInfo _auditWebInfo;

		protected readonly IDistributedEventBus _searchAuditEventBus;

		public BasicSearchAppService(IElasticSearchService elasticSearchService,
			ILogger<BasicSearchAppService> logger,
			BdoTenantService bdoTenantHelperService,
			IAuditWebInfo auditWebInfo,
			IDistributedEventBus searchAuditEventBus
			)
		{
			_elasticSearchService = elasticSearchService;
			_logger = logger;
			_bdoTenantHelperService = bdoTenantHelperService;
			_auditWebInfo = auditWebInfo;
			_searchAuditEventBus = searchAuditEventBus;
		}

		public async Task<BasicSearchResultDto> RABasicSearch(BasicSearchRequestDto searchRequest)
		{
			var docs = await BasicSearch<BasicSearchResultItem>(searchRequest, true);

			try
			{
				await this.AddAuditLogForPerformESSearch(searchRequest, docs.Item2);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "AddAuditLogForPerformESSearch");
			}

			return new BasicSearchResultDto
			{
				Documents = docs.Item1.ToList(),
				TotalCount = docs.Item2
			};
		}

		[RequiresFeature(SearchServiceFeatures.CASearch.Default)]
		public async Task<CaBasicSearchResultDto> CABasicSearch(BasicSearchRequestDto searchRequest)
		{
			throw new UserFriendlyException("CA Basic Search is not enabled for this service instance. Please contact system administrator");
            var docs = await BasicSearch<CABasicSearchResultItem>(searchRequest, false);

			try
			{
				await this.AddAuditLogForPerformESSearch(searchRequest, docs.Item2);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "AddAuditLogForPerformESSearch");
			}

			return new CaBasicSearchResultDto
			{
				Documents = docs.Item1.ToList(),
				TotalCount = docs.Item2
			};
		}

		private async Task<bool> AddAuditLogForPerformESSearch(BasicSearchRequestDto searchRequest, int recordNumber)
		{
			try
			{
				var searchEto = new AuditESBasicSearchEto()
				{
					UserName = this.CurrentUser.UserName,
					UserId = this.CurrentUser.Id,
					IPAddress = _auditWebInfo.IPAddress,
					Action = AuditActionEnum.PerformESSearch,
					TenantId = CurrentTenant.Id,
					AuditDateTime = DateTime.UtcNow,
					ESPeriodEnd = searchRequest?.SearchFields?.PeriodEndYear,
					EntityName = searchRequest?.SearchFields?.EntityName,
					NewValue = new AuditESBasicSearchDto(searchRequest, recordNumber),
				};

				// Add Audit log.
				await _searchAuditEventBus.PublishAsync(searchEto);

				return true;
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "AddAuditLogForPerformESSearch");
				return false;
			}
		}

		private async Task<Tuple<IEnumerable<T>,int>> BasicSearch<T>(BasicSearchRequestDto searchRequest, bool isRASearch) where T : BasicSearchResultItem
		{
			try
			{
				var client = _elasticSearchService.ElasticsearchClient();

				var searchType = isRASearch ? RASearch : CASearch;

				//Convert search request into search query
				Query query = SetupBasicSearchQuery(searchRequest, isRASearch);

				//Setup friendly Query string for logging.
				string friendlyQueryString = SetupFriendlyQueryString(searchRequest.SearchFields);

				//Log query string
				var sbQuery = new StringBuilder();
				sbQuery.AppendFormat("Basic {0} Search: new query - {1}", searchType, friendlyQueryString);
				_logger.LogInformation(sbQuery.ToString());

				//parse out sorting
				var sortField = SearchUtility.GetSortField(searchRequest.Sorting);
				var secondarySortField = SearchUtility.GetSortField("EntityName"); // only in the case of submission time will the primary sort maybe empty and all records without submission time will be sorted by Entity Name with the same order
				SortOrder sortOrder = SearchUtility.GetSortOrder(searchRequest.Sorting);

				var indexName = isRASearch ? _elasticSearchService.RAIndexName() : _elasticSearchService.CAIndexName();

				var response = await client.SearchAsync<T>(s =>
					s.Index(indexName)
					.From(searchRequest.SkipCount)
					.Size(searchRequest.MaxResultCount)
					.Query(query)
					.Sort(s => s.Field(sortField, cfg => cfg.Order(sortOrder)), ss => ss.Field(secondarySortField, cfg => cfg.Order(sortOrder)))
					.TrackTotalHits(new TrackHits(true))
					.Collapse(c => c.Field(new Field("entityId.keyword")).InnerHits(i => i.Name(new Name("innerEntities")).DocvalueFields(f => f.Field(p => p.SubmissionTime))))
				);

				if (response.IsValidResponse)
				{
					var allDocuments = CheckMultipleEndYear(response, isRASearch);

					var sb = new StringBuilder();
					sb.AppendFormat("Basic {0} Search Success, {1} items returned", searchType, GetTotalHit(response.HitsMetadata.Total));

					_logger.LogInformation(sb.ToString());

					return new Tuple<IEnumerable<T>, int> ( allDocuments, (int) GetTotalHit(response.HitsMetadata.Total));
				}
				else
				{
					var sb = new StringBuilder();
					sb.AppendFormat("Basic {0} Search Failed with Error", searchType);
					sb.AppendLine();
					sb.Append(response.DebugInformation);

					_logger.LogError(sb.ToString());
					return new Tuple<IEnumerable<T>, int>(new List<T>(), 0);
				}
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, ex.Message);
				throw;
			}
		}

		private string SetupFriendlyQueryString(BasicSearchFields searchFields)
		{
			var friendlyString = "";

			if (searchFields.PeriodEndYear.HasValue)
			{
				friendlyString += "Period End Year = " + searchFields.PeriodEndYear.Value.ToString() + " ";
			}

			if(searchFields.EntitiesRequiringDeclaration.HasValue)
			{
				friendlyString += "and Entities Requiring Declaration = " + searchFields.EntitiesRequiringDeclaration.Value.ToString() + " ";
			}

			if (!string.IsNullOrEmpty(searchFields.DeclarationStatusName))
			{
				friendlyString += "and Declaration Status = " + searchFields.DeclarationStatusName + " ";
			}

			if (!string.IsNullOrEmpty(searchFields.EntityName))
			{
				friendlyString += "and Entity Name (or Alternative Name) = " + searchFields.EntityName;
			}

			return friendlyString.TrimEnd();
		}

		private IEnumerable<T> CheckMultipleEndYear<T>(SearchResponse<T> response, bool isRASearch) where T : BasicSearchResultItem
		{
			var innerHits = response.Hits.Where(x => x.InnerHits != null && x.InnerHits.Count > 0).ToList();
			var allDocuments = response.Documents.ToList();
			//We are grouping search by entityId. If there are multiple items with same entityID, should return only 1
			//Collapse Value in Search Request specifies grouping
			if (innerHits.Any())
			{
				foreach (var hit in innerHits)
				{
					var i = hit.InnerHits.FirstOrDefault();
					var d = i.Value.Hits.Hits;
					if (d.Count > 1)
					{
						//Need to get the latest submitted declaration, and replace the item in the returned collection
						T latestInnerItem = null;

						foreach (var h in d)
						{
							//var field = h.Fields;
							var source = h.Source.ToString();
							var obj = JsonConvert.DeserializeObject<T>(source);
							if (latestInnerItem == null)
							{
								latestInnerItem = obj;
							}
							else
							{
								//For same year delcarations of an entity, always choose the one has later end date
								if ((obj.FinancialPeriodEndDate.HasValue && !latestInnerItem.FinancialPeriodEndDate.HasValue) || 
									(obj.FinancialPeriodEndDate.HasValue && latestInnerItem.FinancialPeriodEndDate.HasValue &&
									obj.FinancialPeriodEndDate.Value > latestInnerItem.FinancialPeriodEndDate.Value))
								{
									latestInnerItem = obj;
								}
								else if (isRASearch && (obj.LastSavedTime.HasValue && !latestInnerItem.LastSavedTime.HasValue) ||
									(obj.LastSavedTime.HasValue && latestInnerItem.LastSavedTime.HasValue && obj.LastSavedTime > latestInnerItem.LastSavedTime)
									) //For Draft Declaration, there is no Submission Time, use LastSaveTime instead
								{
									latestInnerItem = obj;
								}
								else if (!isRASearch && obj.SubmissionTime > latestInnerItem.SubmissionTime)
									//For CA Search Declaration
								{
									latestInnerItem = obj;
								}
							}
						}
						//If the Search outer item has a lower submission time, replace with the latest inner item
						if (latestInnerItem != null)
						{
							var entityField = hit.Fields["entityId.keyword"]?.ToString();
							if (entityField != null)
							{
								var entityIds = JsonConvert.DeserializeObject<Guid[]>(entityField);
								if (entityIds != null && entityIds.Length > 0)
								{
									int index = allDocuments.FindIndex(x => x.EntityId == entityIds[0]);
									if (index >= 0)
									{
										allDocuments[index] = latestInnerItem;
									}
								}
							}
						}
					}

				}
			}
			return allDocuments;
		}

		private Query SetupBasicSearchQuery(BasicSearchRequestDto searchRequest, bool isRASearch)
		{
			Query q;

			var sf = searchRequest.SearchFields;

			//Period End Year is always required
			q = new TermQuery("periodEndYear") { Value = sf.PeriodEndYear.Value };

			var tenantKeyword = "tenantId.keyword";
			//Tenant ID
			if (isRASearch)
			{
				try
				{
					var tenantId = CurrentTenant.Id.Value.ToString();
					if (string.IsNullOrEmpty(tenantId))
					{
						//Don't allow RAs to run basic search without tenant filter
						_logger.LogError("RA Basic Search - Tenant Id was not found. Throwing error to prevent security risks");
						throw new UserFriendlyException("There was a problem running this Search Query. Please try again later");
					}
					q = q && new TermQuery(tenantKeyword) { Value = tenantId };
				}
				catch (Exception e)
				{
					_logger.LogError("RA Basic Search - There was an error accessing Current Tenant information");
					_logger.LogError(e, e.Message);
					throw;
				}
			}
			else
			{
				var ignoreIds = _elasticSearchService.CAIgnoreIds();
				if (ignoreIds.Length > 0)
				{
					var firstId = ignoreIds[0];
					q = q && !(Query.Term( new TermQuery(tenantKeyword) { Value = firstId }));

					var ignoreList = ignoreIds.Where(x => x != firstId).ToList();

					foreach (var ignoreId in ignoreList)
					{
						q = q && !(Query.Term(new TermQuery(tenantKeyword) { Value = ignoreId })); 
					}
				}
			}

			//EntityName
			if (!string.IsNullOrEmpty(sf.EntityName))
			{
				var entityNameKeyword = "entityName.keyword";
				var alternativeNameKeyword = "alternativeName.keyword";

				var entityNameValue = "*" + sf.EntityName + "*";

				var entityNameQuery = new WildcardQuery(entityNameKeyword) { Value = entityNameValue, CaseInsensitive = true };
				var alternativeNameQuery = new WildcardQuery(alternativeNameKeyword) { Value = entityNameValue, CaseInsensitive = true };

				q = q && (Query.Wildcard( entityNameQuery) || Query.Wildcard(alternativeNameQuery));
			}

			//Entities Requiring Declaration
			if (sf.EntitiesRequiringDeclaration.HasValue && sf.EntitiesRequiringDeclaration.Value)
			{ 
				var erdQuery = new TermQuery("entitiesRequiringDeclaration") { Value = sf.EntitiesRequiringDeclaration.Value };

				q = q && erdQuery;
			}

			//Declaration or Assessment Status
			if (isRASearch)
			{
				if (sf.DeclarationStatus.HasValue)
				{ 
					var declarationStatusKeyword = "declarationStatus.keyword";
					q = q && new TermQuery(declarationStatusKeyword) { Value = sf.DeclarationStatus.Value.ToString() };
				}
			} else
			{
				if (sf.AssessmentStatusId.HasValue)
				{
					var assessmentStatusKeyword = "assessmentStatusId.keyword";
					q = q && new TermQuery(assessmentStatusKeyword) { Value = sf.AssessmentStatusId.Value.ToString() };
				}
			}

			return q;
		}

		private string FormatLuceneQueryString(BasicSearchFields searchObject, bool isRASearch)
		{
			var sb = new StringBuilder();
			if (isRASearch)
			{
				try
				{
					var tenantId = CurrentTenant.Id.Value.ToString();
					if (string.IsNullOrEmpty(tenantId))
					{
						//Don't allow RAs to run basic search without tenant filter
						_logger.LogError("RA Basic Search - Tenant Id was not found. Throwing error to prevent security risks");
						throw new UserFriendlyException("There was a problem running this Search Query. Please try again later");

					}
					sb.AppendFormat("tenantId:\"{0}\" AND ", tenantId);
				}
				catch (Exception e)
				{
					_logger.LogError("RA Basic Search - There was an error accessing Current Tenant information");
					_logger.LogError(e, e.Message);
					throw;
				}
			}
			else {
				var ignoreIds = _elasticSearchService.CAIgnoreIds();
				if (ignoreIds.Length > 0)
				{
					foreach (var ignoreId in ignoreIds)
					{
						sb.AppendFormat("NOT tenantId:\"{0}\" AND ", ignoreId);
					}
				}
			}
			foreach (PropertyInfo prop in searchObject.GetType().GetProperties())
			{
				var val = prop.GetValue(searchObject);
				if (val != null)
				{
					var fieldName = prop.Name;
					if(fieldName == "DeclarationStatusName" || fieldName == "AssessmentStatusName")
					{
						continue;
					}
					var lowerFieldName = (char.ToLower(fieldName[0]) + fieldName.Substring(1));
					//Adding .keyword to the property will index the field including special characters
					var lowerKeyfield = lowerFieldName + ".keyword";
					var valString = val.ToString();
					if (prop.PropertyType == typeof(string))
					{
						valString = EncodeOrEscape(valString);

						//need to separate the query into multiple queries if querystring has spaces, with wildcards at the end, OR the whole query
						//Need to search both keyword attribute and base for case sensitivity and special characters
						//(propertyName:*value AND propertyName:val2 AND propertyName:val3*) OR propertyName:"value val2 val3" OR 
						//(propertyName.keyword:*value AND propertyName.keyword:val2 AND propertyName.keyword:val3*) OR propertyName.keyword:"value val2 val3"
						//
						var split = valString.Split(' ');

						var newString = "";
						var string2 = "";
						if (split.Length > 1)
						{
							for (int i = 0; i < split.Length; i++)
							{
								if (i == 0)
								{
									newString += "(" + lowerFieldName + ":*" + split[i];
									string2 += "(" + lowerKeyfield + ":*" + split[i];
								}
								else if (i == split.Length - 1)
								{
									newString += " AND " + lowerFieldName + ":" + split[i] + "*)";
									string2 += " AND " + lowerKeyfield + ":" + split[i] + "*)";
								}
								else {
									newString += " AND " + lowerFieldName + ":" + split[i];
									string2 += " AND " + lowerKeyfield + ":" + split[i];
								}
							}
							newString += " OR " + lowerFieldName + ":\"" + valString + "\"";
							string2 += " OR " + lowerKeyfield + ":\"" + valString + "\"";
						}
						else {
							//fieldName.keyword:*value* OR fieldName.keyword:"value"
							newString = lowerFieldName + ":*" + valString + "* OR " + lowerFieldName + ":\"" + valString + "\"";
							string2 = lowerKeyfield + ":*" + valString + "* OR " + lowerKeyfield + ":\"" + valString + "\"";
						}

						newString += " OR " + string2;

						if (fieldName == "EntityName") //Need to search the Alternative Name field as well
						{
							var entityQuery = "(" + newString;
							var replaceString = newString.Replace("entityName", "alternativeName");
							entityQuery += " OR " + replaceString + ")";
							newString = entityQuery;
						}
						newString += " AND ";
						sb.Append(newString);
						continue;
					}
					if (prop.PropertyType == typeof(Guid) || Nullable.GetUnderlyingType(prop.PropertyType) == typeof(Guid))
					{
						//Adding escaped double quotes makes the string act as a phrase, instead of a collection of separate strings to match
						var v = "\"" + valString + "\"";
						valString = v;
					}
					if (Nullable.GetUnderlyingType(prop.PropertyType) == typeof(bool))
					{
						valString = (bool)val ? "true" : "false";
					}
					if (prop.Name == "EntityName")
					{
						//EntityName or Alternative Name
						sb.AppendFormat("({0}:{1} OR {2}:{1}) AND ", (char.ToLower(fieldName[0]) + fieldName.Substring(1)), valString, "alternativeName");
					}
					else { 
						sb.AppendFormat("{0}:{1} AND ", (char.ToLower(fieldName[0]) + fieldName.Substring(1)), valString);
					}                    
				}
			}

			var queryString = sb.ToString();
			queryString = queryString.Substring(0, queryString.Length - 5);
			return queryString;
		}

		private string? EncodeOrEscape(string? valString)
		{
			//valString = valString.Replace("%", "\\%25");
			//valString = valString.Replace("#", "\\%23");
			//valString = valString.Replace("&", "\\%26");
			valString = valString.Replace("%", "\\%");
			valString = valString.Replace("#", "\\#");
			valString = valString.Replace("&", "\\&");
			valString = valString.Replace("!", "\\!");
			valString = valString.Replace("^", "\\^");
			valString = valString.Replace("*", "\\*");
			valString = valString.Replace("(", "\\(");
			valString = valString.Replace(")", "\\)");

			return valString;
		}

		private async Task<IEnumerable<T>> GetEntityHistory<T>(Guid entityId, PagedAndSortedResultRequestDto request)
		{
			try
			{
				var client = _elasticSearchService.ElasticsearchClient();
				var isRASearch = (await _bdoTenantHelperService.GetCurrentEdition()) == EssEditionConsts.RaPortal;

				//double quotes for phrase match
				string queryString = "entityId:\"" + entityId + "\" AND ";

				if (isRASearch)
				{
					queryString += "NOT declarationStatus:\"00000000-0000-0000-0000-000000000000\"";
				}
				else {
					queryString += "NOT assessmentStatusId:\"00000000-0000-0000-0000-000000000000\" AND assessmentStatusId:([0 TO 9] [a TO z])";
				}

				//parse out sorting
				var sortField = SearchUtility.GetSortField(request.Sorting, true);
				SortOrder sortOrder = SearchUtility.GetSortOrder(request.Sorting, true);

				var indexName = isRASearch ? _elasticSearchService.RAIndexName() : _elasticSearchService.CAIndexName();

				var searchType = isRASearch ? "RA" : "CA";

				var response = await client.SearchAsync<T>(s =>
					s.Index(indexName)
					.From(request.SkipCount)
					.Size(request.MaxResultCount)
					.QueryLuceneSyntax(queryString)
					.Sort(s => s.Field(sortField, cfg => cfg.Order(sortOrder)),
					ss => ss.Field("submissionTime", cfg => cfg.Order(sortOrder)))
				);
				if (response.IsValidResponse)
				{
					var sb = new StringBuilder();
					sb.AppendFormat("History {0} Search Success, {1} items returned", searchType, response.Documents.Count);
					_logger.LogInformation(sb.ToString());
					return response.Documents.ToList();

				}
				else
				{
					var sb = new StringBuilder();
					sb.AppendFormat("History {0} Search Failed with Error", searchType);
					sb.AppendLine();
					sb.Append(response.DebugInformation);
					_logger.LogError(sb.ToString());
					return new List<T>();
					
				}
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, ex.Message);
				return null;
			}
		}

		public async Task<List<BasicSearchResultItem>> RAEntityHistory(Guid entityId, PagedAndSortedResultRequestDto searchRequest)
		{
			var historyList = await GetEntityHistory<BasicSearchResultItem>(entityId, searchRequest);
			return historyList.ToList();
		}

		[RequiresFeature("SearchService.CASearch")]
		public async Task<List<CABasicSearchResultItem>> CAEntityHistory(Guid entityId, PagedAndSortedResultRequestDto searchRequest)
		{
			var historyList = await GetEntityHistory<CABasicSearchResultItem>(entityId, searchRequest);
			return historyList.ToList();
		}
	}
}
