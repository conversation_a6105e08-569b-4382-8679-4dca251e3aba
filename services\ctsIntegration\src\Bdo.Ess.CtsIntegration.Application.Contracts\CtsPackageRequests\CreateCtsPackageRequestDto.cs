using Bdo.Ess.CtsIntegration.enums;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using System;
using Volo.Abp.Application.Dtos;

namespace Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests
{
    public class CreateCtsPackageRequestDto : EntityDto<Guid>
    {

        public Guid? EssInformationXmlId { get; set; }
        public string? CtsPackageFileName { get; set; }
        public string ReceiverCountryCode { get; set; } = "";
        public string? MetaCountryCode { get; set; }
        public int FiscalYear { get; set; }
        public ExchangeReason? ExchangeReason { get; set; }
        
        //This property could be retired, since we put source XML (Encrypted) in azure storage 
        public string? XmlPayload { get; set; }
        public string  XmlPayloadUrl { get; set; } = "";
        public string? PackageZipUrl { get; set; }
        public DateTime? FileCreatedAt { get; set; }
        public DateTime? UploadedAt { get; set; }
        public int UploadAttempts { get; set; }
        public CTSUploadStatus? UploadStatus { get; set; }
        public bool? EligibleCheckCtsStatus { get; set; }
        public string? TransmissionStatus { get; set; }
        public bool IsExcludeCtsUpload { get; set; }
        public DateTime? StatusUpdatedAt { get; set; }
        public bool HasExchangeRecords { get; set; }
		public string? TransmissionStatusDesc { get; set; }
		public string? CtsTransmissionId { get; set; }
		public DateTime? TransmissionStatusLastCheckedUtc { get; set; }

		public Guid TenantId { get; set; }
        public string MessageRefId { get; set; } = "";
    }
}
