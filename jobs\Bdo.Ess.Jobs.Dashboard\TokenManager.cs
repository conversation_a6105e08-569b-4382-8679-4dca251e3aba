﻿using IdentityModel;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;
using Volo.Abp.IdentityModel;

namespace Bdo.Ess.Jobs.Dashboard
{
    public class TokenManager : ITransientDependency
    {
        private readonly IIdentityModelAuthenticationService _authenticationService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TokenManager> _logger;

        private string? _currentToken;
        private DateTime _tokenExpiresAt = DateTime.MinValue;
        private readonly object _tokenLock = new object();

        public TokenManager(IIdentityModelAuthenticationService authenticationService, IConfiguration configuration, ILogger<TokenManager> logger)
        {
            _authenticationService = authenticationService;
            _configuration = configuration;
            _logger = logger;
        }

        public string GetValidToken()
        {
            // Check if token is still valid (with 2-minute buffer)
            if (_currentToken !=null && !IsTokenExpired())
            {
                return _currentToken;
            }

            // Use lock to prevent multiple concurrent token requests
            lock (_tokenLock)
            {
                // Double-check after acquiring lock
                if (_currentToken != null && !IsTokenExpired())
                {
                    return _currentToken;
                }

                // Note: We need to use GetAwaiter().GetResult() since we're in a lock
                return RefreshTokenAsync().GetAwaiter().GetResult();
            }
        }

        private DateTime? GetTokenExpirationTime(string jwtToken)
        {
           
                // Method 1: Using System.IdentityModel.Tokens.Jwt (if available)
                var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
                var token = handler.ReadJwtToken(jwtToken);

                _logger.LogInformation("Token expires at: {ExpiresAt} UTC", token.ValidTo);
                var dt = DateTime.SpecifyKind(token.ValidTo, DateTimeKind.Utc);
                return dt;
            
        }

        public bool IsTokenExpired()
        {
            return string.IsNullOrEmpty(_currentToken) || DateTime.UtcNow >= _tokenExpiresAt.AddMinutes(-2);
        }
        public async Task<string> RefreshTokenAsync()
        {
            _logger.LogInformation("Refreshing access token...");

            _currentToken = await _authenticationService.GetAccessTokenAsync(
                new IdentityClientConfiguration(
                    _configuration["IdentityClients:Default:Authority"]!,
                    _configuration["IdentityClients:Default:Scope"]!,
                    _configuration["IdentityClients:Default:ClientId"]!,
                    _configuration["IdentityClients:Default:ClientSecret"]!,
                    grantType: OidcConstants.GrantTypes.ClientCredentials,
                    cacheAbsoluteExpiration: 1,  //set it to 1 second to force refresh every time
                    requireHttps: false
                )
            );

            var expiresAt = GetTokenExpirationTime(_currentToken);
            Console.WriteLine("Token expires at: {0} UTC", expiresAt);
            Console.WriteLine("Current UTC time: {0} UTC", DateTime.UtcNow);
            _tokenExpiresAt = expiresAt!.Value;

            _logger.LogInformation("Access token refreshed. Expires at: {ExpiresAt}", _tokenExpiresAt);

            return _currentToken;
        }
    }
}