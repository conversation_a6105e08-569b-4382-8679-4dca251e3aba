{"ast": null, "code": "import _asyncToGenerator from \"C:/Temp/azuregit/cosmosDb/ESS/apps/angular/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DateHelper } from '@app/shared/utils/date-helper';\nimport { AppComponentBase } from '@app/app-component-base';\nimport { themeJson } from '@app/features/es-declaration/containers/es-declaration/survey-theme-json';\nimport { viewDeclarationEmpty, viewRelevantActivites } from '@app/shared/declaration-jsons/view-declaration-json';\nimport { environment } from '@environments/environment';\nimport { finalize, forkJoin } from 'rxjs';\nimport { ComponentCollection, Model, Serializer } from \"survey-core\";\nimport { RelevantActivityType } from 'proxies/economic-service/lib/proxy/bdo/ess/shared/hosting/microservices/eto/declaration/base-classes';\nimport Swal from 'sweetalert2';\nimport { Converter } from \"showdown\";\nimport { AngularComponentFactory } from 'survey-angular-ui';\nimport { SurveyFilePreviewComponent } from '@app/shared/components/survey-file-preview/survey-file-preview.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declarations\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration\";\nimport * as i5 from \"@app/shared/services/composite-question.service\";\nimport * as i6 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates\";\nimport * as i7 from \"proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/assessment\";\nimport * as i8 from \"@app/shared/services/sweetalert.service\";\nimport * as i9 from \"@abp/ng.theme.shared\";\nimport * as i10 from \"@abp/ng.core\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"survey-angular-ui\";\nimport * as i13 from \"../../../es-declaration/containers/declaration-entity-details/declaration-entity-details.component\";\nimport * as i14 from \"../../../es-declaration/containers/assessment-action-view/assessment-action-view.component\";\nimport * as i15 from \"../assesment-selection/assesment-selection.component\";\nfunction CaActionPageComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"mode\", \"indeterminate\")(\"diameter\", 100);\n  }\n}\nfunction CaActionPageComponent_app_assesment_selection_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-assesment-selection\", 6);\n    i0.ɵɵlistener(\"refreshData\", function CaActionPageComponent_app_assesment_selection_1_Template_app_assesment_selection_refreshData_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refresh($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"activityActions\", ctx_r1.activityActions)(\"activityCount\", ctx_r1.activityCount)(\"declarationData\", ctx_r1.declarationData)(\"from\", ctx_r1.from);\n  }\n}\nfunction CaActionPageComponent_app_assessment_action_view_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-assessment-action-view\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"declarationData\", ctx_r1.declarationData)(\"declarationId\", ctx_r1.declarationData == null ? null : ctx_r1.declarationData.id);\n  }\n}\nfunction CaActionPageComponent_div_3_survey_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"survey\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"model\", ctx_r1.survey);\n  }\n}\nfunction CaActionPageComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"app-declaration-entity-details\", 9);\n    i0.ɵɵtemplate(2, CaActionPageComponent_div_3_survey_2_Template, 1, 1, \"survey\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"entityId\", ctx_r1.entityId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.declarationData);\n  }\n}\nAngularComponentFactory.Instance.registerComponent(\"sv-file-preview\", SurveyFilePreviewComponent);\nexport class CaActionPageComponent extends AppComponentBase {\n  constructor(injector, declarationService, route, datePipe, activityLookup, compositeQuestionService, service, cigaLookup, assessmentService, sweetAlert, toasterService, config) {\n    super(injector);\n    this.declarationService = declarationService;\n    this.route = route;\n    this.datePipe = datePipe;\n    this.activityLookup = activityLookup;\n    this.compositeQuestionService = compositeQuestionService;\n    this.service = service;\n    this.cigaLookup = cigaLookup;\n    this.assessmentService = assessmentService;\n    this.sweetAlert = sweetAlert;\n    this.toasterService = toasterService;\n    this.config = config;\n    this.compositeKeys = [\"intellectualPropertyBusiness\", \"bankingQuestions\", \"distributionQuestions\", \"financeQuestions\", \"fundManagmentQuestions\", \"headquartersQuestions\", \"holdingBusinessQuestions\", \"insuranceQuestions\", \"outsourcingIntellectualPropertyBusiness\", \"shippingQuestions\"];\n    this.templateListDto = {\n      sorting: \"id\",\n      skipCount: 0,\n      maxResultCount: 100\n    }; // TODO: Get all results not just first 10\n    this.loading = false;\n    this.relevantActivites = [];\n    this.activites = [];\n    this.cigaOtherId = {\n      holding: \"\",\n      distribution: \"\",\n      ip: \"\",\n      shipping: \"\",\n      headquarters: \"\",\n      finance: \"\",\n      funds: \"\",\n      insurance: \"\",\n      banking: \"\"\n    };\n    this.activityMappings = {\n      holding: \"\",\n      distribution: \"\",\n      ip: \"\",\n      shipping: \"\",\n      headquarters: \"\",\n      finance: \"\",\n      funds: \"\",\n      insurance: \"\",\n      banking: \"\"\n    };\n    this.activityActions = [];\n    this.relevantActivityNames = [];\n    this.activityCount = 0;\n    this.assumedLocalZone = 'America/New_York'; //'local'\n    /**\n     *  Work for redirect back to information-exchange dashboard, if the request came from there.\n     *  value = 'info-exchange', redirect back to /es-info-exchange\n     *  value = 'es-search', redirect back to '/es-search\n    */\n    this.from = '';\n    this.route.queryParams.subscribe(params => {\n      this.declarationId = params['declarationid'] ? params['declarationid'] : null;\n      this.entityId = params['entityid'] ? params['entityid'] : null;\n      // Assign value to assessment-selection.component.ts\n      // work for define redirection path\n      this.from = params['from'] ? params['from'] : 'es-search';\n    });\n    //this.compositeQuestionService.setCompositeQuestions();\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.loading = true;\n      _this.currentUser = _this.config.getOne(\"currentUser\");\n      yield _this.intialConfigAndSetup();\n    })();\n  }\n  setSurvey() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.viewTemplate = viewDeclarationEmpty;\n      _this2.viewRelevantTemplate = viewRelevantActivites;\n      _this2.assignedUserController();\n      _this2.mapSurveyJson();\n      const converter = new Converter();\n      _this2.survey.onTextMarkdown.add(function (survey, options) {\n        // Convert Markdown to HTML\n        let str = converter.makeHtml(options.text);\n        // Remove root paragraphs <p></p>\n        str = str.substring(3);\n        str = str.substring(0, str.length - 4);\n        // Set HTML markup to render\n        options.html = str;\n      });\n      _this2.setSurveyData();\n    })();\n  }\n  assignedUserController() {\n    const readonlyStatuses = ['Fail', 'Pass'];\n    if (this.declarationData.assessmentDetails.assignedTo === this.currentUser.id && !readonlyStatuses.includes(this.declarationData.assessmentStatus)) {\n      this.relevantActivityReadonly = false;\n    } else {\n      this.relevantActivityReadonly = true;\n    }\n  }\n  refresh(event) {\n    this.declarationData = event;\n    this.setSurvey();\n  }\n  hardRefresh(name) {\n    this.setSurvey();\n    this.survey.getQuestionByName(name).focus();\n  }\n  formatDate(dateStr) {\n    return DateHelper.formatEstUtcDate(dateStr, 'yyyy-MM-dd');\n  }\n  setSurveyData() {\n    if (this.declarationId) {\n      // if declaration ID then go get that and set survey\n      for (const key in this.declarationData.surveyData) {\n        if (this.declarationData.surveyData[key]) {\n          // dates need to be set up by trimming the extra stuff off\n          if (key === \"financialPeriodStartDate\") {\n            this.declarationData.surveyData[key] = this.formatDate(this.declarationData.surveyData[key]);\n          }\n          if (key === \"financialPeriodEndDate\") {\n            this.declarationData.surveyData[key] = this.formatDate(this.declarationData.surveyData[key]);\n          }\n          if (this.compositeKeys.includes(key)) {\n            if (key === \"holdingBusinessQuestions\") {\n              this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"] = this.formatDate(this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"]);\n              this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"] = this.formatDate(this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"]);\n            }\n            if (key === \"intellectualPropertyBusiness\") {\n              this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"] = this.formatDate(this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"]);\n              this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"] = this.formatDate(this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"]);\n            }\n            if (key !== \"intellectualPropertyBusiness\" && key !== \"holdingBusinessQuestions\") {\n              // Other activities\n              this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"] = this.formatDate(this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"]);\n              this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"] = this.formatDate(this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"]);\n            }\n          }\n        }\n      }\n      this.survey.data = JSON.parse(JSON.stringify(this.declarationData.surveyData));\n      this.mapData();\n    }\n  }\n  onDownloadFile(options) {\n    if (options != null && options.fileValue) {\n      var count = 1;\n      var fileList = [];\n      // for(const f of options.fileValue) {\n      var f = options.fileValue;\n      var docType = options.question.name;\n      var header = this.GetHeader(options.fileValue);\n      let isImport = false;\n      if (docType) {\n        const fileUploadLocation = options?.name;\n        const spinnerName = fileUploadLocation + \"Spinner\";\n        this.declarationService.downloadCADeclarationDocument(this.declarationId, f.name, docType, isImport).pipe(finalize(() => {})).subscribe(result => {\n          var t = result;\n          options.callback(\"success\", header + result);\n        });\n      }\n      // }\n    }\n  }\n  GetHeader(fileValue) {\n    var fileName = fileValue.name;\n    var split = fileName.split('.');\n    var extension = split[split.length - 1];\n    switch (extension.toLowerCase()) {\n      case \"png\":\n        {\n          return \"data:image/png;base64,\";\n        }\n      case \"bmp\":\n        {\n          return \"data:image/bmp;base64,\";\n        }\n      case \"jpg\":\n        {\n          return \"data:image/jpeg;base64,\";\n        }\n      case \"jpeg\":\n        {\n          return \"data:image/jpeg;base64,\";\n        }\n      case \"pdf\":\n        {\n          return \"data:application/pdf;base64,\";\n        }\n    }\n    return \"\";\n  }\n  isValidFileType(fileType) {\n    switch (fileType.toLowerCase()) {\n      case \"png\":\n        {\n          return true;\n        }\n      case \"bmp\":\n        {\n          return true;\n        }\n      case \"jpg\":\n        {\n          return true;\n        }\n      case \"jpeg\":\n        {\n          return true;\n        }\n      case \"pdf\":\n        {\n          return true;\n        }\n    }\n    return false;\n  }\n  mapSurveyJson() {\n    const relevantActivitesFormated = this.relevantActivites.map(activity => activity.toLowerCase());\n    this.viewTemplate.pages[0].elements.forEach(element => {\n      this.activeTemplate?.pages.forEach(page => {\n        if (element.name === page.name) {\n          if (page.name === 'activityDetail') {\n            let tempPages = [];\n            page.elements[0].elements.forEach(e => {\n              e.width = '100%';\n              if (relevantActivitesFormated.includes(e.title.toLowerCase())) {\n                this.relevantActivityNames.push(e.name + 'dropdown');\n                tempPages.push({\n                  type: \"dropdown\",\n                  name: e.name + 'dropdown',\n                  title: e.title + ' Assessment Action',\n                  choices: [\"Pass\", \"Fail\"],\n                  elements: null,\n                  columns: null,\n                  choicesByUrl: null,\n                  options: null,\n                  validators: null,\n                  readOnly: this.relevantActivityReadonly,\n                  hideNumber: true,\n                  startWithNewLine: false\n                });\n              }\n              tempPages.push(e);\n            });\n            element.elements = tempPages;\n          } else {\n            element.elements = page.elements;\n          }\n        }\n      });\n    });\n    this.activityCount = this.relevantActivityNames.length;\n    //console.log(this.activityCount)\n    if (this.viewTemplate && this.viewTemplate.pages && this.viewTemplate.pages.length > 0) {\n      this.viewTemplate.pages[0].title = 'ES DECLARATION SUCCESSFULLY SUBMITTED AT ' + this.datePipe.transform(this.declarationData.submittedDate, 'yyyy-MM-dd, h:mm:ss a', 'local');\n      //disable all elements\n      if (this.viewTemplate.pages[0].elements && this.viewTemplate.pages[0].elements.length > 0) {\n        this.viewTemplate.pages[0].elements.forEach(e1 => {\n          if (e1.elements && e1.elements.length > 0) {\n            e1.elements.forEach(e2 => {\n              if (e2.type != 'dropdown') e2.enableIf = 'false';\n            });\n          }\n        });\n      }\n    }\n    Serializer.addProperty(\"question\", {\n      name: \"inputWidth\",\n      type: \"string\"\n    });\n    this.survey = new Model(this.viewTemplate);\n    this.survey.applyTheme(themeJson);\n    //this.compositeQuestionService.setupCustomSurveyFields(this.survey);\n    this.survey.showNavigationButtons = false;\n    this.survey.onAfterRenderSurvey.add((sender, options) => {\n      this.saveRelevantActivityDecision(sender, options);\n    });\n    this.survey.onAfterRenderQuestionInput.add(function (sender, options) {\n      if (!options.question.inputWidth) return;\n      options.htmlElement.style.width = options.question.inputWidth;\n    });\n    this.survey.onValueChanged.add((sender, options) => {\n      this.survey.getQuestionByName(options.name).enableIf = 'false';\n      if (this.relevantActivityNames.includes(options.name) && options.value) {\n        //confirmation dialog\n        Swal.fire({\n          title: 'Relevant Activity Decision',\n          text: \"Are you sure you want to save this decision?\",\n          icon: 'warning',\n          showCancelButton: true,\n          confirmButtonColor: '#3085d6',\n          cancelButtonColor: '#d33'\n        }).then(result => {\n          if (result.isConfirmed) {\n            var decision = {\n              relevantActivity: this.getActivityEnum(options.name),\n              pass: options.value.toLowerCase() == \"pass\"\n            };\n            this.assessmentService.relevantActivityDecisionByDeclarationIdAndDecision(this.declarationId, decision).subscribe(result => {\n              if (result) {\n                this.declarationData = result;\n                this.saveRelevantActivityDecision(sender, options);\n                this.toasterService.success('Relevant Activity Decision saved');\n                this.hardRefresh(options.name);\n              }\n            });\n          } else {\n            options.question.clearValue();\n            this.hardRefresh(options.name);\n            //after the clear, the dropdown sometimes hangs around the top left of the page for some reason.\n          }\n        });\n      } else {\n        this.survey.getQuestionByName(options.name).enableIf = 'true';\n        this.saveRelevantActivityDecision(sender, options);\n      }\n    });\n    this.survey.onDownloadFile.add((_, options) => {\n      this.onDownloadFile(options);\n    });\n  }\n  saveRelevantActivityDecision(sender, options) {\n    let tempActivtyActions = [];\n    this.relevantActivityNames.forEach(name => {\n      sender.data[name] && tempActivtyActions.push({\n        name,\n        value: sender.data[name]\n      });\n    });\n    if (!options.name || options.name != 'relevantActRelevantActivities') {\n      this.activityActions = tempActivtyActions;\n    }\n  }\n  mapData() {\n    // mapping from id to name\n    if (this.activites) {\n      if (this.survey.data['relevantActRelevantActivities']) {\n        if (this.relevantActivites && this.relevantActivites.length > 0) {\n          this.survey.setValue('relevantActRelevantActivities', this.relevantActivites, undefined, false);\n        } else {\n          this.survey.setValue('relevantActRelevantActivities', this.activites, undefined, false);\n        }\n      }\n    }\n    if (this.declarationData && this.declarationData.assessmentDetails && this.declarationData.assessmentDetails.relevantActivityDecisions) {\n      this.declarationData.assessmentDetails.relevantActivityDecisions.forEach(ra => {\n        var activityName = this.getActivityString(ra.relevantActivity);\n        this.survey.setValue(activityName, ra.pass ? \"Pass\" : \"Fail\", undefined, false);\n      });\n    }\n    this.loading = false;\n  }\n  preProcessCigaDropdown() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      ComponentCollection.Instance.items.forEach(Component => {\n        if (Component['json']['name'] === \"IntellectualPropertyBusiness\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivity') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.ip;\n              }\n              if (element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivityComment') {\n                elements['visibleIf'] = \"{IntellectualPropertyBusiness.intelPropBusCIGAInBahamasForRelevantActivity} anyof ['\" + _this3.cigaOtherId.ip + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"bankingOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.banking;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{bankingQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.banking + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"distributionOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.distribution;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{distributionQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.distribution + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"financeOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.finance;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{financeQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.finance + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"fundManagmentOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.funds;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{fundManagmentQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.funds + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"headquartersOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.headquarters;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{headquartersQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.headquarters + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"insuranceOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.insurance;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{insuranceQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.insurance + \"']\";\n              }\n            }\n          });\n        }\n        if (Component['json']['name'] === \"shippingOtherRelevantActivities\") {\n          Component['json']['elementsJSON'].forEach(elements => {\n            for (const element in elements) {\n              // properties of each question\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA') {\n                elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", _this3.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + _this3.activityMappings.shipping;\n              }\n              if (element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment') {\n                elements['visibleIf'] = \"{shippingQuestions.otherRelevantActivitiesCIGA} anyof ['\" + _this3.cigaOtherId.shipping + \"']\";\n              }\n            }\n          });\n        }\n      });\n    })();\n  }\n  getNeededData() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.activites = _this4.declarationData.surveyData[\"relevantActRelevantActivities\"];\n      if (_this4.activites) {\n        _this4.relevantActivityList.items.forEach(element => {\n          _this4.activites.forEach(activity => {\n            if (element.id === activity) {\n              _this4.relevantActivites.push(element.name);\n            }\n          });\n        });\n      }\n      _this4.setSurvey();\n    })();\n  }\n  intialConfigAndSetup() {\n    const configData = this.configState.getAll();\n    if (configData.currentTenant.isAvailable) {\n      this.tenantName = configData.currentTenant.name;\n    }\n    let cigaObservable = this.cigaLookup.getList({\n      maxResultCount: 100\n    });\n    let templateObservable = this.service.getList(this.templateListDto);\n    let activityObservable = this.activityLookup.getList({\n      maxResultCount: 100\n    });\n    let declarationObservable = this.declarationService.getCADeclarationByDeclarationId(this.declarationId);\n    let observableList = [cigaObservable, templateObservable, activityObservable, declarationObservable];\n    forkJoin(observableList).subscribe(result => {\n      const cigaData = result[0].items;\n      const templateData = result[1].items;\n      this.relevantActivityList = result[2];\n      this.declarationData = result[3];\n      //console.log('declarationData', this.declarationData);\n      this.relevantActivityList.items.forEach(element => {\n        if (element.name === \"Holding business\") this.activityMappings.holding = element.id;\n        if (element.name === \"Distribution and service centre business\") this.activityMappings.distribution = element.id;\n        if (element.name === \"Intellectual property business\") this.activityMappings.ip = element.id;\n        if (element.name === \"Shipping business\") this.activityMappings.shipping = element.id;\n        if (element.name === \"Headquarters business\") this.activityMappings.headquarters = element.id;\n        if (element.name === \"Finance and leasing business\") this.activityMappings.finance = element.id;\n        if (element.name === \"Fund management business\") this.activityMappings.funds = element.id;\n        if (element.name === \"Insurance business\") this.activityMappings.insurance = element.id;\n        if (element.name === \"Banking business\") this.activityMappings.banking = element.id;\n      });\n      cigaData.forEach(item => {\n        if (item.name.includes('(please specify)')) {\n          if (item.relevantActivityId === this.activityMappings.holding) this.cigaOtherId.holding = item.id;\n          if (item.relevantActivityId === this.activityMappings.distribution) this.cigaOtherId.distribution = item.id;\n          if (item.relevantActivityId === this.activityMappings.ip) this.cigaOtherId.ip = item.id;\n          if (item.relevantActivityId === this.activityMappings.shipping) this.cigaOtherId.shipping = item.id;\n          if (item.relevantActivityId === this.activityMappings.headquarters) this.cigaOtherId.headquarters = item.id;\n          if (item.relevantActivityId === this.activityMappings.finance) this.cigaOtherId.finance = item.id;\n          if (item.relevantActivityId === this.activityMappings.funds) this.cigaOtherId.funds = item.id;\n          if (item.relevantActivityId === this.activityMappings.insurance) this.cigaOtherId.insurance = item.id;\n          if (item.relevantActivityId === this.activityMappings.banking) this.cigaOtherId.banking = item.id;\n        }\n      });\n      var activeTemplate;\n      if (this.declarationData && this.declarationData.declarationTemplateId) {\n        activeTemplate = templateData.find(x => x.id == this.declarationData.declarationTemplateId);\n      } else {\n        activeTemplate = templateData.find(element => element.isActive);\n      }\n      if (activeTemplate) {\n        this.activeTemplate = activeTemplate.survey;\n        this.templateId = activeTemplate.id;\n      }\n      this.compositeQuestionService.setCompositeQuestions(activeTemplate);\n      this.setUrls(this.activeTemplate);\n      this.preProcessCigaDropdown();\n      this.getNeededData();\n    }, error => {\n      this.loading = false;\n      console.log(\"intialConfigAndSetup Error:\", error);\n    });\n  }\n  setUrls(SurveyTemplate) {\n    for (const key in SurveyTemplate) {\n      for (const key2 in SurveyTemplate[key]) {\n        for (const key3 in SurveyTemplate[key][key2]) {\n          for (const key4 in SurveyTemplate[key][key2][key3]) {\n            for (const key5 in SurveyTemplate[key][key2][key3][key4]) {\n              for (const key6 in SurveyTemplate[key][key2][key3][key4][key5]) {\n                for (const key7 in SurveyTemplate[key][key2][key3][key4][key5][key6]) {\n                  if (key7 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7] !== null) {\n                    // choices by url is on the question level example is relevant activity drop down\n                    SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url.replace(\"{0}\", this.tenantName);\n                    console.log(key7, SurveyTemplate[key][key2][key3][key4][key5][key6][key7]);\n                  }\n                  for (const key8 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7]) {\n                    for (const key9 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8]) {\n                      if (key9 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9] !== null) {\n                        // choices by url is on a column level example entity details differnt business address country drop down\n                        SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url.replace(\"{0}\", this.tenantName);\n                        console.log(key9, SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9]);\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  getActivityEnum(name) {\n    switch (name) {\n      case \"intellectualPropertyBusinessdropdown\":\n        return RelevantActivityType.IntellectualProperty;\n      case \"holdingBusinessQuestionsdropdown\":\n        return RelevantActivityType.Holding;\n      case \"bankingQuestionsdropdown\":\n        return RelevantActivityType.Banking;\n      case \"insuranceQuestionsdropdown\":\n        return RelevantActivityType.Insurance;\n      case \"fundManagmentQuestionsdropdown\":\n        return RelevantActivityType.FundManagement;\n      case \"financeQuestionsdropdown\":\n        return RelevantActivityType.FinanceAndLeasing;\n      case \"headquartersQuestionsdropdown\":\n        return RelevantActivityType.Headquarter;\n      case \"shippingQuestionsdropdown\":\n        return RelevantActivityType.Shipping;\n      case \"distributionQuestionsdropdown\":\n        return RelevantActivityType.Distribution;\n      default:\n        return RelevantActivityType.NA;\n    }\n  }\n  getActivityString(activity) {\n    switch (activity) {\n      case RelevantActivityType.IntellectualProperty:\n        return \"intellectualPropertyBusinessdropdown\";\n      case RelevantActivityType.Holding:\n        return \"holdingBusinessQuestionsdropdown\";\n      case RelevantActivityType.Banking:\n        return \"bankingQuestionsdropdown\";\n      case RelevantActivityType.Insurance:\n        return \"insuranceQuestionsdropdown\";\n      case RelevantActivityType.FundManagement:\n        return \"fundManagmentQuestionsdropdown\";\n      case RelevantActivityType.FinanceAndLeasing:\n        return \"financeQuestionsdropdown\";\n      case RelevantActivityType.Headquarter:\n        return \"headquartersQuestionsdropdown\";\n      case RelevantActivityType.Shipping:\n        return \"shippingQuestionsdropdown\";\n      case RelevantActivityType.Distribution:\n        return \"distributionQuestionsdropdown\";\n      default:\n        return \"\";\n    }\n  }\n  static {\n    this.ɵfac = function CaActionPageComponent_Factory(t) {\n      return new (t || CaActionPageComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.DeclarationService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.DatePipe), i0.ɵɵdirectiveInject(i4.RelevantActivityService), i0.ɵɵdirectiveInject(i5.CompositeQuestionService), i0.ɵɵdirectiveInject(i6.TemplateService), i0.ɵɵdirectiveInject(i4.CigaService), i0.ɵɵdirectiveInject(i7.AssessmentService), i0.ɵɵdirectiveInject(i8.SweetAlertService), i0.ɵɵdirectiveInject(i9.ToasterService), i0.ɵɵdirectiveInject(i10.ConfigStateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CaActionPageComponent,\n      selectors: [[\"app-ca-action-page\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[\"class\", \"spinner\", 4, \"ngIf\"], [3, \"activityActions\", \"activityCount\", \"declarationData\", \"from\", \"refreshData\", 4, \"ngIf\"], [\"type\", \"CA\", 3, \"declarationData\", \"declarationId\", 4, \"ngIf\"], [\"id\", \"survey\", 4, \"ngIf\"], [1, \"spinner\"], [1, \"mat-spinner-color\", 3, \"mode\", \"diameter\"], [3, \"refreshData\", \"activityActions\", \"activityCount\", \"declarationData\", \"from\"], [\"type\", \"CA\", 3, \"declarationData\", \"declarationId\"], [\"id\", \"survey\"], [\"type\", \"CA\", \"isViewMode\", \"true\", 3, \"entityId\"], [3, \"model\", 4, \"ngIf\"], [3, \"model\"]],\n      template: function CaActionPageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CaActionPageComponent_div_0_Template, 2, 2, \"div\", 0)(1, CaActionPageComponent_app_assesment_selection_1_Template, 1, 4, \"app-assesment-selection\", 1)(2, CaActionPageComponent_app_assessment_action_view_2_Template, 1, 2, \"app-assessment-action-view\", 2)(3, CaActionPageComponent_div_3_Template, 3, 2, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.declarationData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.declarationData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.entityId && ctx.declarationData);\n        }\n      },\n      dependencies: [i11.MatProgressSpinner, i3.NgIf, i12.SurveyComponent, i13.DeclarationEntityDetailsComponent, i14.AssessmentActionViewComponent, i15.AssesmentSelectionComponent],\n      styles: [\".spinner[_ngcontent-%COMP%] {\\n  position: relative;\\n  left: 45%;\\n  top: 6em;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImNhLWFjdGlvbi1wYWdlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usa0JBQUE7RUFDQSxTQUFBO0VBQ0EsUUFBQTtBQUNGIiwiZmlsZSI6ImNhLWFjdGlvbi1wYWdlLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLnNwaW5uZXJ7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIGxlZnQ6IDQ1JTtcclxuICB0b3A6IDZlbTtcclxuXHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvY2EtYWN0aW9uLXBhZ2UvY29udGFpbmVycy9hY3Rpb24tcGFnZS9jYS1hY3Rpb24tcGFnZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFFBQUE7QUFDRjtBQUNBLGdaQUFnWiIsInNvdXJjZXNDb250ZW50IjpbIi5zcGlubmVye1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICBsZWZ0OiA0NSU7XHJcbiAgdG9wOiA2ZW07XHJcblxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Date<PERSON>elper", "AppComponentBase", "themeJson", "viewDeclarationEmpty", "viewRelevantActivites", "environment", "finalize", "fork<PERSON><PERSON>n", "ComponentCollection", "Model", "Serializer", "RelevantActivityType", "<PERSON><PERSON>", "Converter", "AngularComponentFactory", "SurveyFilePreviewComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵlistener", "CaActionPageComponent_app_assesment_selection_1_Template_app_assesment_selection_refreshData_0_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "refresh", "activityActions", "activityCount", "declarationData", "from", "id", "survey", "ɵɵtemplate", "CaActionPageComponent_div_3_survey_2_Template", "entityId", "Instance", "registerComponent", "CaActionPageComponent", "constructor", "injector", "declarationService", "route", "datePipe", "activityLookup", "compositeQuestionService", "service", "cigaLookup", "assessmentService", "<PERSON><PERSON><PERSON><PERSON>", "toasterService", "config", "compositeKeys", "templateListDto", "sorting", "skip<PERSON><PERSON>nt", "maxResultCount", "loading", "relevantActivites", "activites", "cigaOtherId", "holding", "distribution", "ip", "shipping", "headquarters", "finance", "funds", "insurance", "banking", "activityMappings", "relevantActivityNames", "assumedLocalZone", "queryParams", "subscribe", "params", "declarationId", "ngOnInit", "_this", "_asyncToGenerator", "currentUser", "getOne", "intialConfigAndSetup", "<PERSON><PERSON><PERSON><PERSON>", "_this2", "viewTemplate", "viewRelevantTemplate", "assignedUserController", "mapSurvey<PERSON>son", "converter", "onTextMarkdown", "add", "options", "str", "makeHtml", "text", "substring", "length", "html", "setSurveyData", "readonlyStatuses", "assessmentDetails", "assignedTo", "includes", "assessmentStatus", "relevantActivityReadonly", "event", "hardRefresh", "name", "getQuestionByName", "focus", "formatDate", "dateStr", "formatEstUtcDate", "key", "surveyData", "data", "JSON", "parse", "stringify", "mapData", "onDownloadFile", "fileValue", "count", "fileList", "f", "docType", "question", "header", "GetH<PERSON>er", "isImport", "fileUploadLocation", "spinnerName", "downloadCADeclarationDocument", "pipe", "result", "t", "callback", "fileName", "split", "extension", "toLowerCase", "isValidFileType", "fileType", "relevantActivitesFormated", "map", "activity", "pages", "elements", "for<PERSON>ach", "element", "activeTemplate", "page", "tempPages", "e", "width", "title", "push", "type", "choices", "columns", "choicesByUrl", "validators", "readOnly", "hideNumber", "startWithNewLine", "transform", "submittedDate", "e1", "e2", "enableIf", "addProperty", "applyTheme", "showNavigationButtons", "onAfterRender<PERSON><PERSON><PERSON>", "sender", "saveRelevantActivityDecision", "onAfterRenderQuestionInput", "inputWidth", "htmlElement", "style", "onValueChanged", "value", "fire", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "then", "isConfirmed", "decision", "relevantActivity", "getActivityEnum", "pass", "relevantActivityDecisionByDeclarationIdAndDecision", "success", "clearValue", "_", "tempActivtyActions", "setValue", "undefined", "relevantActivityDecisions", "ra", "activityName", "getActivityString", "preProcessCigaDropdown", "_this3", "items", "Component", "replace", "tenantName", "getNeededData", "_this4", "relevantActivityList", "configData", "configState", "getAll", "currentTenant", "isAvailable", "cigaObservable", "getList", "templateObservable", "activityObservable", "declarationObservable", "getCADeclarationByDeclarationId", "observableList", "cigaData", "templateData", "item", "relevantActivityId", "declarationTemplateId", "find", "x", "isActive", "templateId", "setCompositeQuestions", "setUrls", "error", "console", "log", "SurveyTemplate", "key2", "key3", "key4", "key5", "key6", "key7", "url", "key8", "key9", "IntellectualProperty", "Holding", "Banking", "Insurance", "FundManagement", "FinanceAndLeasing", "Headquarter", "Shipping", "Distribution", "NA", "ɵɵdirectiveInject", "Injector", "i1", "DeclarationService", "i2", "ActivatedRoute", "i3", "DatePipe", "i4", "RelevantActivityService", "i5", "CompositeQuestionService", "i6", "TemplateService", "CigaService", "i7", "AssessmentService", "i8", "SweetAlertService", "i9", "ToasterService", "i10", "ConfigStateService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "CaActionPageComponent_Template", "rf", "ctx", "CaActionPageComponent_div_0_Template", "CaActionPageComponent_app_assesment_selection_1_Template", "CaActionPageComponent_app_assessment_action_view_2_Template", "CaActionPageComponent_div_3_Template"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\ca-action-page\\containers\\action-page\\ca-action-page.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\ca-action-page\\containers\\action-page\\ca-action-page.component.html"], "sourcesContent": ["import { DatePipe } from '@angular/common';\r\nimport { DateHelper } from '@app/shared/utils/date-helper';\r\nimport { Component, Injector, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { AppComponentBase } from '@app/app-component-base';\r\nimport { themeJson } from '@app/features/es-declaration/containers/es-declaration/survey-theme-json';\r\nimport { viewDeclarationEmpty, viewRelevantActivites } from '@app/shared/declaration-jsons/view-declaration-json';\r\nimport { CompositeQuestionService } from '@app/shared/services/composite-question.service';\r\nimport {  CigaService, RelevantActivityDto, RelevantActivityService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';\r\nimport { SweetAlertService } from '@app/shared/services/sweetalert.service';\r\nimport { environment } from '@environments/environment';\r\nimport { DeclartionDocumentType } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declaration-imports';\r\nimport { AssessmentActivityDecisions, DeclarationAssessmentDto, DeclarationDto, DeclarationService, UploadDeclarationDocumentDto } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/declarations';\r\nimport { GetTemplateListDto, SurveyDto, TemplateService } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/templates';\r\nimport { Observable, finalize, forkJoin } from 'rxjs';\r\nimport { ComponentCollection, Model, Serializer } from \"survey-core\";\r\nimport { RelevantActivityType } from 'proxies/economic-service/lib/proxy/bdo/ess/shared/hosting/microservices/eto/declaration/base-classes';\r\nimport { ConfigStateService, PagedResultDto } from '@abp/ng.core';\r\nimport Swal from 'sweetalert2';\r\nimport { AssessmentService } from 'proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/assessment';\r\nimport { ToasterService } from '@abp/ng.theme.shared';\r\nimport { Converter } from \"showdown\";\r\nimport { AngularComponentFactory } from 'survey-angular-ui';\r\nimport { SurveyFilePreviewComponent } from '@app/shared/components/survey-file-preview/survey-file-preview.component';\r\n\r\nAngularComponentFactory.Instance.registerComponent(\"sv-file-preview\", SurveyFilePreviewComponent)\r\n@Component({\r\n  selector: 'app-ca-action-page',\r\n  templateUrl: './ca-action-page.component.html',\r\n  styleUrls: ['./ca-action-page.component.scss']\r\n})\r\nexport class CaActionPageComponent extends AppComponentBase implements OnInit {\r\n\r\n  constructor(injector: Injector,\r\n    private declarationService: DeclarationService,\r\n    private route: ActivatedRoute,\r\n    private datePipe: DatePipe,\r\n    private activityLookup: RelevantActivityService,\r\n    private compositeQuestionService: CompositeQuestionService,\r\n    private service: TemplateService,\r\n    private cigaLookup: CigaService,\r\n    private assessmentService:AssessmentService,\r\n    private sweetAlert: SweetAlertService,\r\n    private toasterService: ToasterService,\r\n    private config: ConfigStateService,\r\n  ){\r\n    super(injector)\r\n    this.route.queryParams.subscribe(params => {\r\n      this.declarationId = params['declarationid'] ? params['declarationid'] : null;\r\n      this.entityId =  params['entityid'] ? params['entityid']: null ;\r\n      // Assign value to assessment-selection.component.ts\r\n      // work for define redirection path\r\n      this.from = params['from']? params['from']: 'es-search';\r\n    });\r\n    //this.compositeQuestionService.setCompositeQuestions();\r\n  }\r\n\r\n  declarationId: string;\r\n  /** Get value from url paramter 'entityid', then assign value to child component app-declaration-entity-details  */\r\n  entityId: string\r\n  declarationData:DeclarationAssessmentDto;\r\n  viewTemplate: SurveyDto;\r\n  viewRelevantTemplate: SurveyDto\r\n  activeTemplate: SurveyDto\r\n  survey: Model;\r\n  compositeKeys: string[] = [\"intellectualPropertyBusiness\",\"bankingQuestions\", \"distributionQuestions\", \"financeQuestions\", \"fundManagmentQuestions\",\r\n  \"headquartersQuestions\", \"holdingBusinessQuestions\", \"insuranceQuestions\",\"outsourcingIntellectualPropertyBusiness\", \"shippingQuestions\"]\r\n  templateListDto: GetTemplateListDto = {sorting: \"id\", skipCount:0, maxResultCount: 100} // TODO: Get all results not just first 10\r\n  templateId:string;\r\n  loading = false;\r\n  relevantActivites = [];\r\n  activites = [];\r\n  tenantName: string;\r\n  cigaOtherId = {holding: \"\", distribution: \"\", ip:\"\", shipping: \"\",  headquarters:\"\", finance: \"\",  funds:\"\", insurance: \"\", banking:\"\"  };\r\n  activityMappings = {holding: \"\", distribution: \"\", ip:\"\", shipping: \"\",  headquarters:\"\", finance: \"\",  funds:\"\", insurance: \"\", banking:\"\"  };\r\n  activityActions = []\r\n  relevantActivityNames = []\r\n  activityCount = 0\r\n  relevantActivityList:PagedResultDto<RelevantActivityDto>;\r\n  relevantActivityReadonly: boolean\r\n  assumedLocalZone = 'America/New_York'; //'local'\r\n  /**\r\n   *  Work for redirect back to information-exchange dashboard, if the request came from there.\r\n   *  value = 'info-exchange', redirect back to /es-info-exchange\r\n   *  value = 'es-search', redirect back to '/es-search\r\n  */\r\n  from = '';\r\n  private currentUser\r\n\r\n  async ngOnInit(): Promise<void> {\r\n    this.loading = true\r\n    this.currentUser = this.config.getOne(\"currentUser\");\r\n    await this.intialConfigAndSetup();\r\n  }\r\n\r\n\r\n  async setSurvey(): Promise<void>{\r\n    this.viewTemplate = viewDeclarationEmpty\r\n    this.viewRelevantTemplate = viewRelevantActivites\r\n    this.assignedUserController()\r\n    this.mapSurveyJson()\r\n    const converter = new Converter();\r\n    this.survey.onTextMarkdown.add(function (survey, options) {\r\n      // Convert Markdown to HTML\r\n      let str = converter.makeHtml(options.text);\r\n      // Remove root paragraphs <p></p>\r\n      str = str.substring(3);\r\n      str = str.substring(0, str.length - 4);\r\n      // Set HTML markup to render\r\n      options.html = str;\r\n    });\r\n    this.setSurveyData()\r\n  }\r\n\r\n  assignedUserController(){\r\n    const readonlyStatuses = ['Fail', 'Pass']\r\n    if (this.declarationData.assessmentDetails.assignedTo === this.currentUser.id && !readonlyStatuses.includes(this.declarationData.assessmentStatus)){\r\n      this.relevantActivityReadonly = false\r\n    }else{\r\n      this.relevantActivityReadonly = true\r\n    }\r\n\r\n  }\r\n\r\n\r\n  refresh(event){\r\n    this.declarationData = event\r\n    this.setSurvey()\r\n  }\r\n\r\n  hardRefresh(name: string){\r\n\r\n    this.setSurvey()\r\n    this.survey.getQuestionByName(name).focus()\r\n  }\r\n  formatDate(dateStr: string): string {\r\n    return DateHelper.formatEstUtcDate(dateStr,'yyyy-MM-dd');\r\n  }\r\n   setSurveyData(){\r\n    if(this.declarationId){// if declaration ID then go get that and set survey\r\n      for(const key in this.declarationData.surveyData){\r\n        if(this.declarationData.surveyData[key]){ // dates need to be set up by trimming the extra stuff off\r\n          if(key === \"financialPeriodStartDate\"){\r\n            this.declarationData.surveyData[key] = this.formatDate(this.declarationData.surveyData[key]);\r\n          }\r\n          if(key === \"financialPeriodEndDate\") {\r\n            this.declarationData.surveyData[key] = this.formatDate(this.declarationData.surveyData[key]);\r\n          }\r\n\r\n          if(this.compositeKeys.includes(key)){\r\n            if(key === \"holdingBusinessQuestions\") {\r\n              this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"] = this.formatDate(this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodStartDate\"]);\r\n              this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"] = this.formatDate(this.declarationData.surveyData[key][\"holdingBussinessPartOfFinancialPeriodEndDate\"]);\r\n            }\r\n            if(key === \"intellectualPropertyBusiness\"){\r\n              this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"] = this.formatDate(this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodStartDate\"]);\r\n              this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"] = this.formatDate(this.declarationData.surveyData[key][\"intelPropPartOfFinancialPeriodEndDate\"]);\r\n            }\r\n            if(key !== \"intellectualPropertyBusiness\" && key !== \"holdingBusinessQuestions\"){ // Other activities\r\n              this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"] = this.formatDate(this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodStartDate\"]);\r\n              this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"] = this.formatDate(this.declarationData.surveyData[key][\"otherRelevantActivitiesPartOfFinancialPeriodEndDate\"]);\r\n            }\r\n          }\r\n\r\n        }\r\n      }\r\n      this.survey.data = JSON.parse(JSON.stringify(this.declarationData.surveyData));\r\n      this.mapData();\r\n    }\r\n  }\r\n\r\n  onDownloadFile(options:any) {\r\n    if(options != null && options.fileValue)\r\n    {\r\n      var count = 1;\r\n      var fileList = [];\r\n      // for(const f of options.fileValue) {\r\n        var f = options.fileValue;\r\n        var docType = options.question.name as DeclartionDocumentType;\r\n        var header = this.GetHeader(options.fileValue);\r\n        let isImport = false;\r\n        if(docType){\r\n          const fileUploadLocation = options?.name;\r\n          const spinnerName = fileUploadLocation + \"Spinner\";\r\n          this.declarationService.downloadCADeclarationDocument(this.declarationId, f.name, docType, isImport).pipe(\r\n            finalize(()=>{\r\n            })\r\n          )\r\n          .subscribe((result)=> {\r\n            var t = result;\r\n            options.callback(\"success\", header + result);\r\n          });\r\n        }\r\n      // }\r\n    }\r\n  }\r\n  GetHeader(fileValue: any) {\r\n    var fileName = fileValue.name;\r\n    var split = fileName.split('.');\r\n    var extension = split[split.length-1];\r\n    switch(extension.toLowerCase())\r\n    {\r\n      case \"png\": {\r\n        return \"data:image/png;base64,\";\r\n      }\r\n      case \"bmp\": {\r\n        return \"data:image/bmp;base64,\";\r\n      }\r\n      case \"jpg\": {\r\n        return \"data:image/jpeg;base64,\";\r\n      }\r\n      case \"jpeg\": {\r\n        return \"data:image/jpeg;base64,\";\r\n      }\r\n      case \"pdf\": {\r\n        return \"data:application/pdf;base64,\";\r\n      }\r\n    }\r\n    return \"\";\r\n  }\r\n\r\n  isValidFileType(fileType: string):boolean{\r\n    switch(fileType.toLowerCase())\r\n    {\r\n      case \"png\": {\r\n        return true;\r\n      }\r\n      case \"bmp\": {\r\n        return true;\r\n      }\r\n      case \"jpg\": {\r\n        return true;\r\n      }\r\n      case \"jpeg\": {\r\n        return true;\r\n      }\r\n      case \"pdf\": {\r\n        return true;\r\n      }\r\n    }\r\n    return false;\r\n  }\r\n\r\n  mapSurveyJson(){\r\n    const relevantActivitesFormated = this.relevantActivites.map(activity=>activity.toLowerCase())\r\n    this.viewTemplate.pages[0].elements.forEach(element => {\r\n      this.activeTemplate?.pages.forEach(page=>{\r\n            if(element.name === page.name) {\r\n              if (page.name === 'activityDetail'){\r\n                let tempPages = [];\r\n\r\n                page.elements[0].elements.forEach((e)=>{\r\n                  e.width = '100%'\r\n                  if (relevantActivitesFormated.includes(e.title.toLowerCase())) {\r\n                    this.relevantActivityNames.push(e.name +'dropdown')\r\n                    tempPages.push({\r\n                      type: \"dropdown\",\r\n                      name: e.name +'dropdown',\r\n                      title: e.title + ' Assessment Action',\r\n                      choices: [\"Pass\",\"Fail\"],\r\n                      elements: null,\r\n                      columns: null,\r\n                      choicesByUrl: null,\r\n                      options: null,\r\n                      validators: null,\r\n                      readOnly: this.relevantActivityReadonly,\r\n                      hideNumber: true,\r\n                      startWithNewLine:false\r\n                    })\r\n                  }\r\n                  tempPages.push(e)\r\n                })\r\n                element.elements = tempPages\r\n              }else{\r\n                element.elements = page.elements;\r\n              }\r\n            }\r\n        });\r\n    });\r\n\r\n    this.activityCount = this.relevantActivityNames.length\r\n\r\n    //console.log(this.activityCount)\r\n\r\n   if (this.viewTemplate && this.viewTemplate.pages && this.viewTemplate.pages.length>0){\r\n    this.viewTemplate.pages[0].title = 'ES DECLARATION SUCCESSFULLY SUBMITTED AT ' + this.datePipe.transform(this.declarationData.submittedDate,'yyyy-MM-dd, h:mm:ss a', 'local');\r\n    //disable all elements\r\n    if (this.viewTemplate.pages[0].elements && this.viewTemplate.pages[0].elements.length>0) {\r\n      this.viewTemplate.pages[0].elements.forEach(e1 => {\r\n        if (e1.elements && e1.elements.length>0) {\r\n          e1.elements.forEach(e2 => {\r\n          if ( e2.type!='dropdown') e2.enableIf = 'false'\r\n          });\r\n        }\r\n      })\r\n     }\r\n  }\r\n   \r\n    Serializer.addProperty(\"question\", {\r\n      name: \"inputWidth\",\r\n      type: \"string\"         \r\n    });\r\n    this.survey = new Model(this.viewTemplate);\r\n    this.survey.applyTheme(themeJson);\r\n    //this.compositeQuestionService.setupCustomSurveyFields(this.survey);\r\n    this.survey.showNavigationButtons = false;\r\n    this.survey.onAfterRenderSurvey.add((sender,options)=>{\r\n      this.saveRelevantActivityDecision(sender,options)\r\n    })\r\n\r\n    this.survey.onAfterRenderQuestionInput.add(function(sender, options){\r\n      if(!options.question.inputWidth) return;\r\n      options.htmlElement.style.width = options.question.inputWidth;\r\n    });\r\n\r\n    this.survey.onValueChanged.add((sender, options)=>{\r\n\r\n      this.survey.getQuestionByName(options.name).enableIf = 'false'\r\n      if(this.relevantActivityNames.includes(options.name) && options.value)\r\n      {\r\n        //confirmation dialog\r\n        Swal.fire({\r\n          title: 'Relevant Activity Decision',\r\n          text: \"Are you sure you want to save this decision?\",\r\n          icon: 'warning',\r\n          showCancelButton: true,\r\n          confirmButtonColor: '#3085d6',\r\n          cancelButtonColor: '#d33'\r\n        }).then((result) => {\r\n          if(result.isConfirmed)\r\n          {\r\n              var decision = {\r\n                relevantActivity: this.getActivityEnum(options.name),\r\n                pass: options.value.toLowerCase() == \"pass\"\r\n              } as AssessmentActivityDecisions;\r\n              this.assessmentService.relevantActivityDecisionByDeclarationIdAndDecision(this.declarationId, decision)\r\n              .subscribe(result => {\r\n                if(result){\r\n                  this.declarationData = result;\r\n                  this.saveRelevantActivityDecision(sender,options);\r\n                  this.toasterService.success('Relevant Activity Decision saved');\r\n                  this.hardRefresh(options.name)\r\n                }\r\n              })\r\n\r\n          } else {\r\n            options.question.clearValue();\r\n            this.hardRefresh(options.name)\r\n            //after the clear, the dropdown sometimes hangs around the top left of the page for some reason.\r\n          }\r\n        });\r\n      }else{\r\n        this.survey.getQuestionByName(options.name).enableIf = 'true';\r\n        this.saveRelevantActivityDecision(sender,options);\r\n      }\r\n\r\n    });\r\n    this.survey.onDownloadFile.add((_, options) => {\r\n      this.onDownloadFile(options);\r\n    });\r\n\r\n  }\r\n\r\n  saveRelevantActivityDecision(sender, options) {\r\n    let tempActivtyActions = []\r\n      this.relevantActivityNames.forEach(name=>{\r\n        sender.data[name] && tempActivtyActions.push({\r\n          name,\r\n          value: sender.data[name]\r\n        })\r\n        })\r\n        if (!options.name || options.name != 'relevantActRelevantActivities'){\r\n          this.activityActions = tempActivtyActions\r\n        }\r\n  }\r\n\r\n  mapData():void{\r\n    // mapping from id to name\r\n    if(this.activites){\r\n      if(this.survey.data['relevantActRelevantActivities']){\r\n\r\n        if(this.relevantActivites && this.relevantActivites.length >0){\r\n          this.survey.setValue('relevantActRelevantActivities', this.relevantActivites, undefined, false)\r\n        }\r\n        else {\r\n          this.survey.setValue('relevantActRelevantActivities', this.activites, undefined, false);\r\n        }\r\n      }\r\n    }\r\n    if(this.declarationData && this.declarationData.assessmentDetails && this.declarationData.assessmentDetails.relevantActivityDecisions)\r\n    {\r\n      this.declarationData.assessmentDetails.relevantActivityDecisions.forEach(ra => {\r\n        var activityName = this.getActivityString(ra.relevantActivity);\r\n          this.survey.setValue(activityName,ra.pass ? \"Pass\":\"Fail\", undefined, false);\r\n      });\r\n    }\r\n    this.loading = false\r\n  }\r\n\r\n  async preProcessCigaDropdown():Promise<void>{\r\n    ComponentCollection.Instance.items.forEach(Component => {\r\n      if(Component['json']['name'] ===\"IntellectualPropertyBusiness\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivity'){\r\n              elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.ip;\r\n            }\r\n            if(element === 'name' && elements[element] === 'intelPropBusCIGAInBahamasForRelevantActivityComment'){\r\n              elements['visibleIf'] = \"{IntellectualPropertyBusiness.intelPropBusCIGAInBahamasForRelevantActivity} anyof ['\" +this.cigaOtherId.ip + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"bankingOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n             elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.banking;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{bankingQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.banking + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"distributionOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n               elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.distribution;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{distributionQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.distribution + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"financeOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n               elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.finance;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{financeQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.finance + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"fundManagmentOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n              elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.funds;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{fundManagmentQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.funds + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"headquartersOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n               elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" +'?FilterByRelevantActivityId=' + this.activityMappings.headquarters;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{headquartersQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.headquarters + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"insuranceOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n               elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + this.activityMappings.insurance;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{insuranceQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.insurance + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if(Component['json']['name'] ===\"shippingOtherRelevantActivities\"){\r\n        Component['json']['elementsJSON'].forEach(elements => { // the differnt questions\r\n          for(const element in elements ){ // properties of each question\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGA'){\r\n              elements['choicesByUrl']['url'] = environment['apis']['default']['url'].replace(\"{0}\", this.tenantName) + \"api/lookup-service/ciga\" + '?FilterByRelevantActivityId=' + this.activityMappings.shipping;\r\n            }\r\n            if(element === 'name' && elements[element] === 'otherRelevantActivitiesCIGAComment'){\r\n              elements['visibleIf'] = \"{shippingQuestions.otherRelevantActivitiesCIGA} anyof ['\" +this.cigaOtherId.shipping + \"']\";\r\n            }\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  async getNeededData():Promise<void> {\r\n    this.activites  = this.declarationData.surveyData[\"relevantActRelevantActivities\"]\r\n    if(this.activites)\r\n    {\r\n      this.relevantActivityList.items.forEach(element => {\r\n        this.activites.forEach(activity => {\r\n          if(element.id === activity){\r\n            this.relevantActivites.push(element.name);\r\n          }\r\n        });\r\n      });\r\n    }\r\n    this.setSurvey()\r\n  }\r\n\r\n  intialConfigAndSetup():void{\r\n    const configData = this.configState.getAll();\r\n    if(configData.currentTenant.isAvailable){\r\n      this.tenantName = configData.currentTenant.name;\r\n    }\r\n\r\n    let cigaObservable = this.cigaLookup.getList({maxResultCount:100});\r\n    let templateObservable = this.service.getList(this.templateListDto);\r\n    let activityObservable = this.activityLookup.getList({maxResultCount: 100});\r\n    let declarationObservable = this.declarationService.getCADeclarationByDeclarationId(this.declarationId);\r\n\r\n    let observableList : Observable<any>[] = [cigaObservable,templateObservable,activityObservable,declarationObservable];\r\n\r\n    forkJoin(observableList).subscribe(result =>{\r\n      const cigaData = result[0].items;\r\n      const templateData = result[1].items;\r\n      this.relevantActivityList = result[2];\r\n      this.declarationData = result[3];\r\n\r\n      //console.log('declarationData', this.declarationData);\r\n\r\n      this.relevantActivityList.items.forEach(element => {\r\n        if(element.name ===\"Holding business\") this.activityMappings.holding = element.id;\r\n        if(element.name ===\"Distribution and service centre business\") this.activityMappings.distribution = element.id;\r\n        if(element.name ===\"Intellectual property business\") this.activityMappings.ip = element.id;\r\n        if(element.name ===\"Shipping business\") this.activityMappings.shipping = element.id;\r\n        if(element.name ===\"Headquarters business\") this.activityMappings.headquarters = element.id;\r\n        if(element.name ===\"Finance and leasing business\") this.activityMappings.finance = element.id;\r\n        if(element.name ===\"Fund management business\") this.activityMappings.funds = element.id;\r\n        if(element.name ===\"Insurance business\") this.activityMappings.insurance = element.id;\r\n        if(element.name ===\"Banking business\") this.activityMappings.banking = element.id;\r\n      });\r\n\r\n      cigaData.forEach(item=>{\r\n        if(item.name.includes('(please specify)')){\r\n          if(item.relevantActivityId === this.activityMappings.holding) this.cigaOtherId.holding = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.distribution) this.cigaOtherId.distribution = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.ip) this.cigaOtherId.ip = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.shipping) this.cigaOtherId.shipping = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.headquarters) this.cigaOtherId.headquarters = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.finance) this.cigaOtherId.finance = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.funds) this.cigaOtherId.funds = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.insurance) this.cigaOtherId.insurance = item.id;\r\n          if(item.relevantActivityId === this.activityMappings.banking) this.cigaOtherId.banking = item.id;\r\n        }\r\n      });\r\n\r\n      var activeTemplate;\r\n      if(this.declarationData && this.declarationData.declarationTemplateId)\r\n      {\r\n        activeTemplate = templateData.find(x => x.id == this.declarationData.declarationTemplateId);\r\n      } else {\r\n        activeTemplate = templateData.find(element => element.isActive);\r\n      }\r\n      if(activeTemplate)\r\n      {\r\n        this.activeTemplate = activeTemplate.survey;\r\n        this.templateId = activeTemplate.id;\r\n      }\r\n      this.compositeQuestionService.setCompositeQuestions(activeTemplate);\r\n      this.setUrls(this.activeTemplate);\r\n      this.preProcessCigaDropdown()\r\n      this.getNeededData()\r\n    }, error=>{\r\n      this.loading = false;\r\n      console.log(\"intialConfigAndSetup Error:\", error);\r\n    });\r\n  }\r\n\r\n  setUrls(SurveyTemplate:SurveyDto):void{ // only for non composite questions\r\n    for(const key in SurveyTemplate){\r\n      for(const key2 in SurveyTemplate[key]){\r\n        for(const key3 in SurveyTemplate[key][key2]){\r\n          for(const key4 in SurveyTemplate[key][key2][key3]){\r\n            for(const key5 in SurveyTemplate[key][key2][key3][key4]){\r\n              for(const key6 in SurveyTemplate[key][key2][key3][key4][key5]){\r\n                for(const key7 in SurveyTemplate[key][key2][key3][key4][key5][key6]){\r\n                  if(key7 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7] !== null){ // choices by url is on the question level example is relevant activity drop down\r\n                    SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7].url.replace(\"{0}\", this.tenantName);\r\n                    console.log(key7,SurveyTemplate[key][key2][key3][key4][key5][key6][key7] )\r\n                  }\r\n                  for(const key8 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7]){\r\n                    for(const key9 in SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8]){\r\n                      if(key9 === 'choicesByUrl' && SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9] !== null ){ // choices by url is on a column level example entity details differnt business address country drop down\r\n                        SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url = SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9].url.replace(\"{0}\", this.tenantName);\r\n                        console.log(key9,SurveyTemplate[key][key2][key3][key4][key5][key6][key7][key8][key9])\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  getActivityEnum(name:string)\r\n  {\r\n    switch(name){\r\n      case \"intellectualPropertyBusinessdropdown\":\r\n        return RelevantActivityType.IntellectualProperty;\r\n      case \"holdingBusinessQuestionsdropdown\":\r\n        return RelevantActivityType.Holding;\r\n      case \"bankingQuestionsdropdown\":\r\n        return RelevantActivityType.Banking;\r\n      case \"insuranceQuestionsdropdown\":\r\n        return RelevantActivityType.Insurance;\r\n      case \"fundManagmentQuestionsdropdown\":\r\n        return RelevantActivityType.FundManagement;\r\n      case \"financeQuestionsdropdown\":\r\n        return RelevantActivityType.FinanceAndLeasing;\r\n      case \"headquartersQuestionsdropdown\":\r\n        return RelevantActivityType.Headquarter;\r\n      case \"shippingQuestionsdropdown\":\r\n        return RelevantActivityType.Shipping;\r\n      case \"distributionQuestionsdropdown\":\r\n        return RelevantActivityType.Distribution;\r\n      default:\r\n        return RelevantActivityType.NA;\r\n    }\r\n  }\r\n\r\n  getActivityString(activity:RelevantActivityType)\r\n  {\r\n    switch(activity){\r\n      case RelevantActivityType.IntellectualProperty:\r\n        return \"intellectualPropertyBusinessdropdown\";\r\n      case RelevantActivityType.Holding:\r\n      return \"holdingBusinessQuestionsdropdown\";\r\n      case RelevantActivityType.Banking:\r\n        return \"bankingQuestionsdropdown\";\r\n      case RelevantActivityType.Insurance:\r\n        return \"insuranceQuestionsdropdown\";\r\n      case RelevantActivityType.FundManagement:\r\n        return \"fundManagmentQuestionsdropdown\";\r\n      case RelevantActivityType.FinanceAndLeasing:\r\n        return \"financeQuestionsdropdown\";\r\n      case RelevantActivityType.Headquarter:\r\n        return \"headquartersQuestionsdropdown\";\r\n      case RelevantActivityType.Shipping:\r\n        return \"shippingQuestionsdropdown\";\r\n      case RelevantActivityType.Distribution:\r\n        return \"distributionQuestionsdropdown\";\r\n      default:\r\n        return \"\";\r\n    }\r\n  }\r\n}\r\n", "<div class=\"spinner\" *ngIf=\"loading\">\r\n  <mat-progress-spinner\r\n    [mode]=\"'indeterminate'\"\r\n    [diameter]=\"100\"\r\n    class=\"mat-spinner-color\"\r\n  ></mat-progress-spinner>\r\n</div>\r\n\r\n<app-assesment-selection\r\n  (refreshData)=\"refresh($event)\"\r\n  [activityActions]=\"activityActions\"\r\n  [activityCount]=\"activityCount\"\r\n  *ngIf=\"declarationData\"\r\n  [declarationData]=\"declarationData\"\r\n  [from]=\"from\"\r\n></app-assesment-selection>\r\n<app-assessment-action-view\r\n  *ngIf=\"declarationData\"\r\n  [declarationData]=\"declarationData\"\r\n  [declarationId]=\"declarationData?.id\"\r\n  type=\"CA\"\r\n></app-assessment-action-view>\r\n<div id=\"survey\" *ngIf=\"entityId && declarationData\" >\r\n  <app-declaration-entity-details\r\n    [entityId]=\"entityId\"\r\n    type=\"CA\"\r\n    isViewMode=\"true\"\r\n  ></app-declaration-entity-details>\r\n  <survey *ngIf=\"declarationData\" [model]=\"survey\"></survey>\r\n</div>\r\n"], "mappings": ";AACA,SAASA,UAAU,QAAQ,+BAA+B;AAG1D,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,SAAS,QAAQ,0EAA0E;AACpG,SAASC,oBAAoB,EAAEC,qBAAqB,QAAQ,qDAAqD;AAIjH,SAASC,WAAW,QAAQ,2BAA2B;AAIvD,SAAqBC,QAAQ,EAAEC,QAAQ,QAAQ,MAAM;AACrD,SAASC,mBAAmB,EAAEC,KAAK,EAAEC,UAAU,QAAQ,aAAa;AACpE,SAASC,oBAAoB,QAAQ,sGAAsG;AAE3I,OAAOC,IAAI,MAAM,aAAa;AAG9B,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,0BAA0B,QAAQ,0EAA0E;;;;;;;;;;;;;;;;;;;ICvBrHC,EAAA,CAAAC,cAAA,aAAqC;IACnCD,EAAA,CAAAE,SAAA,8BAIwB;IAC1BF,EAAA,CAAAG,YAAA,EAAM;;;IAJFH,EAAA,CAAAI,SAAA,EAAwB;IACxBJ,EADA,CAAAK,UAAA,yBAAwB,iBACR;;;;;;IAKpBL,EAAA,CAAAC,cAAA,iCAOC;IANCD,EAAA,CAAAM,UAAA,yBAAAC,wGAAAC,MAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAeF,MAAA,CAAAG,OAAA,CAAAN,MAAA,CAAe;IAAA,EAAC;IAMhCR,EAAA,CAAAG,YAAA,EAA0B;;;;IADzBH,EAJA,CAAAK,UAAA,oBAAAM,MAAA,CAAAI,eAAA,CAAmC,kBAAAJ,MAAA,CAAAK,aAAA,CACJ,oBAAAL,MAAA,CAAAM,eAAA,CAEI,SAAAN,MAAA,CAAAO,IAAA,CACtB;;;;;IAEflB,EAAA,CAAAE,SAAA,oCAK8B;;;;IAF5BF,EADA,CAAAK,UAAA,oBAAAM,MAAA,CAAAM,eAAA,CAAmC,kBAAAN,MAAA,CAAAM,eAAA,kBAAAN,MAAA,CAAAM,eAAA,CAAAE,EAAA,CACE;;;;;IASrCnB,EAAA,CAAAE,SAAA,iBAA0D;;;;IAA1BF,EAAA,CAAAK,UAAA,UAAAM,MAAA,CAAAS,MAAA,CAAgB;;;;;IANlDpB,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAE,SAAA,wCAIkC;IAClCF,EAAA,CAAAqB,UAAA,IAAAC,6CAAA,qBAAiD;IACnDtB,EAAA,CAAAG,YAAA,EAAM;;;;IALFH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,aAAAM,MAAA,CAAAY,QAAA,CAAqB;IAIdvB,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,SAAAM,MAAA,CAAAM,eAAA,CAAqB;;;ADHhCnB,uBAAuB,CAAC0B,QAAQ,CAACC,iBAAiB,CAAC,iBAAiB,EAAE1B,0BAA0B,CAAC;AAMjG,OAAM,MAAO2B,qBAAsB,SAAQzC,gBAAgB;EAEzD0C,YAAYC,QAAkB,EACpBC,kBAAsC,EACtCC,KAAqB,EACrBC,QAAkB,EAClBC,cAAuC,EACvCC,wBAAkD,EAClDC,OAAwB,EACxBC,UAAuB,EACvBC,iBAAmC,EACnCC,UAA6B,EAC7BC,cAA8B,EAC9BC,MAA0B;IAElC,KAAK,CAACX,QAAQ,CAAC;IAZP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAqBhB,KAAAC,aAAa,GAAa,CAAC,8BAA8B,EAAC,kBAAkB,EAAE,uBAAuB,EAAE,kBAAkB,EAAE,wBAAwB,EACnJ,uBAAuB,EAAE,0BAA0B,EAAE,oBAAoB,EAAC,yCAAyC,EAAE,mBAAmB,CAAC;IACzI,KAAAC,eAAe,GAAuB;MAACC,OAAO,EAAE,IAAI;MAAEC,SAAS,EAAC,CAAC;MAAEC,cAAc,EAAE;IAAG,CAAC,EAAC;IAExF,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,SAAS,GAAG,EAAE;IAEd,KAAAC,WAAW,GAAG;MAACC,OAAO,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,EAAE,EAAC,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAGC,YAAY,EAAC,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAGC,KAAK,EAAC,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,OAAO,EAAC;IAAE,CAAG;IACzI,KAAAC,gBAAgB,GAAG;MAACT,OAAO,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,EAAE,EAAC,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAGC,YAAY,EAAC,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAGC,KAAK,EAAC,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,OAAO,EAAC;IAAE,CAAG;IAC9I,KAAA1C,eAAe,GAAG,EAAE;IACpB,KAAA4C,qBAAqB,GAAG,EAAE;IAC1B,KAAA3C,aAAa,GAAG,CAAC;IAGjB,KAAA4C,gBAAgB,GAAG,kBAAkB,CAAC,CAAC;IACvC;;;;;IAKA,KAAA1C,IAAI,GAAG,EAAE;IAvCP,IAAI,CAACY,KAAK,CAAC+B,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAI,CAACC,aAAa,GAAGD,MAAM,CAAC,eAAe,CAAC,GAAGA,MAAM,CAAC,eAAe,CAAC,GAAG,IAAI;MAC7E,IAAI,CAACxC,QAAQ,GAAIwC,MAAM,CAAC,UAAU,CAAC,GAAGA,MAAM,CAAC,UAAU,CAAC,GAAE,IAAI;MAC9D;MACA;MACA,IAAI,CAAC7C,IAAI,GAAG6C,MAAM,CAAC,MAAM,CAAC,GAAEA,MAAM,CAAC,MAAM,CAAC,GAAE,WAAW;IACzD,CAAC,CAAC;IACF;EACF;EAkCME,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACrB,OAAO,GAAG,IAAI;MACnBqB,KAAI,CAACE,WAAW,GAAGF,KAAI,CAAC3B,MAAM,CAAC8B,MAAM,CAAC,aAAa,CAAC;MACpD,MAAMH,KAAI,CAACI,oBAAoB,EAAE;IAAC;EACpC;EAGMC,SAASA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MACbK,MAAI,CAACC,YAAY,GAAGtF,oBAAoB;MACxCqF,MAAI,CAACE,oBAAoB,GAAGtF,qBAAqB;MACjDoF,MAAI,CAACG,sBAAsB,EAAE;MAC7BH,MAAI,CAACI,aAAa,EAAE;MACpB,MAAMC,SAAS,GAAG,IAAIhF,SAAS,EAAE;MACjC2E,MAAI,CAACpD,MAAM,CAAC0D,cAAc,CAACC,GAAG,CAAC,UAAU3D,MAAM,EAAE4D,OAAO;QACtD;QACA,IAAIC,GAAG,GAAGJ,SAAS,CAACK,QAAQ,CAACF,OAAO,CAACG,IAAI,CAAC;QAC1C;QACAF,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,CAAC,CAAC;QACtBH,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,CAAC,EAAEH,GAAG,CAACI,MAAM,GAAG,CAAC,CAAC;QACtC;QACAL,OAAO,CAACM,IAAI,GAAGL,GAAG;MACpB,CAAC,CAAC;MACFT,MAAI,CAACe,aAAa,EAAE;IAAA;EACtB;EAEAZ,sBAAsBA,CAAA;IACpB,MAAMa,gBAAgB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IACzC,IAAI,IAAI,CAACvE,eAAe,CAACwE,iBAAiB,CAACC,UAAU,KAAK,IAAI,CAACtB,WAAW,CAACjD,EAAE,IAAI,CAACqE,gBAAgB,CAACG,QAAQ,CAAC,IAAI,CAAC1E,eAAe,CAAC2E,gBAAgB,CAAC,EAAC;MACjJ,IAAI,CAACC,wBAAwB,GAAG,KAAK;IACvC,CAAC,MAAI;MACH,IAAI,CAACA,wBAAwB,GAAG,IAAI;IACtC;EAEF;EAGA/E,OAAOA,CAACgF,KAAK;IACX,IAAI,CAAC7E,eAAe,GAAG6E,KAAK;IAC5B,IAAI,CAACvB,SAAS,EAAE;EAClB;EAEAwB,WAAWA,CAACC,IAAY;IAEtB,IAAI,CAACzB,SAAS,EAAE;IAChB,IAAI,CAACnD,MAAM,CAAC6E,iBAAiB,CAACD,IAAI,CAAC,CAACE,KAAK,EAAE;EAC7C;EACAC,UAAUA,CAACC,OAAe;IACxB,OAAOpH,UAAU,CAACqH,gBAAgB,CAACD,OAAO,EAAC,YAAY,CAAC;EAC1D;EACCb,aAAaA,CAAA;IACZ,IAAG,IAAI,CAACvB,aAAa,EAAC;MAAC;MACrB,KAAI,MAAMsC,GAAG,IAAI,IAAI,CAACrF,eAAe,CAACsF,UAAU,EAAC;QAC/C,IAAG,IAAI,CAACtF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,EAAC;UAAE;UACxC,IAAGA,GAAG,KAAK,0BAA0B,EAAC;YACpC,IAAI,CAACrF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,GAAG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAClF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC;UAC9F;UACA,IAAGA,GAAG,KAAK,wBAAwB,EAAE;YACnC,IAAI,CAACrF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,GAAG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAClF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC;UAC9F;UAEA,IAAG,IAAI,CAAC9D,aAAa,CAACmD,QAAQ,CAACW,GAAG,CAAC,EAAC;YAClC,IAAGA,GAAG,KAAK,0BAA0B,EAAE;cACrC,IAAI,CAACrF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,gDAAgD,CAAC,GAAG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAClF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,gDAAgD,CAAC,CAAC;cAChM,IAAI,CAACrF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,8CAA8C,CAAC,GAAG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAClF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,8CAA8C,CAAC,CAAC;YAC9L;YACA,IAAGA,GAAG,KAAK,8BAA8B,EAAC;cACxC,IAAI,CAACrF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,yCAAyC,CAAC,GAAG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAClF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,yCAAyC,CAAC,CAAC;cAClL,IAAI,CAACrF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,uCAAuC,CAAC,GAAG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAClF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC;YAChL;YACA,IAAGA,GAAG,KAAK,8BAA8B,IAAIA,GAAG,KAAK,0BAA0B,EAAC;cAAE;cAChF,IAAI,CAACrF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,uDAAuD,CAAC,GAAG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAClF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,uDAAuD,CAAC,CAAC;cAC9M,IAAI,CAACrF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,qDAAqD,CAAC,GAAG,IAAI,CAACH,UAAU,CAAC,IAAI,CAAClF,eAAe,CAACsF,UAAU,CAACD,GAAG,CAAC,CAAC,qDAAqD,CAAC,CAAC;YAC5M;UACF;QAEF;MACF;MACA,IAAI,CAAClF,MAAM,CAACoF,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC1F,eAAe,CAACsF,UAAU,CAAC,CAAC;MAC9E,IAAI,CAACK,OAAO,EAAE;IAChB;EACF;EAEAC,cAAcA,CAAC7B,OAAW;IACxB,IAAGA,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC8B,SAAS,EACvC;MACE,IAAIC,KAAK,GAAG,CAAC;MACb,IAAIC,QAAQ,GAAG,EAAE;MACjB;MACE,IAAIC,CAAC,GAAGjC,OAAO,CAAC8B,SAAS;MACzB,IAAII,OAAO,GAAGlC,OAAO,CAACmC,QAAQ,CAACnB,IAA8B;MAC7D,IAAIoB,MAAM,GAAG,IAAI,CAACC,SAAS,CAACrC,OAAO,CAAC8B,SAAS,CAAC;MAC9C,IAAIQ,QAAQ,GAAG,KAAK;MACpB,IAAGJ,OAAO,EAAC;QACT,MAAMK,kBAAkB,GAAGvC,OAAO,EAAEgB,IAAI;QACxC,MAAMwB,WAAW,GAAGD,kBAAkB,GAAG,SAAS;QAClD,IAAI,CAAC1F,kBAAkB,CAAC4F,6BAA6B,CAAC,IAAI,CAACzD,aAAa,EAAEiD,CAAC,CAACjB,IAAI,EAAEkB,OAAO,EAAEI,QAAQ,CAAC,CAACI,IAAI,CACvGpI,QAAQ,CAAC,MAAI,CACb,CAAC,CAAC,CACH,CACAwE,SAAS,CAAE6D,MAAM,IAAG;UACnB,IAAIC,CAAC,GAAGD,MAAM;UACd3C,OAAO,CAAC6C,QAAQ,CAAC,SAAS,EAAET,MAAM,GAAGO,MAAM,CAAC;QAC9C,CAAC,CAAC;MACJ;MACF;IACF;EACF;EACAN,SAASA,CAACP,SAAc;IACtB,IAAIgB,QAAQ,GAAGhB,SAAS,CAACd,IAAI;IAC7B,IAAI+B,KAAK,GAAGD,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;IAC/B,IAAIC,SAAS,GAAGD,KAAK,CAACA,KAAK,CAAC1C,MAAM,GAAC,CAAC,CAAC;IACrC,QAAO2C,SAAS,CAACC,WAAW,EAAE;MAE5B,KAAK,KAAK;QAAE;UACV,OAAO,wBAAwB;QACjC;MACA,KAAK,KAAK;QAAE;UACV,OAAO,wBAAwB;QACjC;MACA,KAAK,KAAK;QAAE;UACV,OAAO,yBAAyB;QAClC;MACA,KAAK,MAAM;QAAE;UACX,OAAO,yBAAyB;QAClC;MACA,KAAK,KAAK;QAAE;UACV,OAAO,8BAA8B;QACvC;IACF;IACA,OAAO,EAAE;EACX;EAEAC,eAAeA,CAACC,QAAgB;IAC9B,QAAOA,QAAQ,CAACF,WAAW,EAAE;MAE3B,KAAK,KAAK;QAAE;UACV,OAAO,IAAI;QACb;MACA,KAAK,KAAK;QAAE;UACV,OAAO,IAAI;QACb;MACA,KAAK,KAAK;QAAE;UACV,OAAO,IAAI;QACb;MACA,KAAK,MAAM;QAAE;UACX,OAAO,IAAI;QACb;MACA,KAAK,KAAK;QAAE;UACV,OAAO,IAAI;QACb;IACF;IACA,OAAO,KAAK;EACd;EAEArD,aAAaA,CAAA;IACX,MAAMwD,yBAAyB,GAAG,IAAI,CAACtF,iBAAiB,CAACuF,GAAG,CAACC,QAAQ,IAAEA,QAAQ,CAACL,WAAW,EAAE,CAAC;IAC9F,IAAI,CAACxD,YAAY,CAAC8D,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAG;MACpD,IAAI,CAACC,cAAc,EAAEJ,KAAK,CAACE,OAAO,CAACG,IAAI,IAAE;QACnC,IAAGF,OAAO,CAAC1C,IAAI,KAAK4C,IAAI,CAAC5C,IAAI,EAAE;UAC7B,IAAI4C,IAAI,CAAC5C,IAAI,KAAK,gBAAgB,EAAC;YACjC,IAAI6C,SAAS,GAAG,EAAE;YAElBD,IAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAACC,OAAO,CAAEK,CAAC,IAAG;cACrCA,CAAC,CAACC,KAAK,GAAG,MAAM;cAChB,IAAIX,yBAAyB,CAACzC,QAAQ,CAACmD,CAAC,CAACE,KAAK,CAACf,WAAW,EAAE,CAAC,EAAE;gBAC7D,IAAI,CAACtE,qBAAqB,CAACsF,IAAI,CAACH,CAAC,CAAC9C,IAAI,GAAE,UAAU,CAAC;gBACnD6C,SAAS,CAACI,IAAI,CAAC;kBACbC,IAAI,EAAE,UAAU;kBAChBlD,IAAI,EAAE8C,CAAC,CAAC9C,IAAI,GAAE,UAAU;kBACxBgD,KAAK,EAAEF,CAAC,CAACE,KAAK,GAAG,oBAAoB;kBACrCG,OAAO,EAAE,CAAC,MAAM,EAAC,MAAM,CAAC;kBACxBX,QAAQ,EAAE,IAAI;kBACdY,OAAO,EAAE,IAAI;kBACbC,YAAY,EAAE,IAAI;kBAClBrE,OAAO,EAAE,IAAI;kBACbsE,UAAU,EAAE,IAAI;kBAChBC,QAAQ,EAAE,IAAI,CAAC1D,wBAAwB;kBACvC2D,UAAU,EAAE,IAAI;kBAChBC,gBAAgB,EAAC;iBAClB,CAAC;cACJ;cACAZ,SAAS,CAACI,IAAI,CAACH,CAAC,CAAC;YACnB,CAAC,CAAC;YACFJ,OAAO,CAACF,QAAQ,GAAGK,SAAS;UAC9B,CAAC,MAAI;YACHH,OAAO,CAACF,QAAQ,GAAGI,IAAI,CAACJ,QAAQ;UAClC;QACF;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,IAAI,CAACxH,aAAa,GAAG,IAAI,CAAC2C,qBAAqB,CAAC0B,MAAM;IAEtD;IAED,IAAI,IAAI,CAACZ,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC8D,KAAK,IAAI,IAAI,CAAC9D,YAAY,CAAC8D,KAAK,CAAClD,MAAM,GAAC,CAAC,EAAC;MACpF,IAAI,CAACZ,YAAY,CAAC8D,KAAK,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,2CAA2C,GAAG,IAAI,CAACjH,QAAQ,CAAC2H,SAAS,CAAC,IAAI,CAACzI,eAAe,CAAC0I,aAAa,EAAC,uBAAuB,EAAE,OAAO,CAAC;MAC7K;MACA,IAAI,IAAI,CAAClF,YAAY,CAAC8D,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,IAAI,IAAI,CAAC/D,YAAY,CAAC8D,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACnD,MAAM,GAAC,CAAC,EAAE;QACvF,IAAI,CAACZ,YAAY,CAAC8D,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,OAAO,CAACmB,EAAE,IAAG;UAC/C,IAAIA,EAAE,CAACpB,QAAQ,IAAIoB,EAAE,CAACpB,QAAQ,CAACnD,MAAM,GAAC,CAAC,EAAE;YACvCuE,EAAE,CAACpB,QAAQ,CAACC,OAAO,CAACoB,EAAE,IAAG;cACzB,IAAKA,EAAE,CAACX,IAAI,IAAE,UAAU,EAAEW,EAAE,CAACC,QAAQ,GAAG,OAAO;YAC/C,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACH;IACH;IAEEpK,UAAU,CAACqK,WAAW,CAAC,UAAU,EAAE;MACjC/D,IAAI,EAAE,YAAY;MAClBkD,IAAI,EAAE;KACP,CAAC;IACF,IAAI,CAAC9H,MAAM,GAAG,IAAI3B,KAAK,CAAC,IAAI,CAACgF,YAAY,CAAC;IAC1C,IAAI,CAACrD,MAAM,CAAC4I,UAAU,CAAC9K,SAAS,CAAC;IACjC;IACA,IAAI,CAACkC,MAAM,CAAC6I,qBAAqB,GAAG,KAAK;IACzC,IAAI,CAAC7I,MAAM,CAAC8I,mBAAmB,CAACnF,GAAG,CAAC,CAACoF,MAAM,EAACnF,OAAO,KAAG;MACpD,IAAI,CAACoF,4BAA4B,CAACD,MAAM,EAACnF,OAAO,CAAC;IACnD,CAAC,CAAC;IAEF,IAAI,CAAC5D,MAAM,CAACiJ,0BAA0B,CAACtF,GAAG,CAAC,UAASoF,MAAM,EAAEnF,OAAO;MACjE,IAAG,CAACA,OAAO,CAACmC,QAAQ,CAACmD,UAAU,EAAE;MACjCtF,OAAO,CAACuF,WAAW,CAACC,KAAK,CAACzB,KAAK,GAAG/D,OAAO,CAACmC,QAAQ,CAACmD,UAAU;IAC/D,CAAC,CAAC;IAEF,IAAI,CAAClJ,MAAM,CAACqJ,cAAc,CAAC1F,GAAG,CAAC,CAACoF,MAAM,EAAEnF,OAAO,KAAG;MAEhD,IAAI,CAAC5D,MAAM,CAAC6E,iBAAiB,CAACjB,OAAO,CAACgB,IAAI,CAAC,CAAC8D,QAAQ,GAAG,OAAO;MAC9D,IAAG,IAAI,CAACnG,qBAAqB,CAACgC,QAAQ,CAACX,OAAO,CAACgB,IAAI,CAAC,IAAIhB,OAAO,CAAC0F,KAAK,EACrE;QACE;QACA9K,IAAI,CAAC+K,IAAI,CAAC;UACR3B,KAAK,EAAE,4BAA4B;UACnC7D,IAAI,EAAE,8CAA8C;UACpDyF,IAAI,EAAE,SAAS;UACfC,gBAAgB,EAAE,IAAI;UACtBC,kBAAkB,EAAE,SAAS;UAC7BC,iBAAiB,EAAE;SACpB,CAAC,CAACC,IAAI,CAAErD,MAAM,IAAI;UACjB,IAAGA,MAAM,CAACsD,WAAW,EACrB;YACI,IAAIC,QAAQ,GAAG;cACbC,gBAAgB,EAAE,IAAI,CAACC,eAAe,CAACpG,OAAO,CAACgB,IAAI,CAAC;cACpDqF,IAAI,EAAErG,OAAO,CAAC0F,KAAK,CAACzC,WAAW,EAAE,IAAI;aACP;YAChC,IAAI,CAAC7F,iBAAiB,CAACkJ,kDAAkD,CAAC,IAAI,CAACtH,aAAa,EAAEkH,QAAQ,CAAC,CACtGpH,SAAS,CAAC6D,MAAM,IAAG;cAClB,IAAGA,MAAM,EAAC;gBACR,IAAI,CAAC1G,eAAe,GAAG0G,MAAM;gBAC7B,IAAI,CAACyC,4BAA4B,CAACD,MAAM,EAACnF,OAAO,CAAC;gBACjD,IAAI,CAAC1C,cAAc,CAACiJ,OAAO,CAAC,kCAAkC,CAAC;gBAC/D,IAAI,CAACxF,WAAW,CAACf,OAAO,CAACgB,IAAI,CAAC;cAChC;YACF,CAAC,CAAC;UAEN,CAAC,MAAM;YACLhB,OAAO,CAACmC,QAAQ,CAACqE,UAAU,EAAE;YAC7B,IAAI,CAACzF,WAAW,CAACf,OAAO,CAACgB,IAAI,CAAC;YAC9B;UACF;QACF,CAAC,CAAC;MACJ,CAAC,MAAI;QACH,IAAI,CAAC5E,MAAM,CAAC6E,iBAAiB,CAACjB,OAAO,CAACgB,IAAI,CAAC,CAAC8D,QAAQ,GAAG,MAAM;QAC7D,IAAI,CAACM,4BAA4B,CAACD,MAAM,EAACnF,OAAO,CAAC;MACnD;IAEF,CAAC,CAAC;IACF,IAAI,CAAC5D,MAAM,CAACyF,cAAc,CAAC9B,GAAG,CAAC,CAAC0G,CAAC,EAAEzG,OAAO,KAAI;MAC5C,IAAI,CAAC6B,cAAc,CAAC7B,OAAO,CAAC;IAC9B,CAAC,CAAC;EAEJ;EAEAoF,4BAA4BA,CAACD,MAAM,EAAEnF,OAAO;IAC1C,IAAI0G,kBAAkB,GAAG,EAAE;IACzB,IAAI,CAAC/H,qBAAqB,CAAC8E,OAAO,CAACzC,IAAI,IAAE;MACvCmE,MAAM,CAAC3D,IAAI,CAACR,IAAI,CAAC,IAAI0F,kBAAkB,CAACzC,IAAI,CAAC;QAC3CjD,IAAI;QACJ0E,KAAK,EAAEP,MAAM,CAAC3D,IAAI,CAACR,IAAI;OACxB,CAAC;IACF,CAAC,CAAC;IACF,IAAI,CAAChB,OAAO,CAACgB,IAAI,IAAIhB,OAAO,CAACgB,IAAI,IAAI,+BAA+B,EAAC;MACnE,IAAI,CAACjF,eAAe,GAAG2K,kBAAkB;IAC3C;EACN;EAEA9E,OAAOA,CAAA;IACL;IACA,IAAG,IAAI,CAAC7D,SAAS,EAAC;MAChB,IAAG,IAAI,CAAC3B,MAAM,CAACoF,IAAI,CAAC,+BAA+B,CAAC,EAAC;QAEnD,IAAG,IAAI,CAAC1D,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACuC,MAAM,GAAE,CAAC,EAAC;UAC5D,IAAI,CAACjE,MAAM,CAACuK,QAAQ,CAAC,+BAA+B,EAAE,IAAI,CAAC7I,iBAAiB,EAAE8I,SAAS,EAAE,KAAK,CAAC;QACjG,CAAC,MACI;UACH,IAAI,CAACxK,MAAM,CAACuK,QAAQ,CAAC,+BAA+B,EAAE,IAAI,CAAC5I,SAAS,EAAE6I,SAAS,EAAE,KAAK,CAAC;QACzF;MACF;IACF;IACA,IAAG,IAAI,CAAC3K,eAAe,IAAI,IAAI,CAACA,eAAe,CAACwE,iBAAiB,IAAI,IAAI,CAACxE,eAAe,CAACwE,iBAAiB,CAACoG,yBAAyB,EACrI;MACE,IAAI,CAAC5K,eAAe,CAACwE,iBAAiB,CAACoG,yBAAyB,CAACpD,OAAO,CAACqD,EAAE,IAAG;QAC5E,IAAIC,YAAY,GAAG,IAAI,CAACC,iBAAiB,CAACF,EAAE,CAACX,gBAAgB,CAAC;QAC5D,IAAI,CAAC/J,MAAM,CAACuK,QAAQ,CAACI,YAAY,EAACD,EAAE,CAACT,IAAI,GAAG,MAAM,GAAC,MAAM,EAAEO,SAAS,EAAE,KAAK,CAAC;MAChF,CAAC,CAAC;IACJ;IACA,IAAI,CAAC/I,OAAO,GAAG,KAAK;EACtB;EAEMoJ,sBAAsBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA/H,iBAAA;MAC1B3E,mBAAmB,CAACgC,QAAQ,CAAC2K,KAAK,CAAC1D,OAAO,CAAC2D,SAAS,IAAG;QACrD,IAAGA,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,8BAA8B,EAAC;UAC7DA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC3D,OAAO,CAACD,QAAQ,IAAG;YACnD,KAAI,MAAME,OAAO,IAAIF,QAAQ,EAAE;cAAE;cAC/B,IAAGE,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,8CAA8C,EAAC;gBAC5FF,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAGnJ,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACgN,OAAO,CAAC,KAAK,EAAEH,MAAI,CAACI,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGJ,MAAI,CAACxI,gBAAgB,CAACP,EAAE;cAChM;cACA,IAAGuF,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,qDAAqD,EAAC;gBACnGF,QAAQ,CAAC,WAAW,CAAC,GAAG,sFAAsF,GAAE0D,MAAI,CAAClJ,WAAW,CAACG,EAAE,GAAG,IAAI;cAC5I;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAGiJ,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,gCAAgC,EAAC;UAC/DA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC3D,OAAO,CAACD,QAAQ,IAAG;YACnD,KAAI,MAAME,OAAO,IAAIF,QAAQ,EAAE;cAAE;cAC/B,IAAGE,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC5EF,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAGnJ,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACgN,OAAO,CAAC,KAAK,EAAEH,MAAI,CAACI,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGJ,MAAI,CAACxI,gBAAgB,CAACD,OAAO;cACpM;cACA,IAAGiF,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFF,QAAQ,CAAC,WAAW,CAAC,GAAG,yDAAyD,GAAE0D,MAAI,CAAClJ,WAAW,CAACS,OAAO,GAAG,IAAI;cACpH;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAG2I,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,qCAAqC,EAAC;UACpEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC3D,OAAO,CAACD,QAAQ,IAAG;YACnD,KAAI,MAAME,OAAO,IAAIF,QAAQ,EAAE;cAAE;cAC/B,IAAGE,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC1EF,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAGnJ,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACgN,OAAO,CAAC,KAAK,EAAEH,MAAI,CAACI,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGJ,MAAI,CAACxI,gBAAgB,CAACR,YAAY;cAC3M;cACA,IAAGwF,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFF,QAAQ,CAAC,WAAW,CAAC,GAAG,8DAA8D,GAAE0D,MAAI,CAAClJ,WAAW,CAACE,YAAY,GAAG,IAAI;cAC9H;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAGkJ,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,gCAAgC,EAAC;UAC/DA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC3D,OAAO,CAACD,QAAQ,IAAG;YACnD,KAAI,MAAME,OAAO,IAAIF,QAAQ,EAAE;cAAE;cAC/B,IAAGE,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC1EF,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAGnJ,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACgN,OAAO,CAAC,KAAK,EAAEH,MAAI,CAACI,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGJ,MAAI,CAACxI,gBAAgB,CAACJ,OAAO;cACtM;cACA,IAAGoF,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFF,QAAQ,CAAC,WAAW,CAAC,GAAG,yDAAyD,GAAE0D,MAAI,CAAClJ,WAAW,CAACM,OAAO,GAAG,IAAI;cACpH;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAG8I,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,sCAAsC,EAAC;UACrEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC3D,OAAO,CAACD,QAAQ,IAAG;YACnD,KAAI,MAAME,OAAO,IAAIF,QAAQ,EAAE;cAAE;cAC/B,IAAGE,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC3EF,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAGnJ,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACgN,OAAO,CAAC,KAAK,EAAEH,MAAI,CAACI,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGJ,MAAI,CAACxI,gBAAgB,CAACH,KAAK;cACnM;cACA,IAAGmF,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFF,QAAQ,CAAC,WAAW,CAAC,GAAG,+DAA+D,GAAE0D,MAAI,CAAClJ,WAAW,CAACO,KAAK,GAAG,IAAI;cACxH;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAG6I,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,qCAAqC,EAAC;UACpEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC3D,OAAO,CAACD,QAAQ,IAAG;YACnD,KAAI,MAAME,OAAO,IAAIF,QAAQ,EAAE;cAAE;cAC/B,IAAGE,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC1EF,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAGnJ,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACgN,OAAO,CAAC,KAAK,EAAEH,MAAI,CAACI,UAAU,CAAC,GAAG,yBAAyB,GAAE,8BAA8B,GAAGJ,MAAI,CAACxI,gBAAgB,CAACL,YAAY;cAC3M;cACA,IAAGqF,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFF,QAAQ,CAAC,WAAW,CAAC,GAAG,8DAA8D,GAAE0D,MAAI,CAAClJ,WAAW,CAACK,YAAY,GAAG,IAAI;cAC9H;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAG+I,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,kCAAkC,EAAC;UACjEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC3D,OAAO,CAACD,QAAQ,IAAG;YACnD,KAAI,MAAME,OAAO,IAAIF,QAAQ,EAAE;cAAE;cAC/B,IAAGE,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC1EF,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAGnJ,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACgN,OAAO,CAAC,KAAK,EAAEH,MAAI,CAACI,UAAU,CAAC,GAAG,yBAAyB,GAAG,8BAA8B,GAAGJ,MAAI,CAACxI,gBAAgB,CAACF,SAAS;cACzM;cACA,IAAGkF,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFF,QAAQ,CAAC,WAAW,CAAC,GAAG,2DAA2D,GAAE0D,MAAI,CAAClJ,WAAW,CAACQ,SAAS,GAAG,IAAI;cACxH;YACF;UACF,CAAC,CAAC;QACJ;QACA,IAAG4I,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAI,iCAAiC,EAAC;UAChEA,SAAS,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC3D,OAAO,CAACD,QAAQ,IAAG;YACnD,KAAI,MAAME,OAAO,IAAIF,QAAQ,EAAE;cAAE;cAC/B,IAAGE,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,6BAA6B,EAAC;gBAC3EF,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,GAAGnJ,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAACgN,OAAO,CAAC,KAAK,EAAEH,MAAI,CAACI,UAAU,CAAC,GAAG,yBAAyB,GAAG,8BAA8B,GAAGJ,MAAI,CAACxI,gBAAgB,CAACN,QAAQ;cACvM;cACA,IAAGsF,OAAO,KAAK,MAAM,IAAIF,QAAQ,CAACE,OAAO,CAAC,KAAK,oCAAoC,EAAC;gBAClFF,QAAQ,CAAC,WAAW,CAAC,GAAG,0DAA0D,GAAE0D,MAAI,CAAClJ,WAAW,CAACI,QAAQ,GAAG,IAAI;cACtH;YACF;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IAAC;EACL;EAEMmJ,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArI,iBAAA;MACjBqI,MAAI,CAACzJ,SAAS,GAAIyJ,MAAI,CAACvL,eAAe,CAACsF,UAAU,CAAC,+BAA+B,CAAC;MAClF,IAAGiG,MAAI,CAACzJ,SAAS,EACjB;QACEyJ,MAAI,CAACC,oBAAoB,CAACN,KAAK,CAAC1D,OAAO,CAACC,OAAO,IAAG;UAChD8D,MAAI,CAACzJ,SAAS,CAAC0F,OAAO,CAACH,QAAQ,IAAG;YAChC,IAAGI,OAAO,CAACvH,EAAE,KAAKmH,QAAQ,EAAC;cACzBkE,MAAI,CAAC1J,iBAAiB,CAACmG,IAAI,CAACP,OAAO,CAAC1C,IAAI,CAAC;YAC3C;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACAwG,MAAI,CAACjI,SAAS,EAAE;IAAA;EAClB;EAEAD,oBAAoBA,CAAA;IAClB,MAAMoI,UAAU,GAAG,IAAI,CAACC,WAAW,CAACC,MAAM,EAAE;IAC5C,IAAGF,UAAU,CAACG,aAAa,CAACC,WAAW,EAAC;MACtC,IAAI,CAACR,UAAU,GAAGI,UAAU,CAACG,aAAa,CAAC7G,IAAI;IACjD;IAEA,IAAI+G,cAAc,GAAG,IAAI,CAAC5K,UAAU,CAAC6K,OAAO,CAAC;MAACpK,cAAc,EAAC;IAAG,CAAC,CAAC;IAClE,IAAIqK,kBAAkB,GAAG,IAAI,CAAC/K,OAAO,CAAC8K,OAAO,CAAC,IAAI,CAACvK,eAAe,CAAC;IACnE,IAAIyK,kBAAkB,GAAG,IAAI,CAAClL,cAAc,CAACgL,OAAO,CAAC;MAACpK,cAAc,EAAE;IAAG,CAAC,CAAC;IAC3E,IAAIuK,qBAAqB,GAAG,IAAI,CAACtL,kBAAkB,CAACuL,+BAA+B,CAAC,IAAI,CAACpJ,aAAa,CAAC;IAEvG,IAAIqJ,cAAc,GAAuB,CAACN,cAAc,EAACE,kBAAkB,EAACC,kBAAkB,EAACC,qBAAqB,CAAC;IAErH5N,QAAQ,CAAC8N,cAAc,CAAC,CAACvJ,SAAS,CAAC6D,MAAM,IAAG;MAC1C,MAAM2F,QAAQ,GAAG3F,MAAM,CAAC,CAAC,CAAC,CAACwE,KAAK;MAChC,MAAMoB,YAAY,GAAG5F,MAAM,CAAC,CAAC,CAAC,CAACwE,KAAK;MACpC,IAAI,CAACM,oBAAoB,GAAG9E,MAAM,CAAC,CAAC,CAAC;MACrC,IAAI,CAAC1G,eAAe,GAAG0G,MAAM,CAAC,CAAC,CAAC;MAEhC;MAEA,IAAI,CAAC8E,oBAAoB,CAACN,KAAK,CAAC1D,OAAO,CAACC,OAAO,IAAG;QAChD,IAAGA,OAAO,CAAC1C,IAAI,KAAI,kBAAkB,EAAE,IAAI,CAACtC,gBAAgB,CAACT,OAAO,GAAGyF,OAAO,CAACvH,EAAE;QACjF,IAAGuH,OAAO,CAAC1C,IAAI,KAAI,0CAA0C,EAAE,IAAI,CAACtC,gBAAgB,CAACR,YAAY,GAAGwF,OAAO,CAACvH,EAAE;QAC9G,IAAGuH,OAAO,CAAC1C,IAAI,KAAI,gCAAgC,EAAE,IAAI,CAACtC,gBAAgB,CAACP,EAAE,GAAGuF,OAAO,CAACvH,EAAE;QAC1F,IAAGuH,OAAO,CAAC1C,IAAI,KAAI,mBAAmB,EAAE,IAAI,CAACtC,gBAAgB,CAACN,QAAQ,GAAGsF,OAAO,CAACvH,EAAE;QACnF,IAAGuH,OAAO,CAAC1C,IAAI,KAAI,uBAAuB,EAAE,IAAI,CAACtC,gBAAgB,CAACL,YAAY,GAAGqF,OAAO,CAACvH,EAAE;QAC3F,IAAGuH,OAAO,CAAC1C,IAAI,KAAI,8BAA8B,EAAE,IAAI,CAACtC,gBAAgB,CAACJ,OAAO,GAAGoF,OAAO,CAACvH,EAAE;QAC7F,IAAGuH,OAAO,CAAC1C,IAAI,KAAI,0BAA0B,EAAE,IAAI,CAACtC,gBAAgB,CAACH,KAAK,GAAGmF,OAAO,CAACvH,EAAE;QACvF,IAAGuH,OAAO,CAAC1C,IAAI,KAAI,oBAAoB,EAAE,IAAI,CAACtC,gBAAgB,CAACF,SAAS,GAAGkF,OAAO,CAACvH,EAAE;QACrF,IAAGuH,OAAO,CAAC1C,IAAI,KAAI,kBAAkB,EAAE,IAAI,CAACtC,gBAAgB,CAACD,OAAO,GAAGiF,OAAO,CAACvH,EAAE;MACnF,CAAC,CAAC;MAEFmM,QAAQ,CAAC7E,OAAO,CAAC+E,IAAI,IAAE;QACrB,IAAGA,IAAI,CAACxH,IAAI,CAACL,QAAQ,CAAC,kBAAkB,CAAC,EAAC;UACxC,IAAG6H,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAAC/J,gBAAgB,CAACT,OAAO,EAAE,IAAI,CAACD,WAAW,CAACC,OAAO,GAAGuK,IAAI,CAACrM,EAAE;UAChG,IAAGqM,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAAC/J,gBAAgB,CAACR,YAAY,EAAE,IAAI,CAACF,WAAW,CAACE,YAAY,GAAGsK,IAAI,CAACrM,EAAE;UAC1G,IAAGqM,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAAC/J,gBAAgB,CAACP,EAAE,EAAE,IAAI,CAACH,WAAW,CAACG,EAAE,GAAGqK,IAAI,CAACrM,EAAE;UACtF,IAAGqM,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAAC/J,gBAAgB,CAACN,QAAQ,EAAE,IAAI,CAACJ,WAAW,CAACI,QAAQ,GAAGoK,IAAI,CAACrM,EAAE;UAClG,IAAGqM,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAAC/J,gBAAgB,CAACL,YAAY,EAAE,IAAI,CAACL,WAAW,CAACK,YAAY,GAAGmK,IAAI,CAACrM,EAAE;UAC1G,IAAGqM,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAAC/J,gBAAgB,CAACJ,OAAO,EAAE,IAAI,CAACN,WAAW,CAACM,OAAO,GAAGkK,IAAI,CAACrM,EAAE;UAChG,IAAGqM,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAAC/J,gBAAgB,CAACH,KAAK,EAAE,IAAI,CAACP,WAAW,CAACO,KAAK,GAAGiK,IAAI,CAACrM,EAAE;UAC5F,IAAGqM,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAAC/J,gBAAgB,CAACF,SAAS,EAAE,IAAI,CAACR,WAAW,CAACQ,SAAS,GAAGgK,IAAI,CAACrM,EAAE;UACpG,IAAGqM,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAAC/J,gBAAgB,CAACD,OAAO,EAAE,IAAI,CAACT,WAAW,CAACS,OAAO,GAAG+J,IAAI,CAACrM,EAAE;QAClG;MACF,CAAC,CAAC;MAEF,IAAIwH,cAAc;MAClB,IAAG,IAAI,CAAC1H,eAAe,IAAI,IAAI,CAACA,eAAe,CAACyM,qBAAqB,EACrE;QACE/E,cAAc,GAAG4E,YAAY,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzM,EAAE,IAAI,IAAI,CAACF,eAAe,CAACyM,qBAAqB,CAAC;MAC7F,CAAC,MAAM;QACL/E,cAAc,GAAG4E,YAAY,CAACI,IAAI,CAACjF,OAAO,IAAIA,OAAO,CAACmF,QAAQ,CAAC;MACjE;MACA,IAAGlF,cAAc,EACjB;QACE,IAAI,CAACA,cAAc,GAAGA,cAAc,CAACvH,MAAM;QAC3C,IAAI,CAAC0M,UAAU,GAAGnF,cAAc,CAACxH,EAAE;MACrC;MACA,IAAI,CAACc,wBAAwB,CAAC8L,qBAAqB,CAACpF,cAAc,CAAC;MACnE,IAAI,CAACqF,OAAO,CAAC,IAAI,CAACrF,cAAc,CAAC;MACjC,IAAI,CAACsD,sBAAsB,EAAE;MAC7B,IAAI,CAACM,aAAa,EAAE;IACtB,CAAC,EAAE0B,KAAK,IAAE;MACR,IAAI,CAACpL,OAAO,GAAG,KAAK;MACpBqL,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;IACnD,CAAC,CAAC;EACJ;EAEAD,OAAOA,CAACI,cAAwB;IAC9B,KAAI,MAAM9H,GAAG,IAAI8H,cAAc,EAAC;MAC9B,KAAI,MAAMC,IAAI,IAAID,cAAc,CAAC9H,GAAG,CAAC,EAAC;QACpC,KAAI,MAAMgI,IAAI,IAAIF,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,EAAC;UAC1C,KAAI,MAAME,IAAI,IAAIH,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;YAChD,KAAI,MAAME,IAAI,IAAIJ,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;cACtD,KAAI,MAAME,IAAI,IAAIL,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;gBAC5D,KAAI,MAAME,IAAI,IAAIN,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;kBAClE,IAAGC,IAAI,KAAK,cAAc,IAAIN,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,KAAK,IAAI,EAAC;oBAAE;oBAC/FN,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,GAAG,GAAGP,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,GAAG,CAACtC,OAAO,CAAC,KAAK,EAAE,IAAI,CAACC,UAAU,CAAC;oBACzJ4B,OAAO,CAACC,GAAG,CAACO,IAAI,EAACN,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAAE;kBAC5E;kBACA,KAAI,MAAME,IAAI,IAAIR,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,EAAC;oBACxE,KAAI,MAAMG,IAAI,IAAIT,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,EAAC;sBAC9E,IAAGC,IAAI,KAAK,cAAc,IAAIT,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,CAACC,IAAI,CAAC,KAAK,IAAI,EAAE;wBAAE;wBAC5GT,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACF,GAAG,GAAGP,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,CAACC,IAAI,CAAC,CAACF,GAAG,CAACtC,OAAO,CAAC,KAAK,EAAE,IAAI,CAACC,UAAU,CAAC;wBACjL4B,OAAO,CAACC,GAAG,CAACU,IAAI,EAACT,cAAc,CAAC9H,GAAG,CAAC,CAAC+H,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACE,IAAI,CAAC,CAACC,IAAI,CAAC,CAAC;sBACvF;oBACF;kBACF;gBACF;cACF;YACF;UACF;QACF;MACF;IACF;EACF;EAEAzD,eAAeA,CAACpF,IAAW;IAEzB,QAAOA,IAAI;MACT,KAAK,sCAAsC;QACzC,OAAOrG,oBAAoB,CAACmP,oBAAoB;MAClD,KAAK,kCAAkC;QACrC,OAAOnP,oBAAoB,CAACoP,OAAO;MACrC,KAAK,0BAA0B;QAC7B,OAAOpP,oBAAoB,CAACqP,OAAO;MACrC,KAAK,4BAA4B;QAC/B,OAAOrP,oBAAoB,CAACsP,SAAS;MACvC,KAAK,gCAAgC;QACnC,OAAOtP,oBAAoB,CAACuP,cAAc;MAC5C,KAAK,0BAA0B;QAC7B,OAAOvP,oBAAoB,CAACwP,iBAAiB;MAC/C,KAAK,+BAA+B;QAClC,OAAOxP,oBAAoB,CAACyP,WAAW;MACzC,KAAK,2BAA2B;QAC9B,OAAOzP,oBAAoB,CAAC0P,QAAQ;MACtC,KAAK,+BAA+B;QAClC,OAAO1P,oBAAoB,CAAC2P,YAAY;MAC1C;QACE,OAAO3P,oBAAoB,CAAC4P,EAAE;IAClC;EACF;EAEAvD,iBAAiBA,CAAC1D,QAA6B;IAE7C,QAAOA,QAAQ;MACb,KAAK3I,oBAAoB,CAACmP,oBAAoB;QAC5C,OAAO,sCAAsC;MAC/C,KAAKnP,oBAAoB,CAACoP,OAAO;QACjC,OAAO,kCAAkC;MACzC,KAAKpP,oBAAoB,CAACqP,OAAO;QAC/B,OAAO,0BAA0B;MACnC,KAAKrP,oBAAoB,CAACsP,SAAS;QACjC,OAAO,4BAA4B;MACrC,KAAKtP,oBAAoB,CAACuP,cAAc;QACtC,OAAO,gCAAgC;MACzC,KAAKvP,oBAAoB,CAACwP,iBAAiB;QACzC,OAAO,0BAA0B;MACnC,KAAKxP,oBAAoB,CAACyP,WAAW;QACnC,OAAO,+BAA+B;MACxC,KAAKzP,oBAAoB,CAAC0P,QAAQ;QAChC,OAAO,2BAA2B;MACpC,KAAK1P,oBAAoB,CAAC2P,YAAY;QACpC,OAAO,+BAA+B;MACxC;QACE,OAAO,EAAE;IACb;EACF;;;uBAxnBW5N,qBAAqB,EAAA1B,EAAA,CAAAwP,iBAAA,CAAAxP,EAAA,CAAAyP,QAAA,GAAAzP,EAAA,CAAAwP,iBAAA,CAAAE,EAAA,CAAAC,kBAAA,GAAA3P,EAAA,CAAAwP,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA7P,EAAA,CAAAwP,iBAAA,CAAAM,EAAA,CAAAC,QAAA,GAAA/P,EAAA,CAAAwP,iBAAA,CAAAQ,EAAA,CAAAC,uBAAA,GAAAjQ,EAAA,CAAAwP,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAAnQ,EAAA,CAAAwP,iBAAA,CAAAY,EAAA,CAAAC,eAAA,GAAArQ,EAAA,CAAAwP,iBAAA,CAAAQ,EAAA,CAAAM,WAAA,GAAAtQ,EAAA,CAAAwP,iBAAA,CAAAe,EAAA,CAAAC,iBAAA,GAAAxQ,EAAA,CAAAwP,iBAAA,CAAAiB,EAAA,CAAAC,iBAAA,GAAA1Q,EAAA,CAAAwP,iBAAA,CAAAmB,EAAA,CAAAC,cAAA,GAAA5Q,EAAA,CAAAwP,iBAAA,CAAAqB,GAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAArBpP,qBAAqB;MAAAqP,SAAA;MAAAC,QAAA,GAAAhR,EAAA,CAAAiR,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTlCvR,EAtBA,CAAAqB,UAAA,IAAAoQ,oCAAA,iBAAqC,IAAAC,wDAAA,qCAepC,IAAAC,2DAAA,wCAMA,IAAAC,oCAAA,iBACqD;;;UAtBhC5R,EAAA,CAAAK,UAAA,SAAAmR,GAAA,CAAA3O,OAAA,CAAa;UAYhC7C,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAK,UAAA,SAAAmR,GAAA,CAAAvQ,eAAA,CAAqB;UAKrBjB,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAK,UAAA,SAAAmR,GAAA,CAAAvQ,eAAA,CAAqB;UAKNjB,EAAA,CAAAI,SAAA,EAAiC;UAAjCJ,EAAA,CAAAK,UAAA,SAAAmR,GAAA,CAAAjQ,QAAA,IAAAiQ,GAAA,CAAAvQ,eAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}