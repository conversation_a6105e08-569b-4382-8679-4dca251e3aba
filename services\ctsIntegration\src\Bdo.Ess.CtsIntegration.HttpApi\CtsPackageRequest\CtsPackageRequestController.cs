using Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests;
using Bdo.Ess.CtsIntegration.CtsPackageRequests;
using Bdo.Ess.CtsIntegration.PackageGeneration;
using Bdo.Ess.CtsIntegration.StateMachine;
using Bdo.Ess.EconomicSubstanceService.DeclarationImports;
using Bdo.Ess.Shared.Constants.Audit;
using Bdo.Ess.Shared.Constants.CtsIntegration;
using Bdo.Ess.Shared.HttpApi.Audit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using static Bdo.Ess.CtsIntegration.Permissions.CtsIntegrationPermissions;

namespace Bdo.Ess.CtsIntegration.CtsPackageRequest;

[RemoteService(Name = CtsIntegrationRemoteServiceConsts.RemoteServiceName)]
[Area(CtsIntegrationRemoteServiceConsts.RemoteServiceName)]
[Route($"api/{CtsIntegrationRemoteServiceConsts.RemoteServiceName}/CtsPackageRequest")]
[Authorize]
public class CtsPackageRequestController : CtsIntegrationController
{
    private readonly ICtsPackageRequestAppService _ctsPackageRequestAppService;
    private readonly ICtsPackageGenerationAppService _packageGenerationAppService;

    /// <summary>
    ///  Note: _auditWebInfo is scoped dependency instance,
    ///  so, it will be shared between HttpApi and AppService
    ///  Work for Auditing purpose to get client IP address.
    /// </summary>
    private readonly IAuditWebInfo _auditWebInfo;

    public CtsPackageRequestController(
        ICtsPackageRequestAppService ctsPackageRequestAppService,
        ICtsPackageGenerationAppService packageGenerationAppService,
        IAuditWebInfo auditWebInfo,
        AuditWebInfoService webInfoService)
    {
        _ctsPackageRequestAppService = ctsPackageRequestAppService;
        _packageGenerationAppService = packageGenerationAppService;
        this._auditWebInfo = auditWebInfo;
        this._auditWebInfo.IPAddress = webInfoService.GetClientIpAddress();
        this._auditWebInfo.AuditUserId = webInfoService.GetAuditUserId();
    }

    [HttpGet]
    [Route(nameof(GetAllCtsPackageRequest))]
    [Authorize(DataPacketDashboardPermissions.Default)]
    public async Task<PagedResultDto<CtsPackageRequestDto>> GetAllCtsPackageRequest([FromQuery] GetCtsPackageRequestDto input)
    {
        return await _ctsPackageRequestAppService.GetAllCtsPackageRequestAsync(input);
    }

    [HttpPost]
    [Authorize(DataPacketDashboardPermissions.Default)]
    public async Task<CtsPackageRequestDataDto> Create([FromBody] CreateCtsPackageRequestDto input)
    {
        return await _ctsPackageRequestAppService.CreateAsync(input);
    }

    [HttpGet]
    [Route("GetSummaryByYear")]
    public async Task<List<CtsPackageRequestSummaryDto>> GetSummaryByYear(string year)
    {
        return await _ctsPackageRequestAppService.GetSummaryByYear(year);
    }

    [HttpPost]
    [Route("MarkAsDoNotUpload")]
    public async Task<CtsActionResultDto> MarkAsDoNotUploadAsync(Guid packageRequestId)
    {
        return await _ctsPackageRequestAppService.MarkAsDoNotUploadAsync(packageRequestId);
    }

    [HttpPost]
    [Route("UnMarkAsDoNotUpload")]
    public async Task<CtsActionResultDto> UnMarkAsDoNotUploadAsync(Guid packageRequestId)
    {
        return await _ctsPackageRequestAppService.UnMarkAsDoNotUploadAsync(packageRequestId);
    }

    [HttpPost]
    [Route("RegeneratePackage")]
    [Authorize(RegenerateDataPacketPermissions.Default)]
    public async Task<CtsActionResultDto> RegeneratePackageAsync(Guid packageRequestId, string comments)
    {
        return await _ctsPackageRequestAppService.RegeneratePackageAsync(packageRequestId, comments);
    }

    [HttpPost]
    [Route("CheckTransmissionStatus")]
    public async Task<CtsActionResultDto> CheckTransmissionStatusAsync(Guid packageRequestId)
    {
        return await _ctsPackageRequestAppService.CheckTransmissionStatusAsync(packageRequestId);
    }


    [HttpPost]
    [Route("UploadToCts")]
    [Authorize(CTSUploadPermissions.Default)]
    public async Task<CtsActionResultDto> UploadToCtsAsync(Guid packageRequestId)
    {
        return await _ctsPackageRequestAppService.UploadToCtsAsync(packageRequestId);
    }

    [HttpPost]
    [Route("BatchUploadToCts")]
    public async Task<CtsActionResultDto> BatchUploadToCtsAsync(int fiscalYear)
    {
        return await _ctsPackageRequestAppService.BatchUploadToCtsAsync(fiscalYear);
    }

    [HttpPost("refresh-transmission-status")]
    [Authorize(RefreshStatusPermissions.Default)]
    public async Task<CtsActionResultDto> RefreshTransmissionStatusAsync()
    {
        return await _ctsPackageRequestAppService.RefreshTransmissionStatusAsync();
    }

    /// <summary>
    /// Unpacks a CTS data packet file and returns the original XML content
    /// </summary>
    /// <param name="file">The CTS data packet zip file</param>
    /// <returns>The original XML content as a file download</returns>
    [HttpPost]
    [Route("UnpackCtsPackage")]
    [Authorize(DecryptReceivedDataPacketPermissions.Default)]
    //[ApiExplorerSettings(IgnoreApi = true)]

    public async Task<IActionResult> UnpackCtsPackageAsync(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
                return BadRequest("No file uploaded or file is empty.");

            if (!file.FileName.EndsWith(".zip", StringComparison.OrdinalIgnoreCase))
                return BadRequest("Only ZIP files are supported.");

            var originalXml = await _packageGenerationAppService.UnpackAsync(file);

            // Prepend XML declaration
            var xmlDeclaration = System.Text.Encoding.UTF8.GetBytes("<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n");
            var contentWithDeclaration = new byte[xmlDeclaration.Length + originalXml.Length];
            Buffer.BlockCopy(xmlDeclaration, 0, contentWithDeclaration, 0, xmlDeclaration.Length);
            Buffer.BlockCopy(originalXml, 0, contentWithDeclaration, xmlDeclaration.Length, originalXml.Length);

            // Generate a filename for the unpacked XML
            var originalFileName = Path.GetFileNameWithoutExtension(file.FileName);
            var xmlFileName = $"{originalFileName}.xml";

            var res = new UnpackCtsPackageFileDto
            {
                FileName = xmlFileName,
                FileType = "text/xml",
                FileContent = contentWithDeclaration
            };

            return Ok(res);

        }
        catch (Exception ex)
        {
            return BadRequest($"Failed to unpack CTS package: {ex.Message}");
        }
    }

    [HttpGet]
    [Route("DownloadDataPacketFile")]
    public async Task<string> DownloadDataPacketFileAsync(Guid requestId)
    {
        return await _ctsPackageRequestAppService.DownloadDataPacketFileAsync(requestId);
    }

    //Will delete this method after QA deployment is done and Migration existing PackageRequest table data
    /*[HttpPost]
    [Route("MigrateMetaCountryCode")]
    [ApiExplorerSettings(IgnoreApi = true)]
    [AllowAnonymous]
    public async Task<string> MigrateMetaCountryCode()
    {
        return await _packageGenerationAppService.MigrateMetaCountryCode();
    }
    */
}