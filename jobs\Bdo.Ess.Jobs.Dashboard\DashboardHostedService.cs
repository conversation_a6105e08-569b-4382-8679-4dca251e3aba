﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Volo.Abp;

namespace Bdo.Ess.Jobs.Dashboard
{
    public class DashboardHostedService : IHostedService
    {
        private readonly IHostApplicationLifetime _hostApplicationLifetime;

        public DashboardHostedService(IHostApplicationLifetime hostApplicationLifetime)
        {
            _hostApplicationLifetime = hostApplicationLifetime;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            using (var application = await AbpApplicationFactory.CreateAsync<DashboardJobModule>(options =>
            {
                var builder = GetConfigurationBuilder(options.Configuration);
                options.Services.ReplaceConfiguration(builder.Build());

                options.Services.AddHttpClient("EssClient", client =>
                {
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                    client.Timeout = TimeSpan.FromHours(6); //6 hours request timeout
                })
                .ConfigurePrimaryHttpMessageHandler(() => new SocketsHttpHandler()
                {
                    ConnectTimeout = TimeSpan.FromSeconds(300), //5 minutes connection timeout
                    PooledConnectionIdleTimeout = TimeSpan.FromMinutes(30),
                    PooledConnectionLifetime = TimeSpan.FromMinutes(60)
                });
            }))
            {
                await application.InitializeAsync();

                var svc = application.ServiceProvider.GetRequiredService<DashboardProcessService>();
                await svc.RunAsync();

                await application.ShutdownAsync();

                _hostApplicationLifetime.StopApplication();
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }

        private static IConfigurationBuilder GetConfigurationBuilder(AbpConfigurationBuilderOptions options)
        {
            options ??= new AbpConfigurationBuilderOptions();

            if (options.BasePath.IsNullOrEmpty())
            {
                options.BasePath = Directory.GetCurrentDirectory();
            }

            var builder = new ConfigurationBuilder()
                .SetBasePath(options.BasePath)
                .AddJsonFile($@"appsettings.json", optional: true, reloadOnChange: true)
                .AddJsonFile($@"config/appsettings.json", optional: true, reloadOnChange: true)
                .AddJsonFile("appsettings.secrets.json", optional: true)
                .AddEnvironmentVariables();

            return builder;
        }
    }
}