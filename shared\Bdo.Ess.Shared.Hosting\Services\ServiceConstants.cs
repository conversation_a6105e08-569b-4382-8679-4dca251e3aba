﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bdo.Ess.Shared.Hosting.Services
{
	/// <summary>
	/// All service names and lists are found here.
	/// This allows us to configure our services in a single location instead of
	/// manually editing both OpenIddictDataSeeder.cs files
	/// </summary>
	public static class ServiceConstants
	{
		public const string AccountService = "AccountService";
		public const string IdentityService = "IdentityService";
		public const string AdministrationService = "AdministrationService";
		public const string SaasService = "SaasService";
		public const string EconomicSubstanceService = "EconomicSubstanceService";
		public const string CorporateEntityService = "CorporateEntityService";
		public const string LookupService = "LookupService";
		public const string SearchService = "SearchService";
		public const string AuditService = "AuditService";
		public const string DashboardService = "DashboardService";
		public const string CtsIntegrationService = "CtsIntegrationService";

		/// <summary>
		/// Add all services here
		/// </summary>
		public static string[] AllServices => new string[]
		{
			AccountService,
			IdentityService,
			AdministrationService,
			SaasService,
			EconomicSubstanceService,
			CorporateEntityService,
			LookupService,
			SearchService,
			AuditService,
			DashboardService,
			CtsIntegrationService
		};

		/// <summary>
		/// Add all public services here
		/// </summary>
		public static string[] AllPublicServices => new string[]
		{
			AccountService,
			AdministrationService,
			EconomicSubstanceService,
			CorporateEntityService,
			LookupService,
			SearchService,
			AuditService,
			DashboardService
		};

		/// <summary>
		/// Add all services required by the Angular client here
		/// </summary>
		public static string[] AngularClientServices => new string[]
		{
			AccountService,
			IdentityService,
			AdministrationService,
			SaasService,
			EconomicSubstanceService,
			CorporateEntityService,
			LookupService,
			SearchService,
			AuditService,
			DashboardService,
            CtsIntegrationService
        };

		public static Dictionary<string, string> WebGatewayScopes => new Dictionary<string, string>
		{
			{ AccountService, "Account Service API" },
			{ IdentityService, "Identity Service API" },
			{ AdministrationService, "Administration Service API" },
			{ SaasService, "Saas Service API" },
			{ EconomicSubstanceService, "Economic Substance Service API" },
			{ CorporateEntityService, "Corporate Entity Service API" },
			{ LookupService, "Lookup Service API" },
			{ SearchService, "Search Service API" },
			{ AuditService, "Audit Service API" },
			{ DashboardService, "Dashboard Service API"},
			{ CtsIntegrationService, "CTS Integration Service API" }
		};

		public static Dictionary<string, string> PublicWebGatewayScopes => new Dictionary<string, string>
		{
			{ AccountService, "Account Service API" },
			{ AdministrationService, "Administration Service API" },
			{ EconomicSubstanceService, "Economic Substance Service API" },
			{ CorporateEntityService, "Corporate Entity Service API" },
			{ LookupService, "Lookup Service API" },
			{ SearchService, "Search Service API" },
			{ AuditService, "Audit Service API"},
			{ DashboardService, "Dashboard Service API" }
		};

		//Economic Substance needs to visit the following services
		public static Dictionary<string, string> EconomicSubstanceClientScopes => new Dictionary<string, string>
		{
			{ CorporateEntityService, "Corporate Entity Service API" },
			{ LookupService, "Lookup Service API" },
			{ IdentityService, "Identity Service API" },
			{ SearchService, "Search Service API"},
			{ SaasService, "Saas Service API" }
		};

		public static Dictionary<string, string> CorporateEntityClientScopes => new Dictionary<string, string>
		{
			{ EconomicSubstanceService, "Economic Substance Service API" },
			{ SaasService, "Saas Service API" },
			{ LookupService, "Lookup Service API" },
			{ DashboardService, "Dashboard Service API" },
			{ SearchService, "Search Service API"}
		};

		public static Dictionary<string, string> AdministrationClientScopes => new Dictionary<string, string>
		{
			{ IdentityService, "Identity Service API" }
		};

		public static Dictionary<string, string> SearchClientScopes => new Dictionary<string, string>
		{
			{ SaasService, "Saas Service API" },
			{ LookupService, "Lookup Service API" }
		}; public static Dictionary<string, string> DashboardClientScopes => new Dictionary<string, string>
		{
			{ EconomicSubstanceService, "Economic Substance Service API" },
			{ LookupService, "Lookup Service API" },
			{ SaasService, "Saas Service API" },
			{ CorporateEntityService, "Corporate Entity Service API" },
			{ CtsIntegrationService, "CTS Integration Service API" }
		};

		public static Dictionary<string, string> NightlyJobClientScopes => new Dictionary<string, string>
		{
			{ EconomicSubstanceService, "Economic Substance Service API" },
			{ CorporateEntityService, "Corporate Entity Service API" },
			{ LookupService, "Lookup Service API" },
			{ DashboardService, "Dashboard Service API" },
            { CtsIntegrationService, "CTS Integration Service API" }
        };

		public static Dictionary<string, string> AuditClientScopes => new Dictionary<string, string>
		{
			{ LookupService, "Lookup Service API" },
			{ IdentityService, "Identity Service API" },
			{ SaasService, "Saas Service API" }
		};

		public static Dictionary<string, string> CtsIntegrationClientScopes => new Dictionary<string, string>
		{
			{ LookupService, "Lookup Service API" },
			{ IdentityService, "Identity Service API"},
            { EconomicSubstanceService, "Economic Substance Service API" },
        };

		public static Dictionary<string, string> RefreshCertificatesJobScopes => new Dictionary<string, string>
		{
			{ CtsIntegrationService, "CTS Integration Service API" }
		};

		public static Dictionary<string, string> RefreshTransmissionStatusJobScopes => new Dictionary<string, string>
		{
			{ CtsIntegrationService, "CTS Integration Service API" }
		};
	}
}
