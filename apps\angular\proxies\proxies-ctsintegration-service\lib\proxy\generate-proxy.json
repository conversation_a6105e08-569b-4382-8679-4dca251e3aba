{"generated": ["CtsIntegrationService"], "modules": {"abp": {"rootPath": "abp", "remoteServiceName": "abp", "controllers": {"Volo.Abp.AspNetCore.Mvc.ApiExploring.AbpApiDefinitionController": {"controllerName": "AbpApiDefinition", "controllerGroupName": "AbpApiDefinition", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Volo.Abp.AspNetCore.Mvc.ApiExploring.AbpApiDefinitionController", "interfaces": [], "actions": {"GetByModel": {"uniqueName": "GetByModel", "name": "Get", "httpMethod": "GET", "url": "api/abp/api-definition", "supportedVersions": [], "parametersOnMethod": [{"name": "model", "typeAsString": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModelRequestDto, Volo.Abp.Http", "type": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModelRequestDto", "typeSimple": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModelRequestDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "model", "name": "IncludeTypes", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "model"}], "returnValue": {"type": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel", "typeSimple": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.AspNetCore.Mvc.ApiExploring.AbpApiDefinitionController"}}}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController": {"controllerName": "AbpApplicationConfiguration", "controllerGroupName": "AbpApplicationConfiguration", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController", "interfaces": [{"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IAbpApplicationConfigurationAppService", "name": "IAbpApplicationConfigurationAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "options", "typeAsString": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions, Volo.Abp.AspNetCore.Mvc.Contracts", "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}}]}], "actions": {"GetAsyncByOptions": {"uniqueName": "GetAsyncByOptions", "name": "GetAsync", "httpMethod": "GET", "url": "api/abp/application-configuration", "supportedVersions": [], "parametersOnMethod": [{"name": "options", "typeAsString": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions, Volo.Abp.AspNetCore.Mvc.Contracts", "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "options", "name": "IncludeLocalizationResources", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "options"}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IAbpApplicationConfigurationAppService"}}}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController": {"controllerName": "AbpApplicationLocalization", "controllerGroupName": "AbpApplicationLocalization", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController", "interfaces": [{"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IAbpApplicationLocalizationAppService", "name": "IAbpApplicationLocalizationAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto, Volo.Abp.AspNetCore.Mvc.Contracts", "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}}]}], "actions": {"GetAsyncByInput": {"uniqueName": "GetAsyncByInput", "name": "GetAsync", "httpMethod": "GET", "url": "api/abp/application-localization", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto, Volo.Abp.AspNetCore.Mvc.Contracts", "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "CultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "OnlyDynamics", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IAbpApplicationLocalizationAppService"}}}}}, "CtsIntegrationService": {"rootPath": "CtsIntegrationService", "remoteServiceName": "CtsIntegrationService", "controllers": {"Bdo.Ess.CtsIntegration.BahamasCtsSettings.BahamasCtsSettingController": {"controllerName": "BahamasCtsSetting", "controllerGroupName": "BahamasCtsSetting", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Bdo.Ess.CtsIntegration.BahamasCtsSettings.BahamasCtsSettingController", "interfaces": [], "actions": {"GetAsyncById": {"uniqueName": "GetAsyncById", "name": "GetAsync", "httpMethod": "GET", "url": "api/CtsIntegrationService/bahamas-cts-settings/{id}", "supportedVersions": [], "parametersOnMethod": [{"name": "id", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "id", "name": "id", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "Bdo.Ess.CtsIntegration.BahamasCtsSettings.BahamasCtsSettingDto", "typeSimple": "Bdo.Ess.CtsIntegration.BahamasCtsSettings.BahamasCtsSettingDto"}, "allowAnonymous": null, "implementFrom": "Bdo.Ess.CtsIntegration.BahamasCtsSettings.BahamasCtsSettingController"}, "GetCurrentSettingsAsync": {"uniqueName": "GetCurrentSettingsAsync", "name": "GetCurrentSettingsAsync", "httpMethod": "GET", "url": "api/CtsIntegrationService/bahamas-cts-settings/current", "supportedVersions": [], "parametersOnMethod": [], "parameters": [], "returnValue": {"type": "Bdo.Ess.CtsIntegration.BahamasCtsSettings.BahamasCtsSettingDto", "typeSimple": "Bdo.Ess.CtsIntegration.BahamasCtsSettings.BahamasCtsSettingDto"}, "allowAnonymous": null, "implementFrom": "Bdo.Ess.CtsIntegration.BahamasCtsSettings.BahamasCtsSettingController"}}}, "Bdo.Ess.CtsIntegration.Certificate.CertificateController": {"controllerName": "Certificate", "controllerGroupName": "Certificate", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Bdo.Ess.CtsIntegration.Certificate.CertificateController", "interfaces": [], "actions": {"GetBahamasCertificateInfo": {"uniqueName": "GetBahamasCertificateInfo", "name": "GetBahamasCertificateInfo", "httpMethod": "GET", "url": "api/CtsIntegrationService/certificate/GetBahamasCertificateInfo", "supportedVersions": [], "parametersOnMethod": [], "parameters": [], "returnValue": {"type": "Bdo.Ess.CtsIntegration.Certificate.BahamasCertificateDto", "typeSimple": "Bdo.Ess.CtsIntegration.Certificate.BahamasCertificateDto"}, "allowAnonymous": null, "implementFrom": "Bdo.Ess.CtsIntegration.Certificate.CertificateController"}}}, "Bdo.Ess.CtsIntegration.Certificate.CountryCertificateController": {"controllerName": "CountryCertificate", "controllerGroupName": "CountryCertificate", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Bdo.Ess.CtsIntegration.Certificate.CountryCertificateController", "interfaces": [], "actions": {"RefreshCertificates": {"uniqueName": "RefreshCertificates", "name": "RefreshCertificates", "httpMethod": "POST", "url": "api/CtsIntegrationService/country-certificates/refresh", "supportedVersions": [], "parametersOnMethod": [], "parameters": [], "returnValue": {"type": "System.Void", "typeSimple": "System.Void"}, "allowAnonymous": null, "implementFrom": "Bdo.Ess.CtsIntegration.Certificate.CountryCertificateController"}}}, "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController": {"controllerName": "CtsPackageRequest", "controllerGroupName": "CtsPackageRequest", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController", "interfaces": [], "actions": {"GetAllCtsPackageRequestByInput": {"uniqueName": "GetAllCtsPackageRequestByInput", "name": "GetAllCtsPackageRequest", "httpMethod": "GET", "url": "api/CtsIntegrationService/CtsPackageRequest/GetAllCtsPackageRequest", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.GetCtsPackageRequestDto, Bdo.Ess.CtsIntegration.Application.Contracts", "type": "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.GetCtsPackageRequestDto", "typeSimple": "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.GetCtsPackageRequestDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "FinancialEndYear", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Query", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExchangeReason", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Query", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ReceivingCountry", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Query", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "CtsUploadStatus", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Query", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "Sorting", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Query", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "SkipCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Query", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "MaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Query", "descriptorName": "input"}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CtsPackageRequestDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CtsPackageRequestDto>"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "CreateByInput": {"uniqueName": "CreateByInput", "name": "Create", "httpMethod": "POST", "url": "api/CtsIntegrationService/CtsPackageRequest", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CreateCtsPackageRequestDto, Bdo.Ess.CtsIntegration.Application.Contracts", "type": "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CreateCtsPackageRequestDto", "typeSimple": "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CreateCtsPackageRequestDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "input", "jsonName": null, "type": "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CreateCtsPackageRequestDto", "typeSimple": "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CreateCtsPackageRequestDto", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Body", "descriptorName": ""}], "returnValue": {"type": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsPackageRequestDataDto", "typeSimple": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsPackageRequestDataDto"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "GetSummaryByYearByYear": {"uniqueName": "GetSummaryByYearByYear", "name": "GetSummaryByYear", "httpMethod": "GET", "url": "api/CtsIntegrationService/CtsPackageRequest/GetSummaryByYear", "supportedVersions": [], "parametersOnMethod": [{"name": "year", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "year", "name": "year", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "System.Collections.Generic.List<Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CtsPackageRequestSummaryDto>", "typeSimple": "[Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CtsPackageRequestSummaryDto]"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "MarkAsDoNotUploadAsyncByPackageRequestId": {"uniqueName": "MarkAsDoNotUploadAsyncByPackageRequestId", "name": "MarkAsDoNotUploadAsync", "httpMethod": "POST", "url": "api/CtsIntegrationService/CtsPackageRequest/MarkAsDoNotUpload", "supportedVersions": [], "parametersOnMethod": [{"name": "packageRequestId", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "packageRequestId", "name": "packageRequestId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto", "typeSimple": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "UnMarkAsDoNotUploadAsyncByPackageRequestId": {"uniqueName": "UnMarkAsDoNotUploadAsyncByPackageRequestId", "name": "UnMarkAsDoNotUploadAsync", "httpMethod": "POST", "url": "api/CtsIntegrationService/CtsPackageRequest/UnMarkAsDoNotUpload", "supportedVersions": [], "parametersOnMethod": [{"name": "packageRequestId", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "packageRequestId", "name": "packageRequestId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto", "typeSimple": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "RegeneratePackageAsyncByPackageRequestIdAndComments": {"uniqueName": "RegeneratePackageAsyncByPackageRequestIdAndComments", "name": "RegeneratePackageAsync", "httpMethod": "POST", "url": "api/CtsIntegrationService/CtsPackageRequest/RegeneratePackage", "supportedVersions": [], "parametersOnMethod": [{"name": "packageRequestId", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "comments", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "packageRequestId", "name": "packageRequestId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}, {"nameOnMethod": "comments", "name": "comments", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto", "typeSimple": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "CheckTransmissionStatusAsyncByPackageRequestId": {"uniqueName": "CheckTransmissionStatusAsyncByPackageRequestId", "name": "CheckTransmissionStatusAsync", "httpMethod": "POST", "url": "api/CtsIntegrationService/CtsPackageRequest/CheckTransmissionStatus", "supportedVersions": [], "parametersOnMethod": [{"name": "packageRequestId", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "packageRequestId", "name": "packageRequestId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto", "typeSimple": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "UploadToCtsAsyncByPackageRequestId": {"uniqueName": "UploadToCtsAsyncByPackageRequestId", "name": "UploadToCtsAsync", "httpMethod": "POST", "url": "api/CtsIntegrationService/CtsPackageRequest/UploadToCts", "supportedVersions": [], "parametersOnMethod": [{"name": "packageRequestId", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "packageRequestId", "name": "packageRequestId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto", "typeSimple": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "BatchUploadToCtsAsyncByFiscalYear": {"uniqueName": "BatchUploadToCtsAsyncByFiscalYear", "name": "BatchUploadToCtsAsync", "httpMethod": "POST", "url": "api/CtsIntegrationService/CtsPackageRequest/BatchUploadToCts", "supportedVersions": [], "parametersOnMethod": [{"name": "fiscalYear", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "fiscalYear", "name": "fiscalYear", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto", "typeSimple": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "RefreshTransmissionStatusAsync": {"uniqueName": "RefreshTransmissionStatusAsync", "name": "RefreshTransmissionStatusAsync", "httpMethod": "POST", "url": "api/CtsIntegrationService/CtsPackageRequest/refresh-transmission-status", "supportedVersions": [], "parametersOnMethod": [], "parameters": [], "returnValue": {"type": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto", "typeSimple": "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}, "DownloadDataPacketFileAsyncByRequestId": {"uniqueName": "DownloadDataPacketFileAsyncByRequestId", "name": "DownloadDataPacketFileAsync", "httpMethod": "GET", "url": "api/CtsIntegrationService/CtsPackageRequest/DownloadDataPacketFile", "supportedVersions": [], "parametersOnMethod": [{"name": "requestId", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "requestId", "name": "requestId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "System.String", "typeSimple": "string"}, "allowAnonymous": false, "implementFrom": "Bdo.Ess.CtsIntegration.CtsPackageRequest.CtsPackageRequestController"}}}, "Bdo.Ess.CtsIntegration.TestApi.CTSIntegrationTestApiController": {"controllerName": "CTSIntegrationTestApi", "controllerGroupName": "CTSIntegrationTestApi", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Bdo.Ess.CtsIntegration.TestApi.CTSIntegrationTestApiController", "interfaces": [{"type": "Bdo.Ess.CtsIntegration.TestApi.ICtsIntegrationTestAppService", "name": "ICtsIntegrationTestAppService", "methods": [{"name": "TestConnectionAsync", "parametersOnMethod": [{"name": "message", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.String", "typeSimple": "string"}}, {"name": "GetAllCountriesOrderedAsync", "parametersOnMethod": [], "returnValue": {"type": "System.Collections.Generic.List<Bdo.Ess.LookupService.Declaration.CountryDto>", "typeSimple": "[Bdo.Ess.LookupService.Declaration.CountryDto]"}}, {"name": "FindByEmailAsync", "parametersOnMethod": [{"name": "email", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Identity.IdentityUserDto", "typeSimple": "Volo.Abp.Identity.IdentityUserDto"}}]}], "actions": {"TestConnectionAsyncByMessage": {"uniqueName": "TestConnectionAsyncByMessage", "name": "TestConnectionAsync", "httpMethod": "GET", "url": "api/CtsIntegrationService/test/connection", "supportedVersions": [], "parametersOnMethod": [{"name": "message", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "message", "name": "message", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "System.String", "typeSimple": "string"}, "allowAnonymous": null, "implementFrom": "Bdo.Ess.CtsIntegration.TestApi.ICtsIntegrationTestAppService"}, "GetAllCountriesOrderedAsync": {"uniqueName": "GetAllCountriesOrderedAsync", "name": "GetAllCountriesOrderedAsync", "httpMethod": "GET", "url": "api/CtsIntegrationService/test/countries", "supportedVersions": [], "parametersOnMethod": [], "parameters": [], "returnValue": {"type": "System.Collections.Generic.List<Bdo.Ess.LookupService.Declaration.CountryDto>", "typeSimple": "[Bdo.Ess.LookupService.Declaration.CountryDto]"}, "allowAnonymous": null, "implementFrom": "Bdo.Ess.CtsIntegration.TestApi.ICtsIntegrationTestAppService"}, "FindByEmailAsyncByEmail": {"uniqueName": "FindByEmailAsyncByEmail", "name": "FindByEmailAsync", "httpMethod": "GET", "url": "api/CtsIntegrationService/test/find-by-email", "supportedVersions": [], "parametersOnMethod": [{"name": "email", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "email", "name": "email", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "Volo.Abp.Identity.IdentityUserDto", "typeSimple": "Volo.Abp.Identity.IdentityUserDto"}, "allowAnonymous": null, "implementFrom": "Bdo.Ess.CtsIntegration.TestApi.ICtsIntegrationTestAppService"}, "TestUploadSftpAsyncByZipFileNameAndZipFileUrlAndReceivingCountryCode": {"uniqueName": "TestUploadSftpAsyncByZipFileNameAndZipFileUrlAndReceivingCountryCode", "name": "TestUploadSftpAsync", "httpMethod": "POST", "url": "api/CtsIntegrationService/test/test-upload-sftp/{zipFileName}", "supportedVersions": [], "parametersOnMethod": [{"name": "zipFileName", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "zipFileUrl", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "receivingCountryCode", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "zipFileName", "name": "zipFileName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}, {"nameOnMethod": "zipFileUrl", "name": "zipFileUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}, {"nameOnMethod": "receivingCountryCode", "name": "receivingCountryCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "System.String", "typeSimple": "string"}, "allowAnonymous": true, "implementFrom": "Bdo.Ess.CtsIntegration.TestApi.CTSIntegrationTestApiController"}}}}}}, "types": {"Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CreateCtsPackageRequestDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Guid>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "EssInformationXmlId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsPackageFileName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ReceiverCountryCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MetaCountryCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FiscalYear", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ExchangeReason", "jsonName": null, "type": "Bdo.Ess.Shared.Constants.InformationExchanges.ExchangeReason?", "typeSimple": "Bdo.Ess.Shared.Constants.InformationExchanges.ExchangeReason?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "XmlPayload", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "XmlPayloadUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "PackageZipUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileCreatedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UploadedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UploadAttempts", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UploadStatus", "jsonName": null, "type": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "typeSimple": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "EligibleCheckCtsStatus", "jsonName": null, "type": "System.Boolean?", "typeSimple": "boolean?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TransmissionStatus", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsExcludeCtsUpload", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "StatusUpdatedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "HasExchangeRecords", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TransmissionStatusDesc", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsTransmissionId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TransmissionStatusLastCheckedUtc", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TenantId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MessageRefId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CtsPackageCommentDto": {"baseType": "Volo.Abp.Application.Dtos.AuditedEntityDto<System.Guid>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "CtsPackageId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Comment", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsUploadStatus", "jsonName": null, "type": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "typeSimple": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TransmissionStatus", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsUploadStatusDesc", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CreatorName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CtsPackageRequestDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Guid>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "ExchangeReason", "jsonName": null, "type": "Bdo.Ess.Shared.Constants.InformationExchanges.ExchangeReason?", "typeSimple": "Bdo.Ess.Shared.Constants.InformationExchanges.ExchangeReason?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsPackageId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DataPacket", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FinancialPeriodEndYear", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileCreationDate", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ReceivingCountry", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsUploadStatus", "jsonName": null, "type": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "typeSimple": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsUploadStatusSortValue", "jsonName": null, "type": "System.Decimal", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UploadedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsTransmissionStatus", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ViewExchangeRecords", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ViewComments", "jsonName": null, "type": "[Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CtsPackageCommentDto]", "typeSimple": "[Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CtsPackageCommentDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "RegeneratePacket", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsUpload", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ExcludeFromCtsUpload", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "AllowedActions", "jsonName": null, "type": "[Bdo.Ess.CtsIntegration.Constants.DataPacketAction]", "typeSimple": "[Bdo.Ess.CtsIntegration.Constants.DataPacketAction]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.CtsPackageRequestSummaryDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TotalNotUploaded", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TotalReadyForUpload", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TotalFailedUpload", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TotalUploadedToCTS", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TotalNotEnrolled", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Year", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests.GetCtsPackageRequestDto": {"baseType": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "FinancialEndYear", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ExchangeReason", "jsonName": null, "type": "Bdo.Ess.Shared.Constants.InformationExchanges.ExchangeReason?", "typeSimple": "Bdo.Ess.Shared.Constants.InformationExchanges.ExchangeReason?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ReceivingCountry", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsUploadStatus", "jsonName": null, "type": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "typeSimple": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.CtsIntegration.BahamasCtsSettings.BahamasCtsSettingDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Guid>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "SystemUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SystemUserPassword", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SftpUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SftpSSHKey", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsPublicCertificate", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsPublicCertificateUpdatedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SystemUserPasswordUpdatedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SftpSSHKeyUpdatedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.CtsIntegration.Certificate.BahamasCertificateDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Guid>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "PublicKey", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CertificateContent", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CertificateContentType", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CertificateFileName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CertificatePassword", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ExpiredAt", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ValidFrom", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsActive", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.CtsIntegration.Constants.DataPacketAction": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["Regenerate", "Upload", "MarkDoNotUpload", "UnmarkDoNotUpload", "CheckTransmissionStatus"], "enumValues": [0, 1, 2, 3, 4], "genericArguments": null, "properties": null}, "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsActionResultDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Errors", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Success", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Message", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "AvailableActions", "jsonName": null, "type": "[Bdo.Ess.CtsIntegration.Constants.DataPacketAction]", "typeSimple": "[Bdo.Ess.CtsIntegration.Constants.DataPacketAction]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.CtsIntegration.CtsPackageRequests.CtsPackageRequestDataDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Guid>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "EssInformationXmlId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CtsPackageFileName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ReceiverCountryCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MetaCountryCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FiscalYear", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ExchangeReason", "jsonName": null, "type": "Bdo.Ess.Shared.Constants.InformationExchanges.ExchangeReason?", "typeSimple": "Bdo.Ess.Shared.Constants.InformationExchanges.ExchangeReason?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "XmlPayload", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "XmlPayloadUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "PackageZipUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileCreatedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UploadedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UploadStatus", "jsonName": null, "type": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "typeSimple": "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TransmissionStatus", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsExcludeCtsUpload", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "StatusUpdatedAt", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "HasExchangeRecords", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TenantId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MessageRefId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.CtsIntegration.enums.CTSUploadStatus": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["GenerationInProgress", "ReceivingCountryNotEnrolled", "DataPacketGenerationFailed", "DoNotUpload", "NotStarted", "Uploading", "Uploaded", "UploadFailed"], "enumValues": [0, 1, 2, 3, 4, 5, 6, 7], "genericArguments": null, "properties": null}, "Bdo.Ess.LookupService.Declaration.CountryDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Guid>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Code", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Code2", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsCoop", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Bdo.Ess.Shared.Constants.InformationExchanges.ExchangeReason": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["NonCompliance", "HighRisk", "NonResidence", "OtherCases"], "enumValues": [0, 1, 2, 3], "genericArguments": null, "properties": null}, "System.Nullable<T0>": {"baseType": "System.ValueType", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["T"], "properties": [{"name": "HasValue", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Value", "jsonName": null, "type": "T", "typeSimple": "T", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.AuditedEntityDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.CreationAuditedEntityDto<TPrimaryKey>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["TPrimaryKey"], "properties": [{"name": "LastModificationTime", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LastModifierId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.CreationAuditedEntityDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<TPrimaryKey>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["TPrimaryKey"], "properties": [{"name": "CreationTime", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CreatorId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.EntityDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": []}, "Volo.Abp.Application.Dtos.EntityDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.EntityDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["<PERSON><PERSON><PERSON>"], "properties": [{"name": "Id", "jsonName": null, "type": "<PERSON><PERSON><PERSON>", "typeSimple": "<PERSON><PERSON><PERSON>", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.ExtensibleAuditedEntityDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.ExtensibleCreationAuditedEntityDto<TPrimaryKey>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["TPrimaryKey"], "properties": [{"name": "LastModificationTime", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LastModifierId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.ExtensibleCreationAuditedEntityDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.ExtensibleEntityDto<TPrimaryKey>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["TPrimaryKey"], "properties": [{"name": "CreationTime", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CreatorId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.ExtensibleEntityDto<T0>": {"baseType": "Volo.Abp.ObjectExtending.ExtensibleObject", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["<PERSON><PERSON><PERSON>"], "properties": [{"name": "Id", "jsonName": null, "type": "<PERSON><PERSON><PERSON>", "typeSimple": "<PERSON><PERSON><PERSON>", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.ExtensibleFullAuditedEntityDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.ExtensibleAuditedEntityDto<TPrimaryKey>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["TPrimaryKey"], "properties": [{"name": "IsDeleted", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DeleterId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DeletionTime", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.LimitedResultRequestDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "DefaultMaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MaxMaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": "1", "maximum": "2147483647", "regex": null}]}, "Volo.Abp.Application.Dtos.ListResultDto<T0>": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["T"], "properties": [{"name": "Items", "jsonName": null, "type": "[T]", "typeSimple": "[T]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto": {"baseType": "Volo.Abp.Application.Dtos.PagedResultRequestDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Sorting", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.PagedResultDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.ListResultDto<T>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["T"], "properties": [{"name": "TotalCount", "jsonName": null, "type": "System.Int64", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.PagedResultRequestDto": {"baseType": "Volo.Abp.Application.Dtos.LimitedResultRequestDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "SkipCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": "0", "maximum": "2147483647", "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "GrantedPolicies", "jsonName": null, "type": "{System.String:<PERSON>.Boolean}", "typeSimple": "{string:boolean}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Localization", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON>", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Setting", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CurrentUser", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Features", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "GlobalFeatures", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MultiTenancy", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CurrentTenant", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Timing", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Clock", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ObjectExtensions", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ExtraProperties", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IncludeLocalizationResources", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Values", "jsonName": null, "type": "{System.String:System.String}", "typeSimple": "{string:string}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "EnabledFeatures", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Values", "jsonName": null, "type": "{System.String:System.Collections.Generic.Dictionary<System.String,System.String>}", "typeSimple": "{string:System.Collections.Generic.Dictionary<string,string>}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Resources", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Languages", "jsonName": null, "type": "[Volo.Abp.Localization.LanguageInfo]", "typeSimple": "[Volo.Abp.Localization.LanguageInfo]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CurrentCulture", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultResourceName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LanguagesMap", "jsonName": null, "type": "{System.String:[Volo.Abp.NameValue]}", "typeSimple": "{string:[Volo.Abp.NameValue]}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LanguageFilesMap", "jsonName": null, "type": "{System.String:[Volo.Abp.NameValue]}", "typeSimple": "{string:[Volo.Abp.NameValue]}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Resources", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CurrentCulture", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "CultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": true, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnlyDynamics", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Texts", "jsonName": null, "type": "{System.String:System.String}", "typeSimple": "{string:string}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "BaseResources", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Values", "jsonName": null, "type": "{System.String:System.String}", "typeSimple": "{string:string}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Kind", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "DisplayName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "EnglishName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ThreeLetterIsoLanguageName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TwoLetterIsoLanguageName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsRightToLeft", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "NativeName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DateTimeFormat", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsAuthenticated", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Id", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TenantId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImpersonatorUserId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImpersonatorTenantId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImpersonatorUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImpersonatorTenantName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SurName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Email", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "EmailVerified", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "PhoneNumber", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "PhoneNumberVerified", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Roles", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SessionId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "CalendarAlgorithmType", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DateTimeFormatLong", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ShortDatePattern", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FullDateTimePattern", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DateSeparator", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ShortTimePattern", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LongTimePattern", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TimeZoneName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Properties", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Configuration", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Fields", "jsonName": null, "type": "[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto]", "typeSimple": "[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LocalizationResource", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Value", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsAvailable", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "OnGet", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnCreate", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnUpdate", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsAvailable", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsAvailable", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Config", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DisplayName", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Api", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Ui", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Attributes", "jsonName": null, "type": "[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto]", "typeSimple": "[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Configuration", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultValue", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "OnTable", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnCreateForm", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnEditForm", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Lookup", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsVisible", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Url", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ResultListPropertyName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DisplayPropertyName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ValuePropertyName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FilterParamName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsVisible", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Resource", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Entities", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Configuration", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "<PERSON><PERSON><PERSON>", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Enums", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "<PERSON><PERSON>", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Windows", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TimeZone", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TimeZoneId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsAvailable", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsEnabled", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ActionApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "UniqueName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "HttpMethod", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Url", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SupportedVersions", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ParametersOnMethod", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Parameters", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.ParameterApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.ParameterApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ReturnValue", "jsonName": null, "type": "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel", "typeSimple": "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "AllowAnonymous", "jsonName": null, "type": "System.Boolean?", "typeSimple": "boolean?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImplementFrom", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "<PERSON><PERSON><PERSON>", "jsonName": null, "type": "{System.String:Volo.Abp.Http.Modeling.ModuleApiDescriptionModel}", "typeSimple": "{string:Volo.Abp.Http.Modeling.ModuleApiDescriptionModel}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Types", "jsonName": null, "type": "{System.String:Volo.Abp.Http.Modeling.TypeApiDescriptionModel}", "typeSimple": "{string:Volo.Abp.Http.Modeling.TypeApiDescriptionModel}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModelRequestDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IncludeTypes", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ControllerApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "ControllerName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ControllerGroupName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsRemoteService", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsIntegrationService", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ApiVersion", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Interfaces", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Actions", "jsonName": null, "type": "{System.String:Volo.Abp.Http.Modeling.ActionApiDescriptionModel}", "typeSimple": "{string:Volo.Abp.Http.Modeling.ActionApiDescriptionModel}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Methods", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ParametersOnMethod", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ReturnValue", "jsonName": null, "type": "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel", "typeSimple": "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeAsString", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsOptional", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultValue", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ModuleApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "RootPath", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "RemoteServiceName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Controllers", "jsonName": null, "type": "{System.String:Volo.Abp.Http.Modeling.ControllerApiDescriptionModel}", "typeSimple": "{string:Volo.Abp.Http.Modeling.ControllerApiDescriptionModel}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ParameterApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "NameOnMethod", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "JsonName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsOptional", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultValue", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ConstraintTypes", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "BindingSourceId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DescriptorName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.PropertyApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "JsonName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsRequired", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Minimum", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Maximum", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Regex", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.TypeApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "BaseType", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsEnum", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "EnumNames", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsonName": null, "type": "[System.Object]", "typeSimple": "[object]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "GenericArguments", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Properties", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.PropertyApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.PropertyApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Identity.IdentityUserDto": {"baseType": "Volo.Abp.Application.Dtos.ExtensibleFullAuditedEntityDto<System.Guid>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TenantId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Email", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Surname", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "EmailConfirmed", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "PhoneNumber", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "PhoneNumberConfirmed", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SupportTwoFactor", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TwoFactorEnabled", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsActive", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LockoutEnabled", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsLockedOut", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LockoutEnd", "jsonName": null, "type": "System.DateTimeOffset?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ShouldChangePasswordOnNextLogin", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ConcurrencyStamp", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "RoleNames", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "AccessFailedCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LastPasswordChangeTime", "jsonName": null, "type": "System.DateTimeOffset?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsExternal", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Localization.LanguageInfo": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "CultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UiCultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DisplayName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TwoLetterISOLanguageName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.NameValue": {"baseType": "Volo.Abp.NameValue<System.String>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": []}, "Volo.Abp.NameValue<T0>": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["T"], "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Value", "jsonName": null, "type": "T", "typeSimple": "T", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.ObjectExtending.ExtensibleObject": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "ExtraProperties", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}}}