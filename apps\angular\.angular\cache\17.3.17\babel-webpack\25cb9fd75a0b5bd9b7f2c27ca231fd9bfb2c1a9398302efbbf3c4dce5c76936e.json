{"ast": null, "code": "export class BdoTableData {\n  constructor(tableId, data, totalRecords, resetToFirstPage = false) {\n    this.tableId = tableId;\n    this.data = data;\n    this.totalRecords = totalRecords;\n    this.resetToFirstPage = resetToFirstPage;\n  }\n}\nexport class BdoTableRowClickEvent {}\nexport class BdoTableCellLinkClickEvent {}\nexport class BdoTableRowActionClickEvent {}\nexport class BdoTableLazyLoadEvent {}\nexport class BdoTableRowExpandedEvent {}\nexport class BdoTableCheckboxClickEvent {\n  constructor(selectedRows, rowId, isChecked, isSelectAll) {\n    this.selectedRows = selectedRows, this.rowId = rowId, this.isChecked = isChecked, this.isSelectAll = isSelectAll;\n  }\n}\nexport var BdoTableColumnType;\n(function (BdoTableColumnType) {\n  BdoTableColumnType[BdoTableColumnType[\"String\"] = 0] = \"String\";\n  BdoTableColumnType[BdoTableColumnType[\"Boolean\"] = 1] = \"Boolean\";\n  BdoTableColumnType[BdoTableColumnType[\"YesNo\"] = 2] = \"YesNo\";\n  BdoTableColumnType[BdoTableColumnType[\"Html\"] = 3] = \"Html\";\n  BdoTableColumnType[BdoTableColumnType[\"Array\"] = 4] = \"Array\";\n  BdoTableColumnType[BdoTableColumnType[\"Link\"] = 5] = \"Link\";\n  BdoTableColumnType[BdoTableColumnType[\"Number\"] = 6] = \"Number\";\n  BdoTableColumnType[BdoTableColumnType[\"Date\"] = 7] = \"Date\";\n  BdoTableColumnType[BdoTableColumnType[\"Currency\"] = 8] = \"Currency\";\n  BdoTableColumnType[BdoTableColumnType[\"Actions\"] = 9] = \"Actions\";\n  BdoTableColumnType[BdoTableColumnType[\"Checkbox\"] = 10] = \"Checkbox\";\n  BdoTableColumnType[BdoTableColumnType[\"FileSize\"] = 11] = \"FileSize\";\n  BdoTableColumnType[BdoTableColumnType[\"DateTime\"] = 12] = \"DateTime\";\n  BdoTableColumnType[BdoTableColumnType[\"ThreeDotActions\"] = 13] = \"ThreeDotActions\";\n  BdoTableColumnType[BdoTableColumnType[\"LinkArray\"] = 14] = \"LinkArray\";\n  BdoTableColumnType[BdoTableColumnType[\"SingleActionButton\"] = 15] = \"SingleActionButton\";\n})(BdoTableColumnType || (BdoTableColumnType = {}));\n/**\n * An object used to get page information from the server\n */\nexport class BdoTablePage {\n  constructor() {\n    // The number of elements in the page\n    this.size = 0;\n    // The total number of elements\n    this.totalElements = 0;\n    // The total number of pages\n    this.totalPages = 0;\n    // The current page number\n    this.pageNumber = 0;\n  }\n}", "map": {"version": 3, "names": ["BdoTableData", "constructor", "tableId", "data", "totalRecords", "resetToFirstPage", "BdoTableRowClickEvent", "BdoTableCellLinkClickEvent", "BdoTableRowActionClickEvent", "BdoTableLazyLoadEvent", "BdoTableRowExpandedEvent", "BdoTableCheckboxClickEvent", "selectedRows", "rowId", "isChecked", "isSelectAll", "BdoTableColumnType", "BdoTablePage", "size", "totalElements", "totalPages", "pageNumber"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\shared\\components\\bdo-table\\bdo-table.model.ts"], "sourcesContent": ["export interface BdoTableColumnDefinition {\r\n    columnId: string;\r\n    columnName: string;\r\n    isSortable?: boolean;\r\n    sortColumnId?: string;\r\n\r\n    width?: number; // in pixels\r\n    minWidth?: number; // in pixels\r\n    maxWidth?: number; // in pixels\r\n\r\n    //used if column mode = 'flex'\r\n    flexGrow?: number;\r\n\r\n    frozenLeft?: boolean;\r\n    frozenRight?: boolean;\r\n\r\n    type: BdoTableColumnType;\r\n\r\n    //custom css class applied to column header\r\n    class?: string;\r\n\r\n    matHeaderClass?: string; // custom css class applied to mat header cell\r\n}\r\n\r\nexport class BdoTableData {\r\n    constructor(tableId?: string, data?: BdoTableRowData[], totalRecords?: number, resetToFirstPage = false) {\r\n        this.tableId = tableId;\r\n        this.data = data;\r\n        this.totalRecords = totalRecords;\r\n        this.resetToFirstPage = resetToFirstPage;\r\n    }\r\n\r\n    public tableId: string | undefined;\r\n    public data: BdoTableRowData[] | undefined;\r\n    public totalRecords: number | undefined;\r\n\r\n    //data for the footer row\r\n    public hasFooter: boolean;\r\n    public footerCells: BdoTableCellData[];\r\n\r\n    public resetToFirstPage: boolean;\r\n}\r\n\r\nexport interface BdoTableRowData {\r\n    id: string;\r\n    class?: string; // css class applied on row level\r\n    cells: BdoTableCellData[];\r\n    searchableText?: string; //text searchable by global filter\r\n    height?: number; //can define height of row manually\r\n    rawData?: any; //pass raw data to be used later\r\n    cannotCheck?: boolean; // indicates whether you cannot check this row for selection\r\n    checked?: boolean; // indicates if the row is selected\r\n    hideExpand?: boolean; //row is not expandable so hide he icon to expand/collapse\r\n}\r\n\r\nexport interface BdoTableCellData {\r\n    class?: string; // css class applied on cell level\r\n    columnId: string;\r\n    value: any;\r\n    hide?: boolean;\r\n\r\n    //used for columns that are conditonally link or text\r\n    //for columns that are 'isLink=true', set this property to true to display as text only, not a link\r\n    isTextInsteadOfLink?: boolean;\r\n}\r\n\r\nexport interface BdoTableActionsRowData {\r\n    actionType: string;\r\n    tooltip?: string;\r\n    icon?: string;\r\n    displayName?: string;\r\n    source? :string;\r\n}\r\n\r\nexport class BdoTableRowClickEvent {\r\n    public id: string;\r\n    public rowData: BdoTableRowData;\r\n}\r\n\r\nexport class BdoTableCellLinkClickEvent {\r\n    public id: string;\r\n    public columnId: string;\r\n    public rawData: any;\r\n    public event?: any; // click event\r\n    public arrayIndex?: number;\r\n}\r\n\r\nexport class BdoTableRowActionClickEvent {\r\n    public action: string;\r\n    public id: string;\r\n    public data?: BdoTableRowData;\r\n    public event?: any; // click event\r\n}\r\n\r\nexport class BdoTableLazyLoadEvent {\r\n    public pageNumber: number;\r\n    public pageSize: number;\r\n    public sortField: string;\r\n    public isAscending: boolean;\r\n}\r\n\r\nexport class BdoTableRowExpandedEvent {\r\n    public id: string;\r\n    public rawData: any;\r\n}\r\n\r\nexport class BdoTableCheckboxClickEvent {\r\n    constructor(\r\n        selectedRows: BdoTableRowData[],\r\n        rowId: string,\r\n        isChecked: boolean,\r\n        isSelectAll: boolean) {\r\n            this.selectedRows = selectedRows,\r\n            this.rowId = rowId,\r\n            this.isChecked = isChecked,\r\n            this.isSelectAll = isSelectAll\r\n    }\r\n    public rowId: string;\r\n    public selectedRows: BdoTableRowData[];\r\n\r\n    //type of event: check or uncheck\r\n    public isChecked: boolean;\r\n    public isSelectAll: boolean;\r\n}\r\n\r\nexport enum BdoTableColumnType {\r\n    String = 0,\r\n    Boolean = 1,\r\n    YesNo = 2,\r\n    Html = 3,\r\n    Array = 4,\r\n    Link = 5,\r\n    Number = 6,\r\n    Date = 7,\r\n    Currency = 8,\r\n    Actions = 9,\r\n    Checkbox = 10,\r\n    FileSize = 11,\r\n    DateTime = 12,\r\n    ThreeDotActions = 13,\r\n    LinkArray = 14,\r\n    SingleActionButton = 15\r\n}\r\n\r\nexport interface BdoTablePageInfo {\r\n    offset: number;\r\n    pageSize: number;\r\n    limit: number;\r\n    count: number;\r\n}\r\n\r\n/**\r\n * An object used to get page information from the server\r\n */\r\nexport class BdoTablePage {\r\n    // The number of elements in the page\r\n    size = 0;\r\n    // The total number of elements\r\n    totalElements = 0;\r\n    // The total number of pages\r\n    totalPages = 0;\r\n    // The current page number\r\n    pageNumber = 0;\r\n}\r\n"], "mappings": "AAwBA,OAAM,MAAOA,YAAY;EACrBC,YAAYC,OAAgB,EAAEC,IAAwB,EAAEC,YAAqB,EAAEC,gBAAgB,GAAG,KAAK;IACnG,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;;AA4CJ,OAAM,MAAOC,qBAAqB;AAKlC,OAAM,MAAOC,0BAA0B;AAQvC,OAAM,MAAOC,2BAA2B;AAOxC,OAAM,MAAOC,qBAAqB;AAOlC,OAAM,MAAOC,wBAAwB;AAKrC,OAAM,MAAOC,0BAA0B;EACnCV,YACIW,YAA+B,EAC/BC,KAAa,EACbC,SAAkB,EAClBC,WAAoB;IAChB,IAAI,CAACH,YAAY,GAAGA,YAAY,EAChC,IAAI,CAACC,KAAK,GAAGA,KAAK,EAClB,IAAI,CAACC,SAAS,GAAGA,SAAS,EAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;EACtC;;AASJ,WAAYC,kBAiBX;AAjBD,WAAYA,kBAAkB;EAC1BA,kBAAA,CAAAA,kBAAA,0BAAU;EACVA,kBAAA,CAAAA,kBAAA,4BAAW;EACXA,kBAAA,CAAAA,kBAAA,wBAAS;EACTA,kBAAA,CAAAA,kBAAA,sBAAQ;EACRA,kBAAA,CAAAA,kBAAA,wBAAS;EACTA,kBAAA,CAAAA,kBAAA,sBAAQ;EACRA,kBAAA,CAAAA,kBAAA,0BAAU;EACVA,kBAAA,CAAAA,kBAAA,sBAAQ;EACRA,kBAAA,CAAAA,kBAAA,8BAAY;EACZA,kBAAA,CAAAA,kBAAA,4BAAW;EACXA,kBAAA,CAAAA,kBAAA,+BAAa;EACbA,kBAAA,CAAAA,kBAAA,+BAAa;EACbA,kBAAA,CAAAA,kBAAA,+BAAa;EACbA,kBAAA,CAAAA,kBAAA,6CAAoB;EACpBA,kBAAA,CAAAA,kBAAA,iCAAc;EACdA,kBAAA,CAAAA,kBAAA,mDAAuB;AAC3B,CAAC,EAjBWA,kBAAkB,KAAlBA,kBAAkB;AA0B9B;;;AAGA,OAAM,MAAOC,YAAY;EAAzBhB,YAAA;IACI;IACA,KAAAiB,IAAI,GAAG,CAAC;IACR;IACA,KAAAC,aAAa,GAAG,CAAC;IACjB;IACA,KAAAC,UAAU,GAAG,CAAC;IACd;IACA,KAAAC,UAAU,GAAG,CAAC;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}