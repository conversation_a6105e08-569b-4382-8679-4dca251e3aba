﻿using Bdo.Ess.CorporateEntityService.CorporateEntities;
using Bdo.Ess.CorporateEntityService.Encryption;
using Bdo.Ess.SearchService.ElasticSearch;
using Bdo.Ess.Shared.Hosting.Microservices.Eto.CorporateEntity;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;

namespace Bdo.Ess.CorporateEntityService.Integration
{
    public class EntitySearchHelperAppService : CorporateEntityServiceAppService, IEntitySearchHelperAppService
    {
        private readonly ICorporateEntityRepository _corporateEntityRepository;
        private readonly ICurrentTenant _currentTenant;
        private readonly IDataFilter _datafilter;
        private readonly ILogger<EntitySearchHelperAppService> _logger;
        private readonly ISearchHelperService _searchHelperService;
        private readonly ICorporateEntityEncryptionManager _entityEncryptionManager;

        public EntitySearchHelperAppService(
            ICorporateEntityRepository corporateEntityRepository,
            ICurrentTenant currentTenant,
            IDistributedEventBus distributedEventBus,
            IDataFilter datafilter,
            ILogger<EntitySearchHelperAppService> logger,
            ISearchHelperService searchHelperService,
            ICorporateEntityEncryptionManager entityEncryptionManager
        ) {
            _corporateEntityRepository = corporateEntityRepository;
            _currentTenant = currentTenant;
            _datafilter = datafilter;
            _logger = logger;
            _searchHelperService = searchHelperService;
            _entityEncryptionManager = entityEncryptionManager;
        }

        public async Task<bool> ReIndexSearchAllTenants(int pageSize = 1000)
        {
            try
            {
                using (_datafilter.Disable<IMultiTenant>()) 
                {
                    using(_datafilter.Disable<ISoftDelete>())
                    {
                        var totalCount = await _corporateEntityRepository.GetCountAsync();
                        _logger.LogInformation("Total Entity Count: {0}", totalCount);
                        
                        var currentSkip = 0;
                        var totalProcessed = 0;
                        
                        while (currentSkip < totalCount)
                        {
                            var page = await _corporateEntityRepository.GetPagedListAsync(currentSkip, pageSize, "");

                            if (page?.Count > 0)
                            {
                                var entityList = ObjectMapper.Map<List<CorporateEntity>, List<CorporateEntityCreatedEto>>(page);
                                await _searchHelperService.IndexManyEntities(entityList);
                                totalProcessed += page.Count;
                            }

                            _logger.LogInformation("Total Entities Re-Indexed: {0}", totalProcessed);

                            currentSkip += pageSize;
                        }
                        
                        _logger.LogInformation("Re-indexing completed. Total entities processed: {0}", totalProcessed);
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during re-indexing all tenants");
                return false;
            }
        }

        public async Task<bool> ReIndexSearchByTenant(Guid tenantId, int pageSize=1000)
        {
            try
            {
                using(_currentTenant.Change(tenantId))
                {
                    var totalCount = await _corporateEntityRepository.GetCountAsync();
                    _logger.LogInformation("Total Entity Count for tenant {0}: {1}", tenantId, totalCount);

                    var currentSkip = 0;
                    var totalProcessed = 0;

                    while (currentSkip < totalCount)
                    {
                        var page = await _corporateEntityRepository.GetPagedListAsync(currentSkip, pageSize, "");
                        
                        if (page?.Count > 0)
                        {
                            var updatedList = ObjectMapper.Map<List<CorporateEntity>, List<CorporateEntityCreatedEto>>(page);
                            await _searchHelperService.IndexManyEntities(updatedList);
                            totalProcessed += page.Count;
                        }

                        _logger.LogInformation("Total Entities Re-Indexed for tenant {0}: {1}", tenantId, totalProcessed);

                        currentSkip += pageSize;
                    }
                    
                    _logger.LogInformation("Re-indexing completed for tenant {0}. Total entities processed: {1}", tenantId, totalProcessed);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during re-indexing for tenant {0}", tenantId);
                return false;
            }
        }
    }
}
